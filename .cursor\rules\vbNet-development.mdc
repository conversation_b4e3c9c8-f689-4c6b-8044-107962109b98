### Rule: VB.NET Framework 4.7.2 Compliance
- **Instruction**: |
    All code must strictly follow VB.NET Framework 4.7.2 conventions:
    1. No C#-style string interpolation ($"...")
    2. Use traditional null checking with Is Nothing/IsNot Nothing
    3. Prohibit null-conditional operators (?.)
    4. Require explicit type declarations
    5. Enforce Try-Catch blocks around database operations
- **Context**:
    Files: *.vb 