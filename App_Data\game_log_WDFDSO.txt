2025-04-05 19:09:42.553262 - Game[WDFDSO] - Conn[no-connection]: updateTurnMessage - Complete
2025-04-05 19:09:47.722554 - Game[WDFDSO] - Conn[no-connection]: Player 2 join attempt
	{
  "gameIdToJoin": "WDFDS<PERSON>",
  "userId": 1,
  "hubId": "95bf7717-e1b5-4b75-bbcc-dfd59d74f4f2"
}
2025-04-05 19:09:47.730875 - Game[WDFDSO] - Conn[no-connection]: joinGame() - STEP 4 - isFirstPlayer = false;
2025-04-05 19:09:47.737978 - Game[WDFDSO] - Conn[no-connection]: joinGame() - STEP 5 - gameHub.server.joinGame(): gameIdToJoin: WDFDSO userId:1
2025-04-05 19:09:47.877385 - Game[WDFDSO] - Conn[no-connection]: updateTurnMessage - Complete
2025-04-05 19:09:47.883889 - Game[WDFDSO] - Conn[no-connection]: updateTurnMessage - Complete
2025-04-05 19:09:48.392055 - Game[WDFDSO] - Conn[no-connection]: Prison displays updated
2025-04-05 19:09:48.900600 - Game[WDFDSO] - Conn[no-connection]: Player 2 joined successfully
2025-04-05 19:09:48.907690 - Game[WDFDSO] - Conn[no-connection]: updateTurnMessage - Complete
