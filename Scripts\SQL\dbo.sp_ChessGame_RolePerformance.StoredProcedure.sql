USE [Elthos<PERSON>hes<PERSON>]
GO
/****** Object:  StoredProcedure [dbo].[sp_ChessGame_RolePerformance]    Script Date: 4/5/2025 1:17:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[sp_ChessGame_RolePerformance]
AS
BEGIN
   SET NOCOUNT ON;

   WITH RoleStats AS (
       SELECT 
           p.UserId,
           p.Display<PERSON>,
           m.PlayerRole,
           COUNT(DISTINCT m.GameId) as GamesInRole,
           COUNT(CASE WHEN m.PieceCaptured <> '' THEN 1 END) as CapturesInRole
       FROM ChessUsers p
       JOIN ChessMoves m ON p.UserId = m.UserId
       GROUP BY p.UserId, p.DisplayName, m.PlayerRole
   )
   SELECT 
       DisplayName,
       PlayerRole,
       GamesInRole,
       CapturesInRole,
       CAST(CAST(CapturesInRole AS FLOAT) / CAST(GamesInRole AS FLOAT) AS DECIMAL(5,2)) as CapturesPerGame
   FROM RoleStats
   ORDER BY PlayerRole, CapturesPerGame DESC;
END
GO
