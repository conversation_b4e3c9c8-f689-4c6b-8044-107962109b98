USE [ElthosChess]
GO
/****** Object:  StoredProcedure [dbo].[sp_ChessGame_GetPlayersList]    Script Date: 4/5/2025 1:17:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
create PROCEDURE [dbo].[sp_ChessGame_GetPlayersList]
AS 
/*
===============================================
USAGE:

  EXEC sp_ChessGame_GetPlayersList

===============================================
*/

SELECT DISTINCT 
  u1.DisplayName
FROM ChessGames               g
  INNER JOIN ChessMoves       m1
    on g.gameid = m1.gameid
  INNER JOIN ChessUsers       u1
    on u1.UserId = g.FirstPlayerUserId 
	or u1.UserId = g.SecondPlayerUserId
GO
