---
description: 
globs: 
---
### Rule: UASP.Net Style and Structure
- **Instruction**: |
    - Write idiomatic and efficient VB.Net code.
    - Follow .NET framework 4.7.2 conventions.
    - You use native ASP.Net HTML controls on all pages.
    - You prefer to find generic reusable code solutions.
    - If you need to use Ajax methods then Async/await should be used where applicable to ensure non-blocking UI operations.
  
    ## WebMethod and Performance Optimization
    - Utilize server-side code or WebMethods optimally based on the project requirements.
    - Use asynchronous methods (async/await) for API calls or UI actions that could block the main thread.
    - Use EventCallbacks for handling user interactions efficiently, passing only minimal data when triggering events.