Imports System.Web.Security
Imports System.Data.SqlClient
Imports System.Data
Imports System.Diagnostics
Imports System.Security.Cryptography
Imports System.Text
Imports System.Net.Mail
Imports System.IO

Public Partial Class Login
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Debug.WriteLine("Login Page_Load")
        Debug.WriteLine("Login Page_Load called - IsPostBack: " & IsPostBack.ToString())
        Debug.WriteLine("Form submission type: " & Request.Form("__EVENTTARGET"))
        Debug.WriteLine("Form submission argument: " & Request.Form("__EVENTARGUMENT"))

        ' Check for reset token in query string
        If Not IsPostBack Then
            Dim token As String = Request.QueryString("token")
            Debug.WriteLine("Token from QueryString: " & If(token, "NULL"))
            
            If Not String.IsNullOrEmpty(token) Then
                Debug.WriteLine("Setting token to hidden field")
                hdnResetToken.Value = token
                
                ' Verify token is valid before showing modal
                Using conn As New SqlConnection(ConfigurationManager.ConnectionStrings("ChessGameConnection").ConnectionString)
                    conn.Open()
                    Using cmd As New SqlCommand("SELECT 1 FROM ChessUsers WHERE ResetToken = @Token AND ResetTokenExpiry > @CurrentTime", conn)
                        cmd.Parameters.AddWithValue("@Token", token)
                        cmd.Parameters.AddWithValue("@CurrentTime", DateTime.UtcNow)
                        
                        Dim isValid As Boolean = cmd.ExecuteScalar() IsNot Nothing
                        Debug.WriteLine("Token validation result: " & isValid)
                        
                        If isValid Then
                            ScriptManager.RegisterStartupScript(Me, Me.GetType(), 
                                "showResetModal", 
                                "showResetPasswordModal();", 
                                True)
                        Else
                            lblResetPasswordMessage.Text = "Invalid or expired reset token."
                            lblResetPasswordMessage.CssClass = "message error"
                        End If
                    End Using
                End Using
            End If
        End If

        ' Add this line to see all form variables
        For Each key As String In Request.Form.AllKeys
            Debug.WriteLine("Login Form key: " & key & " = " & Request.Form(key))
        Next
        
        ' Prevent direct URL access if already authenticated
        If User.Identity.IsAuthenticated Then
            Response.Redirect("~/Default.aspx")
        End If
    End Sub

    Protected Sub btnLogin_Click(sender As Object, e As EventArgs) Handles btnLogin.Click
        Try
            Debug.WriteLine("Login attempt for username: " & txtUsername.Text)
            
            Using conn As New SqlConnection(ConfigurationManager.ConnectionStrings("ChessGameConnection").ConnectionString)
                conn.Open()
                Debug.WriteLine("Database connection opened")
                
                Using cmd As New SqlCommand("SELECT PasswordHash FROM ChessUsers WHERE Username = @Username", conn)
                    cmd.Parameters.AddWithValue("@Username", txtUsername.Text.Trim())
                    
                    Using reader As SqlDataReader = cmd.ExecuteReader()
                        If reader.Read() Then
                            Dim storedHash As String = If(reader("PasswordHash") IsNot DBNull.Value, 
                                                        reader("PasswordHash").ToString(), 
                                                        String.Empty)
                            
                            Debug.WriteLine("Found user, checking password")
                            
                            If String.IsNullOrEmpty(storedHash) Then
                                Debug.WriteLine("No password hash found")
                                lblError.Text = "Invalid username or password"
                                Return
                            End If

                            Dim common As New PvAChess.CommonCode()

                            Dim hashedPassword As String = common.HashPassword(txtPassword.Text)
                            Debug.WriteLine("Entered hash: " & hashedPassword)
                            Debug.WriteLine("Stored hash: " & storedHash)
                            Debug.WriteLine("Hash comparison result: " & (storedHash = hashedPassword).ToString())
                            
                            If storedHash = hashedPassword Then
                                Debug.WriteLine("Password match, setting up authentication")
                                
                                ' Set up Forms Authentication
                                Dim authTicket As New FormsAuthenticationTicket( _
                                    1, _
                                    txtUsername.Text, _
                                    DateTime.Now, _
                                    DateTime.Now.AddMinutes(30), _
                                    False, _
                                    String.Empty _
                                )
                                
                                ' Encrypt the ticket and create the cookie
                                Dim encryptedTicket As String = FormsAuthentication.Encrypt(authTicket)
                                Dim authCookie As New HttpCookie(FormsAuthentication.FormsCookieName, encryptedTicket)
                                Response.Cookies.Add(authCookie)
                                
                                ' Set session variables
                                Session("Username") = txtUsername.Text
                                
                                Debug.WriteLine("Authentication set up, redirecting to Default.aspx")
                                FormsAuthentication.RedirectFromLoginPage(txtUsername.Text, False)
                            Else
                                Debug.WriteLine("Password mismatch")
                                lblError.Text = "Invalid username or password"
                            End If
                        Else
                            Debug.WriteLine("Username not found")
                            lblError.Text = "Invalid username or password"
                        End If
                    End Using
                End Using
            End Using
            
        Catch ex As Exception
            Debug.WriteLine("Login error: " & ex.Message)
            Debug.WriteLine("Stack trace: " & ex.StackTrace)
            lblError.Text = "An error occurred during login"
        End Try
    End Sub

    Protected Sub btnRequestReset_Click(sender As Object, e As EventArgs)
        LogToFile("Password reset request started")
        LogToFile("IsValid: " & Page.IsValid.ToString())
        LogToFile("IsPostBack: " & IsPostBack.ToString())

        Try
            If Not Page.IsValid Then
                LogToFile("Page validation failed")
                lblResetMessage.Text = "Please correct the validation errors."
                lblResetMessage.CssClass = "error"
                Return
            End If

            Dim email As String = txtResetEmail.Text.Trim()
            LogToFile("Processing reset for email: " & email)
            
            ' Generate reset token
            Dim resetToken As String = Guid.NewGuid().ToString()
            Dim expiryDate As DateTime = DateTime.UtcNow.AddHours(24)
            
            Using conn As New SqlConnection(ConfigurationManager.ConnectionStrings("ChessGameConnection").ConnectionString)
                LogToFile("Opening database connection...")
                
                Try
                    conn.Open()
                    LogToFile("Database connection opened")
                    
                    Using cmd As New SqlCommand("sp_ChessUser_StoreResetToken", conn)
                        cmd.CommandType = CommandType.StoredProcedure
                        
                        ' Add parameters with logging
                        LogToFile("Setting up parameters:")
                        LogToFile("Email parameter: '" & email & "'")
                        LogToFile("ResetToken parameter: '" & resetToken & "'")
                        LogToFile("ExpiryDate parameter: '" & expiryDate.ToString() & "'")
                        
                        cmd.Parameters.Add("@Email", SqlDbType.NVarChar, 100).Value = email
                        cmd.Parameters.Add("@ResetToken", SqlDbType.NVarChar, 100).Value = resetToken
                        cmd.Parameters.Add("@ExpiryDate", SqlDbType.DateTime).Value = expiryDate
                        
                        Dim returnParameter As SqlParameter = cmd.Parameters.Add("@ReturnVal", SqlDbType.Int)
                        returnParameter.Direction = ParameterDirection.ReturnValue
                        
                        LogToFile("Executing stored procedure...")
                        cmd.ExecuteNonQuery()
                        
                        ' Get the return value
                        Dim userExists As Integer = Convert.ToInt32(returnParameter.Value)
                        LogToFile("Stored procedure completed. Return value: " & userExists)
                        
                        ' Check database state
                        Using checkCmd As New SqlCommand(
                            "SELECT Email, ResetToken, ResetTokenExpiry FROM ChessUsers WHERE Email = @Email", 
                            conn)
                            checkCmd.Parameters.AddWithValue("@Email", email)
                            Using reader As SqlDataReader = checkCmd.ExecuteReader()
                                If reader.Read() Then
                                    LogToFile("Database check - Found user:")
                                    LogToFile("Email: " & reader("Email").ToString())
                                    LogToFile("Current ResetToken: " & 
                                        If(reader("ResetToken") Is DBNull.Value, "NULL", reader("ResetToken").ToString()))
                                    LogToFile("Current ResetTokenExpiry: " & 
                                        If(reader("ResetTokenExpiry") Is DBNull.Value, "NULL", reader("ResetTokenExpiry").ToString()))
                                Else
                                    LogToFile("Database check - No user found with email: " & email)
                                End If
                            End Using
                        End Using
                        
                        If userExists > 0 Then
                            LogToFile("User found, sending reset email")
                            SendResetEmail(email, resetToken)
                            LogToFile("Reset email sent successfully")
                        Else
                            LogToFile("No user found with email: " & email)
                        End If
                        
                        lblResetMessage.Text = "If an account exists with this email, " & _
                                            "you will receive password reset instructions shortly."
                        lblResetMessage.CssClass = "message success"
                    End Using
                Catch sqlEx As SqlException
                    LogToFile("SQL Error: " & sqlEx.Message)
                    LogToFile("SQL Error Number: " & sqlEx.Number)
                    LogToFile("SQL Procedure: " & sqlEx.Procedure)
                    LogToFile("SQL Line Number: " & sqlEx.LineNumber)
                    LogToFile("SQL Stack: " & sqlEx.StackTrace)
                    Throw
                End Try
            End Using
            
        Catch ex As Exception
            LogToFile("Error in btnRequestReset_Click: " & ex.Message)
            LogToFile("Stack trace: " & ex.StackTrace)
            LogToFile("Source: " & ex.Source)
            If ex.InnerException IsNot Nothing Then
                LogToFile("Inner exception: " & ex.InnerException.Message)
                LogToFile("Inner stack trace: " & ex.InnerException.StackTrace)
            End If
            
            lblResetMessage.Text = "An error occurred. Please try again later."
            lblResetMessage.CssClass = "message error"
        End Try
        
        LogToFile("btnRequestReset_Click completed")
    End Sub

    Private Sub SendResetEmail(email As String, resetToken As String)
        Try
            Debug.WriteLine("Starting SendResetEmail...")
            Debug.WriteLine("Email: " & email)
            Debug.WriteLine("ResetToken: " & resetToken)
            
            Dim resetLink As String = Request.Url.GetLeftPart(UriPartial.Authority) & _
                                    Request.ApplicationPath.TrimEnd("/") & _
                                    "/Login.aspx?token=" & resetToken
            Debug.WriteLine("Reset Link: " & resetLink)
            
            ' Get system email from config with fallback
            Dim systemEmail As String = ConfigurationManager.AppSettings("SystemEmailAddress")
            If String.IsNullOrEmpty(systemEmail) Then
                Debug.WriteLine("WARNING: SystemEmailAddress not found in config, using default")
                systemEmail = "<EMAIL>"
            End If
            
            Debug.WriteLine("Using system email: " & systemEmail)
            
            Dim mailMessage As New MailMessage()
            mailMessage.From = New MailAddress(systemEmail)
            mailMessage.To.Add(email)
            mailMessage.Subject = "Chess Game Password Reset"
            mailMessage.Body = String.Format(
                "To reset your password, click the following link:" & vbCrLf & vbCrLf & _
                "{0}" & vbCrLf & vbCrLf & _
                "This link will expire in 24 hours." & vbCrLf & _
                "If you did not request this reset, please ignore this email.", 
                resetLink)

            Debug.WriteLine("Mail message created, attempting to send...")
            
            Using smtpClient As New SmtpClient()
                Debug.WriteLine("SMTP Settings:")
                Debug.WriteLine("Host: " & smtpClient.Host)
                Debug.WriteLine("Port: " & smtpClient.Port)
                Debug.WriteLine("EnableSsl: " & smtpClient.EnableSsl)
                
                smtpClient.Send(mailMessage)
                Debug.WriteLine("Email sent successfully")
            End Using
            
        Catch ex As Exception
            Debug.WriteLine("Email send error: " & ex.Message)
            Debug.WriteLine("Stack trace: " & ex.StackTrace)
            Throw
        End Try
    End Sub

    Protected Sub btnSetNewPassword_Click(sender As Object, e As EventArgs)
        Try
            If Not Page.IsValid Then
                Return
            End If

            Dim resetToken As String = hdnResetToken.Value
            Dim newPassword As String = txtNewPassword.Text

            Using conn As New SqlConnection(ConfigurationManager.ConnectionStrings("ChessGameConnection").ConnectionString)
                conn.Open()
                Using cmd As New SqlCommand("sp_ChessUser_ResetPassword", conn)
                    cmd.CommandType = CommandType.StoredProcedure
                    cmd.Parameters.AddWithValue("@ResetToken", resetToken)
                    cmd.Parameters.AddWithValue("@NewPassword", HashPassword(newPassword))
                    cmd.Parameters.AddWithValue("@CurrentTime", DateTime.UtcNow)

                    Debug.WriteLine("Executing sp_ChessUser_ResetPassword")
                    Debug.WriteLine("Token: " & resetToken)
                    Debug.WriteLine("Current Time: " & DateTime.UtcNow.ToString())
                    
                    ' First check if token exists
                    Using checkCmd As New SqlCommand(
                        "SELECT ResetTokenExpiry FROM ChessUsers WHERE ResetToken = @Token", conn)
                        checkCmd.Parameters.AddWithValue("@Token", resetToken)
                        Dim tokenExpiry As Object = checkCmd.ExecuteScalar()
                        
                        If tokenExpiry Is Nothing Then
                            Debug.WriteLine("Token not found in database")
                            lblResetPasswordMessage.Text = "This reset link has already been used or is invalid. Please request a new password reset."
                            lblResetPasswordMessage.CssClass = "message error"
                            Return
                        ElseIf DateTime.Parse(tokenExpiry.ToString()) < DateTime.UtcNow Then
                            Debug.WriteLine("Token expired")
                            lblResetPasswordMessage.Text = "This reset link has expired. Please request a new password reset."
                            lblResetPasswordMessage.CssClass = "message error"
                            Return
                        End If
                    End Using
                    
                    ' Add return parameter
                    Dim returnParameter As SqlParameter = cmd.Parameters.Add("@ReturnVal", SqlDbType.Int)
                    returnParameter.Direction = ParameterDirection.ReturnValue
                    
                    cmd.ExecuteNonQuery()
                    Dim result As Integer = Convert.ToInt32(returnParameter.Value)
                    Debug.WriteLine("Stored procedure return value: " & result)
                    
                    If result > 0 Then
                        lblResetPasswordMessage.Text = "Password has been reset successfully. You can now login."
                        lblResetPasswordMessage.CssClass = "message success"
                        
                        ' Hide modal and remove token from URL
                        ScriptManager.RegisterStartupScript(Me, Me.GetType(), 
                            "hideModalAndClearUrl", 
                            "setTimeout(function() { " & _
                            "   hideResetPasswordModal(); " & _
                            "   history.replaceState(null, '', window.location.pathname); " & _
                            "}, 2000);", 
                            True)
                            
                        ' Clear the token from the hidden field
                        hdnResetToken.Value = String.Empty
                    Else
                        lblResetPasswordMessage.Text = "Unable to reset password. Please try again or request a new reset link."
                        lblResetPasswordMessage.CssClass = "message error"
                    End If
                End Using
            End Using

        Catch ex As Exception
            lblResetPasswordMessage.Text = "An error occurred. Please try again."
            lblResetPasswordMessage.CssClass = "message error"
            Debug.WriteLine("Password reset error: " & ex.Message)
        End Try
    End Sub

    Private Function HashPassword(password As String) As String
        Using sha512 As SHA512 = SHA512.Create()
            Dim passwordBytes As Byte() = Encoding.UTF8.GetBytes(password)
            Dim hashBytes As Byte() = sha512.ComputeHash(passwordBytes)
            Return BitConverter.ToString(hashBytes).Replace("-", "")
        End Using
    End Function

    Private Sub LogToFile(message As String)
        Try
            Dim logPath As String = Server.MapPath("~/App_Data/PasswordReset.log")
            Dim timestamp As String = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff")
            Dim logMessage As String = String.Format("{0}: {1}", timestamp, message)
            
            ' Append to log file
            Using writer As New StreamWriter(logPath, True)
                writer.WriteLine(logMessage)
            End Using
        Catch ex As Exception
            ' If we can't log, at least try to write to the event log
            EventLog.WriteEntry("Chess Game", "Failed to write to log file: " & ex.Message, EventLogEntryType.Error)
        End Try
    End Sub
End Class 