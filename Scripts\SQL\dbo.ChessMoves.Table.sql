USE [Elth<PERSON><PERSON><PERSON><PERSON>]
GO
/****** Object:  Table [dbo].[ChessMoves]    Script Date: 4/5/2025 1:17:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ChessMoves](
	[MoveId] [int] IDENTITY(1,1) NOT NULL,
	[GameId] [nvarchar](50) NOT NULL,
	[MoveNumber] [int] NOT NULL,
	[PlayerRole] [nvarchar](10) NOT NULL,
	[FromRow] [int] NOT NULL,
	[FromCol] [int] NOT NULL,
	[ToRow] [int] NOT NULL,
	[ToCol] [int] NOT NULL,
	[PieceMoved] [nvarchar](20) NOT NULL,
	[PieceCaptured] [nvarchar](20) NULL,
	[WasPromotion] [bit] NOT NULL,
	[Timestamp] [datetime] NOT NULL,
	[UserId] [int] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[MoveId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
ALTER TABLE [dbo].[ChessMoves] ADD  DEFAULT ((0)) FOR [WasPromotion]
GO
ALTER TABLE [dbo].[ChessMoves]  WITH NOCHECK ADD  CONSTRAINT [FK_ChessMoves_ChessGames] FOREIGN KEY([GameId])
REFERENCES [dbo].[ChessGames] ([GameId])
GO
ALTER TABLE [dbo].[ChessMoves] NOCHECK CONSTRAINT [FK_ChessMoves_ChessGames]
GO
ALTER TABLE [dbo].[ChessMoves]  WITH NOCHECK ADD  CONSTRAINT [FK_ChessMoves_ChessUsers] FOREIGN KEY([UserId])
REFERENCES [dbo].[ChessUsers] ([UserId])
GO
ALTER TABLE [dbo].[ChessMoves] CHECK CONSTRAINT [FK_ChessMoves_ChessUsers]
GO
