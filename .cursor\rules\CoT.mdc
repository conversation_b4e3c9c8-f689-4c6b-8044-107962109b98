---
description: Using Chain of Thought
globs: *.vb,*.js,*.aspx
---
### Rule: Use Chain of Thought
- **Instruction**: |
  ALWAYS employ Chain of Thought (CoT) for complex tasks:
  Deconstruct problems into smaller, manageable steps
  Elucidate your reasoning for each step
  Demonstrate your work, even when it appears self-evident

  CoT Structure:
  a. Initial problem statement
  b. Step-by-step breakdown
  c. Reasoning for each step
  d. Intermediate conclusions
  e. Final answer or solution
- **Context**:
    Files: *.vb, *.js, *.aspx
    When: [analysis, code-generation, refactoring]