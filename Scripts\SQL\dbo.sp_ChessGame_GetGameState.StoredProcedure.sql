USE [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]
GO
/****** Object:  StoredProcedure [dbo].[sp_ChessGame_GetGameState]    Script Date: 4/5/2025 1:17:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- Create stored procedure for retrieving game state
CREATE PROCEDURE [dbo].[sp_ChessGame_GetGameState]
    @GameId NVARCHAR(50)
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Get basic game information
    SELECT 
        g.GameId,
        g.FirstPlayerUserId,
        g.SecondPlayerUserId,
        g.IsSoloPlay,
        g.StartTime,
        g.EndTime,
        g.EndGameAction,
        g.EndGameUserID,
        CAST(1 AS BIT) AS FirstPlayerIsPlebs -- Default value since column doesn't exist
    FROM 
        ChessGames g
    WHERE 
        g.GameId = @GameId;
        
    -- If no game found, return empty result
    IF @@ROWCOUNT = 0
    BEGIN
        RETURN;
    END
    
    -- Get moves for the game
    SELECT 
        m.MoveN<PERSON><PERSON>,
        m.<PERSON>,
        m.<PERSON>,
        m.<PERSON>,
        m.<PERSON>,
        m.<PERSON>,
        m.<PERSON>,
        m.<PERSON>,
        m.<PERSON>,
        m.UserId
    FROM 
        ChessMoves m
    WHERE 
        m.GameId = @GameId
    ORDER BY 
        m.MoveNumber;
END
GO
