USE [Elth<PERSON><PERSON><PERSON><PERSON>]
GO
/****** Object:  StoredProcedure [dbo].[sp_ChessUser_ResetPassword]    Script Date: 4/5/2025 1:17:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [dbo].[sp_ChessUser_ResetPassword]
    @ResetToken NVARCHAR(100),
    @NewPassword NVARCHAR(128),
    @CurrentTime DATETIME
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Debug prints
    PRINT 'Checking token: ' + @ResetToken
    PRINT 'Current time: ' + CONVERT(NVARCHAR(50), @CurrentTime, 121)
    
    -- First check if token exists and is valid
    IF NOT EXISTS (
        SELECT 1 
        FROM dbo.ChessUsers 
        WHERE ResetToken = @ResetToken 
        AND ResetTokenExpiry > @CurrentTime
    )
    BEGIN
        PRINT 'Token invalid or expired'
        RETURN 0
    END
    
    -- Update password if token is valid
    UPDATE dbo.ChessUsers
    SET PasswordHash = @NewPassword,
        ResetToken = NULL,
        ResetTokenExpiry = NULL
    WHERE ResetToken = @ResetToken
    AND ResetTokenExpiry > @CurrentTime
    
    DECLARE @RowCount INT = @@ROWCOUNT
    PRINT 'Rows updated: ' + CAST(@RowCount AS NVARCHAR(10))
    
    RETURN @RowCount
END
GO
