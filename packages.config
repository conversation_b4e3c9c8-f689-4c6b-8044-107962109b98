﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="jQuery" version="3.7.1" targetFramework="net472" />
  <package id="Microsoft.AspNet.Cors" version="5.0.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.SignalR" version="2.4.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.SignalR.Core" version="2.4.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.SignalR.JS" version="2.4.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.SignalR.SystemWeb" version="2.4.3" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Authorization" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Connections.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Http.Features" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.SignalR.Common" version="1.1.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.SignalR.Core" version="1.1.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.SignalR.Protocols.Json" version="1.1.0" targetFramework="net472" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="2.0.1" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Options" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Primitives" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Owin" version="4.2.2" targetFramework="net472" />
  <package id="Microsoft.Owin.Cors" version="4.2.2" targetFramework="net472" />
  <package id="Microsoft.Owin.Host.SystemWeb" version="4.2.2" targetFramework="net472" />
  <package id="Microsoft.Owin.Security" version="4.2.2" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net472" />
  <package id="Owin" version="1.0" targetFramework="net472" />
  <package id="System.Buffers" version="4.5.0" targetFramework="net472" />
  <package id="System.ComponentModel.Annotations" version="4.5.0" targetFramework="net472" />
  <package id="System.IO.Pipelines" version="4.5.2" targetFramework="net472" />
  <package id="System.Memory" version="4.5.1" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.4.0" targetFramework="net472" />
  <package id="System.Reflection.Emit" version="4.3.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.1" targetFramework="net472" />
  <package id="System.Threading.Channels" version="4.5.0" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.1" targetFramework="net472" />
</packages>