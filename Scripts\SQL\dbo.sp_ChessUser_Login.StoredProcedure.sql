USE [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]
GO
/****** Object:  StoredProcedure [dbo].[sp_ChessUser_Login]    Script Date: 4/5/2025 1:17:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [dbo].[sp_ChessUser_Login]
    @Username NVARCHAR(50),
    @PasswordHash NVARCHAR(128)
AS
BEGIN
    SET NOCOUNT ON;

    -- Update LastLoginDate for successful logins
    UPDATE dbo.ChessUsers
    SET LastLoginDate = GETDATE()
    OUTPUT 
        INSERTED.UserId,
        INSERTED.Username,
        INSERTED.DisplayName,
        INSERTED.RequirePasswordReset
    WHERE Username = @Username 
    AND PasswordHash = @PasswordHash;

    -- If no rows were affected, the login failed
    IF @@ROWCOUNT = 0
    BEGIN
        -- Return an empty result set to indicate failed login
        SELECT TOP 0 UserId, <PERSON><PERSON><PERSON>, DisplayN<PERSON>, RequirePasswordReset
        FROM dbo.ChessUsers;
    END
END
GO
