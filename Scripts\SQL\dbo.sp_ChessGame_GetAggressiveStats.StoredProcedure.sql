USE [ElthosChess]
GO
/****** Object:  StoredProcedure [dbo].[sp_ChessGame_GetAggressiveStats]    Script Date: 4/5/2025 1:17:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- ALTER PROCEDURE for Aggressive Players
CREATE PROCEDURE [dbo].[sp_ChessGame_GetAggressiveStats]
AS
BEGIN
    SET NOCOUNT ON;

    WITH PlayerCaptures AS (
        SELECT 
            p.UserId,
            p.DisplayName,
            COUNT(CASE WHEN m.PieceCaptured <> '' THEN 1 END) as TotalCaptures,
            COUNT(DISTINCT m.GameId) as GamesPlayed
        FROM ChessUsers p
        JOIN ChessGames g ON p.UserId = g.FirstPlayerUserId OR p.UserId = g.SecondPlayerUserId
        JOIN ChessMoves m ON g.GameId = m.GameId AND p.UserId = m.UserId
		WHERE p.DisplayName NOT LIKE '%Test%'
        GROUP BY p.UserId, p.DisplayName
    )
    SELECT 
        DisplayName,
        TotalCaptures,
        GamesPlayed,
        CAST(CAST(TotalCaptures AS FLOAT) / CAST(GamesPlayed AS FLOAT) AS DECIMAL(5,2)) as CapturesPerGame
    FROM PlayerCaptures
    WHERE GamesPlayed > 0
    ORDER BY CapturesPerGame DESC;
END
GO
