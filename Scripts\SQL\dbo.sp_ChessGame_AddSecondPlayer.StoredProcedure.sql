USE [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]
GO
/****** Object:  StoredProcedure [dbo].[sp_ChessGame_AddSecondPlayer]    Script Date: 4/5/2025 1:17:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[sp_ChessGame_AddSecondPlayer]
    @GameId nvarchar(50),
    @SecondPlayerUserId int
AS
BEGIN
    BEGIN TRY
        UPDATE ChessGames 
        SET SecondPlayerUserId = @SecondPlayerUserId
        WHERE GameId = @GameId
        
        RETURN @@ROWCOUNT
    END TRY
    BEGIN CATCH
        RETURN -1
    END CATCH
END
GO
