﻿Imports System.Web
Imports System.Web.Services
Imports System.IO
Imports System.Configuration
Imports Newtonsoft.Json.Linq
Imports System.Collections.Concurrent
Imports System.Threading
Imports PvAChess

Public Class LoggingHandler
    Implements System.Web.IHttpHandler
    Implements System.Web.SessionState.IRequiresSessionState

    ' Add queue, timer, and structure
    Private Shared ReadOnly LogQueue As New ConcurrentQueue(Of LogEntry)
    Private Shared ReadOnly LogTimer As Threading.Timer = InitializeTimer()
    Private Shared ReadOnly _lockObject As New Object()

    Private Structure LogEntry
        Public Timestamp As DateTime
        Public GameId As String
        Public Message As String
        Public Data As Object
        Public ConnectionId As String

        Public Function Clone() As LogEntry
            Return New LogEntry With {
                .Timestamp = Me.Timestamp,
                .GameId = Me.GameId,
                .Message = Me.Message,
                .Data = Me.Data,
                .ConnectionId = Me.ConnectionId
            }
        End Function
    End Structure

    Private Shared Function InitializeTimer() As Threading.Timer
        Return New Threading.Timer(AddressOf ProcessLogQueue, Nothing, 0, 1000)
    End Function

    Private ReadOnly Property IsLoggingEnabled As Boolean
        Get
            Try
                ' First check custom setting
                Dim customSetting = ConfigurationManager.AppSettings("EnableGameLogging")
                If Not String.IsNullOrEmpty(customSetting) Then
                    Return Boolean.Parse(customSetting)
                End If

                ' Fall back to debug flag if custom setting isn't found
                Return HttpContext.Current.IsDebuggingEnabled
            Catch ex As Exception
                ' If anything goes wrong, default to false for production safety
                Return False
            End Try
        End Get
    End Property

    Public Shared Property CurrentGameId As String = String.Empty

    Public Sub ProcessRequest(ByVal context As HttpContext) Implements IHttpHandler.ProcessRequest

        ' In ProcessRequest, right before getting GameId:
        If HttpContext.Current IsNot Nothing Then
            System.Diagnostics.Debug.WriteLine("HttpContext exists")
            If HttpContext.Current.Session IsNot Nothing Then
                System.Diagnostics.Debug.WriteLine("Session exists")
                System.Diagnostics.Debug.WriteLine("Session keys: " & String.Join(", ", HttpContext.Current.Session.Keys.Cast(Of String)()))
                If HttpContext.Current.Session("CurrentGameId") IsNot Nothing Then
                    System.Diagnostics.Debug.WriteLine("CurrentGameId found: " & HttpContext.Current.Session("CurrentGameId"))
                Else
                    System.Diagnostics.Debug.WriteLine("CurrentGameId is Nothing")
                End If
            Else
                System.Diagnostics.Debug.WriteLine("Session is Nothing")
            End If
        Else
            System.Diagnostics.Debug.WriteLine("HttpContext is Nothing")
        End If

        ' Early exit if logging is disabled
        If Not IsLoggingEnabled Then
            context.Response.StatusCode = 200
            context.Response.Write("Logging is disabled")
            Return
        End If

        Try
            context.Response.ContentType = "text/plain"

            ' Get content first
            Dim content As String
            Using reader As New StreamReader(context.Request.InputStream)
                content = reader.ReadToEnd()
            End Using

            ' Parse JSON content for structured logging
            Dim logData As JObject = Nothing
            Try
                logData = JObject.Parse(content)
            Catch ex As Exception
                logData = New JObject()
                logData.Add("message", content)
            End Try

            System.Diagnostics.Debug.WriteLine("ProcessRequest() Step 1 - LogData: " & logData.ToString())

            ' Get GameId from logData first, then fall back to session
            Dim gameId As String = "no-game"

            If logData("gameId") IsNot Nothing Then
                gameId = logData("gameId").ToString()
                System.Diagnostics.Debug.WriteLine("logData(gameId).ToString(): " & gameId)

            ElseIf HttpContext.Current IsNot Nothing AndAlso HttpContext.Current.Session IsNot Nothing Then
                Dim sessionGameId = HttpContext.Current.Session("CurrentGameId")
                System.Diagnostics.Debug.WriteLine(" HttpContext.Current.Session(CurrentGameId) " & sessionGameId)
                If sessionGameId IsNot Nothing Then
                    System.Diagnostics.Debug.WriteLine(" sessionGameId IsNot Nothing " & sessionGameId)
                    gameId = sessionGameId.ToString()
                End If
            End If
            System.Diagnostics.Debug.WriteLine("ProcessRequest() Step 2 - GameID: " & gameId)

            ' Queue the log entry
            Dim entry As New LogEntry With {
                .Timestamp = DateTime.UtcNow,
                .GameId = gameId,
                .Message = If(logData("message")?.ToString(), "No message"),
                .Data = logData("data"),
                .ConnectionId = If(HttpContext.Current?.Session("ConnectionId")?.ToString(), "no-connection")
            }

            System.Diagnostics.Debug.WriteLine("ProcessRequest() Step 4 - entry: " & entry.ToString())

            LogQueue.Enqueue(entry)
            context.Response.Write("Log entry queued successfully")

        Catch ex As Exception
            context.Response.StatusCode = 500
            context.Response.Write("Error logging: " & ex.Message)
        End Try
    End Sub

    ' Add queue processing method
    Private Shared Sub ProcessLogQueue(state As Object)
        Try
            ' Only log if there are entries to process
            If LogQueue.Count = 0 Then
                Return
            End If

            System.Diagnostics.Debug.WriteLine($"ProcessLogQueue started with {LogQueue.Count} entries")

            Dim entries As New Dictionary(Of String, List(Of LogEntry))
            Dim entry As New LogEntry With {
                .Timestamp = DateTime.MinValue,
                .GameId = String.Empty,
                .Message = String.Empty,
                .Data = Nothing,
                .ConnectionId = String.Empty
            }

            ' Log queue state
            System.Diagnostics.Debug.WriteLine($"Current queue size: {LogQueue.Count}")

            ' Collect all current entries, grouped by GameId
            While LogQueue.Count > 0
                Dim result As LogEntry = Nothing
                If LogQueue.TryDequeue(result) Then
                    If Not entries.ContainsKey(result.GameId) Then
                        entries(result.GameId) = New List(Of LogEntry)
                    End If

                    ' Create a deep copy of the LogEntry to preserve all properties
                    Dim clonedEntry = New LogEntry With {
                        .Timestamp = result.Timestamp,
                        .GameId = result.GameId,
                        .Message = result.Message,
                        .Data = result.Data,
                        .ConnectionId = If(String.IsNullOrEmpty(result.ConnectionId), "no-connection", result.ConnectionId)
                    }

                    entries(result.GameId).Add(clonedEntry)
                End If
            End While

            ' Log what we found
            System.Diagnostics.Debug.WriteLine($"Found entries for {entries.Count} games")

            ' Process each game's logs
            For Each gameId In entries.Keys
                Dim gameEntries = entries(gameId)
                gameEntries.Sort(Function(a, b) DateTime.Compare(a.Timestamp, b.Timestamp))

                System.Diagnostics.Debug.WriteLine($"Processing {gameEntries.Count} entries for game {gameId}")

                ' Write to game-specific log file
                Dim logPath = System.Web.Hosting.HostingEnvironment.MapPath($"~/App_Data/game_log_{gameId}.txt")
                System.Diagnostics.Debug.WriteLine($"Writing to log file: {logPath}")

                SyncLock _lockObject
                    Using fileStream As New FileStream(logPath, FileMode.Append, FileAccess.Write, FileShare.ReadWrite)
                        Using writer As New StreamWriter(fileStream)
                            For Each e In gameEntries
                                Dim msg = String.Format("{0} - Game[{1}] - Conn[{2}]: {3}",
                                                      e.Timestamp.ToString("yyyy-MM-dd HH:mm:ss.ffffff"),
                                                      e.GameId,
                                                      e.ConnectionId,
                                                      e.Message)
                                If e.Data IsNot Nothing Then
                                    msg &= vbCrLf & vbTab & Newtonsoft.Json.JsonConvert.SerializeObject(e.Data, Newtonsoft.Json.Formatting.Indented)
                                End If
                                writer.WriteLine(msg)
                                System.Diagnostics.Debug.WriteLine($"Wrote log entry: {msg}")
                            Next
                        End Using
                    End Using
                End SyncLock
            Next

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Log processing failed: " & ex.Message & vbCrLf & ex.StackTrace)
        End Try
    End Sub

    Public ReadOnly Property IsReusable() As Boolean Implements IHttpHandler.IsReusable
        Get
            Return False
        End Get
    End Property

    Public Shared Sub LogToFile(message As String, Optional data As Object = Nothing, Optional GameId As String = "")
        Try
            ' Extract ConnectionId from data if available
            Dim connectionId As String = "no-connection"
            If data IsNot Nothing Then
                Dim dataType = data.GetType()
                If dataType.Name.StartsWith("VB$AnonymousType") Then
                    If dataType.GetProperty("connectionId") IsNot Nothing Then
                        connectionId = data.connectionId.ToString()
                        System.Diagnostics.Debug.WriteLine($"Found ConnectionId: {connectionId}")
                        If dataType.GetProperty("originalData") IsNot Nothing Then
                            data = data.originalData
                        End If
                    End If
                End If
            End If

            If GameId = "" Then
                GameId = "no-game"
            End If

            Dim entry = New LogEntry With {
                .GameId = GameId,
                .Message = If(message IsNot Nothing, message, "no-message"),
                .Data = data,
                .Timestamp = DateTime.Now.ToUniversalTime(),
                .ConnectionId = connectionId
            }

            System.Diagnostics.Debug.WriteLine($"Created LogEntry with ConnectionId: {entry.ConnectionId}")
            LogQueue.Enqueue(entry)
            System.Diagnostics.Debug.WriteLine("Entry queued")
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Error in LogToFile: " & ex.Message)
        End Try
    End Sub

End Class


Public Module LoggingService
    Public Sub LogToFile(message As Object, Optional data As Object = Nothing)
        ' Call the handler directly
        LoggingHandler.LogToFile(message, data)
    End Sub

    Public Sub LogToFile()
        LogToFile("Log entry", Nothing)
    End Sub
End Module

