USE [<PERSON>th<PERSON><PERSON><PERSON><PERSON>]
GO
/****** Object:  StoredProcedure [dbo].[sp_ChessGame_FastestWinners]    Script Date: 4/5/2025 1:17:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [dbo].[sp_ChessGame_FastestWinners]
AS
BEGIN 
   SELECT TOP 10
       p.DisplayName as Winner,
       g.GameId,
       DATEDIFF(minute, g.StartTime, g.EndTime) as GameDurationMinutes,
       CASE WHEN g.IsSoloPlay = 1 THEN 'Solo' ELSE 'Multiplayer' END as GameType
   FROM ChessGames g
   JOIN ChessUsers p ON p.DisplayName = g.Winner
   WHERE g.EndTime IS NOT NULL 
       AND g.Winner NOT IN ('Abandoned', 'Draw')
   ORDER BY GameDurationMinutes ASC;
END
GO
