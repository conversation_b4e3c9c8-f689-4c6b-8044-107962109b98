Imports System.Security.Cryptography
Imports System.Text
Imports System.Data.SqlClient
Imports System.Configuration
Imports System.Data
Imports System.Web.Security
Imports System.Diagnostics

Public Class ChessUserService
    Private Shared ReadOnly ConnectionString As String = ConfigurationManager.ConnectionStrings("ChessGameConnection").ConnectionString

    Public Shared Function CreateUser(username As String, email As String, password As String, displayName As String) As Boolean
        Debug.WriteLine("CreateUser called with username: " & username)
        
        Try
            Dim hashedPassword As String = HashPassword(password)
            Debug.WriteLine("Password hashed successfully")
            
            Using conn As New SqlConnection(ConfigurationManager.ConnectionStrings("ChessGameConnection").ConnectionString)
                Debug.WriteLine("Attempting database connection")
                conn.Open()
                Debug.WriteLine("Database connection opened")
                
                Using cmd As New SqlCommand("sp_ChessUser_Create", conn)
                    cmd.CommandType = CommandType.StoredProcedure
                    cmd.Parameters.AddWithValue("@Username", username)
                    cmd.Parameters.AddWithValue("@Email", email)
                    cmd.Parameters.AddWithValue("@PasswordHash", hashedPassword)
                    cmd.Parameters.AddWithValue("@DisplayName", If(String.IsNullOrEmpty(displayName), username, displayName))

                    Debug.WriteLine("Executing database command")
                    ' Need to use ExecuteScalar to get the RETURN value from the stored procedure
                    cmd.Parameters.Add("@ReturnValue", SqlDbType.Int).Direction = ParameterDirection.ReturnValue
                    cmd.ExecuteNonQuery()
                    
                    Dim result As Integer = Convert.ToInt32(cmd.Parameters("@ReturnValue").Value)
                    Debug.WriteLine("Database command completed. Return value: " & result)
                    
                    ' 0 means success, -1 means user already exists
                    Return result = 0
                End Using
            End Using
        Catch ex As Exception
            Debug.WriteLine("Error in CreateUser: " & ex.ToString())
            Return False
        End Try
    End Function

    Public Shared Function ValidateLogin(username As String, password As String) As ChessUser
        Try
            Using conn As New SqlConnection(ConnectionString)
                conn.Open()
                Using cmd As New SqlCommand("sp_ChessUser_Validate", conn)
                    cmd.CommandType = CommandType.StoredProcedure
                    cmd.Parameters.AddWithValue("@Username", username)
                    
                    Using reader As SqlDataReader = cmd.ExecuteReader()
                        If reader.Read() Then
                            Dim storedHash As String = reader("PasswordHash").ToString()
                            If VerifyPassword(password, storedHash) Then
                                ' Create user object
                                Dim user As New ChessUser With {
                                    .UserId = CInt(reader("UserId")),
                                    .Username = reader("Username").ToString(),
                                    .Email = reader("Email").ToString(),
                                    .DisplayName = If(reader("DisplayName") IsNot DBNull.Value, 
                                                    reader("DisplayName").ToString(), 
                                                    reader("Username").ToString()),
                                    .DateCreated = CDate(reader("DateCreated")),
                                    .LastLoginDate = If(reader("LastLoginDate") IsNot DBNull.Value, 
                                                      CDate(reader("LastLoginDate")), 
                                                      Nothing)
                                }
                                
                                ' Update last login date
                                UpdateLastLoginDate(user.UserId)
                                
                                Return user
                            End If
                        End If
                    End Using
                End Using
            End Using
        Catch ex As Exception
            ' Log the error
            System.Diagnostics.Debug.WriteLine("Error in ValidateLogin: " & ex.Message)
        End Try
        Return Nothing
    End Function

    Private Shared Sub UpdateLastLoginDate(userId As Integer)
        Try
            Using conn As New SqlConnection(ConnectionString)
                conn.Open()
                Using cmd As New SqlCommand("sp_ChessUser_UpdateLastLogin", conn)
                    cmd.CommandType = CommandType.StoredProcedure
                    cmd.Parameters.AddWithValue("@UserId", userId)
                    cmd.ExecuteNonQuery()
                End Using
            End Using
        Catch ex As Exception
            ' Log the error
            System.Diagnostics.Debug.WriteLine("Error in UpdateLastLoginDate: " & ex.Message)
        End Try
    End Sub

    Public Shared Function HashPassword(password As String) As String
        ' Use a more secure hashing method
        Using sha256 As New System.Security.Cryptography.SHA256Managed()
            Dim bytes As Byte() = System.Text.Encoding.UTF8.GetBytes(password)
            Dim hash As Byte() = sha256.ComputeHash(bytes)
            Return Convert.ToBase64String(hash)
        End Using
    End Function

    Private Shared Function VerifyPassword(password As String, storedHash As String) As Boolean
        Dim hashOfInput As String = HashPassword(password)
        Return hashOfInput = storedHash
    End Function
End Class 