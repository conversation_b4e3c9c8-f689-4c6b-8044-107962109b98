﻿Imports System.Data
Imports System.Data.SqlClient
Imports System.Configuration
Imports System.Web.Security
Imports System.IO
Imports System.Web.Services
Imports System.Web.Script.Services
Imports System.Text
Imports Newtonsoft.Json

<System.Web.Script.Services.ScriptService>
Public Class ChessGameReplay
    Inherits System.Web.UI.Page

    Private Property CurrentGame As GameReplay
        Get
            If ViewState("CurrentGame") IsNot Nothing Then
                Return DirectCast(ViewState("CurrentGame"), GameReplay)
            End If
            Return Nothing
        End Get
        Set(value As GameReplay)
            ViewState("CurrentGame") = value
        End Set
    End Property

    Private Property CurrentMoveIndex As Integer
        Get
            If ViewState("CurrentMoveIndex") IsNot Nothing Then
                Return Convert.ToInt32(ViewState("CurrentMoveIndex"))
            End If
            Return -1
        End Get
        Set(value As Integer)
            ViewState("CurrentMoveIndex") = value
        End Set
    End Property

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' Add debug logging
        System.Diagnostics.Debug.WriteLine("ChessGameReplay.aspx Page_Load - Current Path: " & Request.Path)
        System.Diagnostics.Debug.WriteLine("ChessGameReplay.aspx Page_Load - Session UserId: " & If(Session("UserId") Is Nothing, "Nothing", Session("UserId").ToString()))
        System.Diagnostics.Debug.WriteLine("ChessGameReplay.aspx Page_Load - User.Identity.IsAuthenticated: " & User.Identity.IsAuthenticated)

        If User.Identity.IsAuthenticated Then
            If User IsNot Nothing AndAlso User.Identity IsNot Nothing AndAlso Not String.IsNullOrEmpty(User.Identity.Name) Then
                lblUserDisplay.Text = String.Format("Welcome, {0}", User.Identity.Name)
            Else
                lblUserDisplay.Text = "Welcome, Guest"
            End If

            If Session("UserId") Is Nothing Then
                ' Try to recover UserId from the authenticated user
                Dim username As String = User.Identity.Name
                System.Diagnostics.Debug.WriteLine("ChessGameReplay.aspx - Attempting to recover UserId for user: " & username)

                Try
                    Using conn As New SqlConnection(ConfigurationManager.ConnectionStrings("ChessGameConnection").ConnectionString)
                        conn.Open()
                        Using cmd As New SqlCommand("SELECT UserId FROM ChessUsers WHERE Username = @Username", conn)
                            cmd.Parameters.AddWithValue("@Username", username)
                            Dim result = cmd.ExecuteScalar()
                            If result IsNot Nothing Then
                                Session("UserId") = Convert.ToInt32(result)
                                System.Diagnostics.Debug.WriteLine("ChessGameReplay.aspx - Recovered UserId: " & Session("UserId"))
                            Else
                                System.Diagnostics.Debug.WriteLine("ChessGameReplay.aspx - Could not recover UserId, redirecting to Login")
                                FormsAuthentication.SignOut()
                                Response.Redirect("~/Login.aspx", False)
                                Context.ApplicationInstance.CompleteRequest()
                                Return
                            End If
                        End Using
                    End Using
                Catch ex As Exception
                    System.Diagnostics.Debug.WriteLine("ChessGameReplay.aspx - Error recovering UserId: " & ex.Message)
                    FormsAuthentication.SignOut()
                    Response.Redirect("~/Login.aspx", False)
                    Context.ApplicationInstance.CompleteRequest()
                    Return
                End Try
            End If


        Else
            System.Diagnostics.Debug.WriteLine("ChessGameReplay.aspx - User not authenticated, redirecting to Login.aspx")
            Response.Redirect("~/Login.aspx", False)
            Context.ApplicationInstance.CompleteRequest()
            Return
        End If

        If Not IsPostBack Then
            If Session("UserId") IsNot Nothing Then
                hdnUserId.Value = Session("UserId").ToString()
            End If
            InitializePage()
            InitializeGameData()
        Else
            System.Diagnostics.Debug.WriteLine($"Postback - Current Game: {CurrentGame IsNot Nothing}, MoveIndex: {CurrentMoveIndex}")
        End If
    End Sub

    Private Sub btnLeaderBoard_Click(sender As Object, e As EventArgs) Handles btnLeaderBoard.Click
        Response.Redirect("LeaderBoard.aspx", False)
    End Sub

    Private Sub btnLogout_Click(sender As Object, e As EventArgs) Handles btnLogout.Click
        Try
            LogToFile("Starting logout process", New With {
                .sessionId = Session.SessionID,
                .isAuthenticated = User.Identity.IsAuthenticated,
                .userId = If(Session("UserId") IsNot Nothing, Session("UserId").ToString(), "None")
            })

            FormsAuthentication.SignOut()
            LogToFile("Forms authentication signed out")

            Session.Clear()
            LogToFile("Session cleared")

            Session.Abandon()
            LogToFile("Session abandoned")

            LogToFile("About to redirect to Login.aspx")
            Response.Redirect("~/Login.aspx", False)
            Context.ApplicationInstance.CompleteRequest()
        Catch ex As IOException
            LogToFile("IOException during logout", New With {
                .error = ex.Message,
                .stackTrace = ex.StackTrace
            })
            Throw
        Catch ex As Exception
            LogToFile("Error in logout", New With {
                .error = ex.Message,
                .stackTrace = ex.StackTrace
            })
            Throw
        End Try
    End Sub

    Private Sub InitializePage()
        ' Load players for dropdown
        LoadPlayers()

        ' Set default date range (last 30 days)
        txtStartDate.Text = DateTime.Now.AddDays(-30).ToString("yyyy-MM-dd")
        txtEndDate.Text = DateTime.Now.AddDays(1).ToString("yyyy-MM-dd")

        ' Initial game load
        LoadGames()
    End Sub

    Private Sub LoadPlayers()
        Try
            Dim connString As String = ConfigurationManager.ConnectionStrings("ChessGameConnection").ConnectionString

            Using conn As New SqlConnection(connString)
                conn.Open()

                Using cmd As New SqlCommand("sp_ChessGame_GetPlayersList", conn)
                    cmd.CommandType = CommandType.StoredProcedure

                    Dim players As New List(Of ListItem)
                    players.Add(New ListItem("All Players", ""))

                    Using reader As SqlDataReader = cmd.ExecuteReader()
                        While reader.Read()
                            Dim DisplayName As String = reader("DisplayName").ToString()
                            players.Add(New ListItem(DisplayName, DisplayName))
                        End While
                    End Using

                    ddlPlayer.DataSource = players
                    ddlPlayer.DataBind()
                End Using
            End Using
        Catch ex As Exception
            ' Enhanced error logging
            LogToFile("Error in LoadPlayers", New With {
                .error = ex.Message,
                .stackTrace = ex.StackTrace,
                .connectionString = ConfigurationManager.ConnectionStrings("ChessGameConnection").ConnectionString
            })
            ShowError("Unable to load player list. Please try again later.")
        End Try
    End Sub

    Protected Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        LoadGames()
    End Sub

    Private Sub LoadGames()
        Try
            Dim startDate As DateTime
            Dim endDate As DateTime
            Dim playerName As String = ddlPlayer.SelectedValue
            Dim soloGames As Boolean = Convert.ToInt32(rblGameType.SelectedValue) = 1

            '== THE STORED PROCEDURE EXPECTS AN EMPTY STRING FOR ALL PLAYERS
            If playerName = "All Players" Then
                playerName = ""
            End If

            If Not DateTime.TryParse(txtStartDate.Text, startDate) OrElse
               Not DateTime.TryParse(txtEndDate.Text, endDate) Then
                ShowError("Please enter valid dates.")
                Return
            End If

            ' Adjust endDate to include the entire day
            endDate = endDate.AddDays(1).AddSeconds(-1)

            Using conn As New SqlConnection(ConfigurationManager.ConnectionStrings("ChessGameConnection").ConnectionString)
                Using cmd As New SqlCommand("sp_ChessGame_GetGameList", conn)
                    cmd.CommandType = CommandType.StoredProcedure
                    cmd.Parameters.AddWithValue("@StartDate", startDate)
                    cmd.Parameters.AddWithValue("@EndDate", endDate)
                    cmd.Parameters.AddWithValue("@PlayerName", playerName)
                    cmd.Parameters.AddWithValue("@SoloGames", soloGames)

                    conn.Open()
                    Using dt As New DataTable()
                        dt.Load(cmd.ExecuteReader())
                        gvGames.DataSource = dt
                        gvGames.DataBind()
                    End Using
                End Using
            End Using
        Catch ex As Exception
            LogError(ex)
            ShowError("Unable to load games. Please try again later.")
        End Try
    End Sub

    Protected Sub gvGames_SelectedIndexChanged(sender As Object, e As EventArgs)
        Dim gameId As String = gvGames.SelectedRow.Cells(0).Text
        LoadGameMoves(gameId)
        replaySection.Visible = True
    End Sub

    Private Sub LoadGameMoves(gameId As String)
        Try
            CurrentGame = New GameReplay()
            CurrentGame.GameId = gameId
            CurrentGame.Players = New Dictionary(Of String, String)
            CurrentGame.Moves = New List(Of MoveData)

            ' First get the game info for player data
            Using conn As New SqlConnection(ConfigurationManager.ConnectionStrings("ChessGameConnection").ConnectionString)
                Using cmd As New SqlCommand("sp_ChessGame_GetGameList", conn)
                    cmd.CommandType = CommandType.StoredProcedure
                    cmd.Parameters.AddWithValue("@StartDate", New DateTime(1753, 1, 1))
                    cmd.Parameters.AddWithValue("@EndDate", New DateTime(9999, 12, 31))
                    cmd.Parameters.AddWithValue("@PlayerName", "")
                    cmd.Parameters.AddWithValue("@SoloGames", Convert.ToInt32(rblGameType.SelectedValue) = 1)

                    conn.Open()
                    Using reader As SqlDataReader = cmd.ExecuteReader()
                        While reader.Read()
                            If reader("GameId").ToString() = gameId Then
                                CurrentGame.StartTime = Convert.ToDateTime(reader("StartTime"))
                                CurrentGame.Players.Add("Plebs", reader("Plebs").ToString())
                                If reader("Aristoi").ToString() <> "Solo" Then
                                    CurrentGame.Players.Add("Aristoi", reader("Aristoi").ToString())
                                End If
                                Exit While
                            End If
                        End While
                    End Using
                End Using

                ' Now get the moves
                Using cmd As New SqlCommand("sp_ChessGame_GetMoves", conn)
                    cmd.CommandType = CommandType.StoredProcedure
                    cmd.Parameters.AddWithValue("@GameId", gameId)

                    Using reader As SqlDataReader = cmd.ExecuteReader()
                        While reader.Read()
                            Dim move As New MoveData With {
                                .MoveNumber = Convert.ToInt32(reader("MoveNumber")),
                                .PlayerName = reader("PlayerName").ToString(),
                                .PlayerRole = reader("PlayerRole").ToString(),
                                .FromRow = Convert.ToInt32(reader("FromRow")),
                                .FromCol = Convert.ToInt32(reader("FromCol")),
                                .ToRow = Convert.ToInt32(reader("ToRow")),
                                .ToCol = Convert.ToInt32(reader("ToCol")),
                                .PieceMoved = reader("PieceMoved").ToString(),
                                .PieceCaptured = If(reader("PieceCaptured") Is DBNull.Value, Nothing, reader("PieceCaptured").ToString()),
                                .WasPromotion = Convert.ToBoolean(reader("WasPromotion")),
                                .Timestamp = Convert.ToDateTime(reader("Timestamp"))
                            }
                            CurrentGame.Moves.Add(move)
                        End While
                    End Using
                End Using
            End Using

            CurrentMoveIndex = -1
            hdnInitialGameData.Value = JsonConvert.SerializeObject(CurrentGame)
            hdnInitialMoveIndex.Value = CurrentMoveIndex.ToString()

            DisplayCurrentPosition()
            UpdateGameInfo()
        Catch ex As Exception
            LogError(ex)
            ShowError("Error loading game.")
        End Try
    End Sub

    Private Sub DisplayCurrentPosition()
        If CurrentGame Is Nothing Then Return

        ' Initialize the empty board
        Dim board(7, 7) As String
        InitializeStartingPosition(board)

        ' Apply moves up to current index
        For i As Integer = 0 To CurrentMoveIndex
            Dim move = CurrentGame.Moves(i)
            board(move.ToRow, move.ToCol) = board(move.FromRow, move.FromCol)
            board(move.FromRow, move.FromCol) = ""
        Next

        ' Update hidden field with board state for JavaScript to read
        hdnBoardState.Value = ConvertBoardToJsArray(board)


    End Sub

    Private Function ConvertBoardToJsArray(board(,) As String) As String
        Dim result As New StringBuilder("[")
        For row As Integer = 0 To 7
            If row > 0 Then result.Append(",")
            result.Append("[")
            For col As Integer = 0 To 7
                If col > 0 Then result.Append(",")
                If String.IsNullOrEmpty(board(row, col)) Then
                    result.Append("null")
                Else
                    result.Append("""").Append(board(row, col)).Append("""")
                End If
            Next
            result.Append("]")
        Next
        result.Append("]")
        Return result.ToString()
    End Function

    Private Sub DebugPrintBoard(board(,) As String)
        For row As Integer = 0 To 7
            Dim rowStr As New StringBuilder()
            For col As Integer = 0 To 7
                rowStr.Append(If(String.IsNullOrEmpty(board(row, col)), ".", board(row, col))).Append(" ")
            Next
            System.Diagnostics.Debug.WriteLine(rowStr.ToString())
        Next
    End Sub

    Private Function GetSquareName(row As Integer, col As Integer) As String
        ' Convert numerical coordinates to chess notation (e.g., 0,0 -> "a8")
        Dim colName As Char = Chr(Asc("a"c) + col)
        Dim rowName As Integer = 8 - row
        Return colName & rowName.ToString()
    End Function

    Private Function GetPieceSymbol(piece As String) As String
        Select Case piece.ToLower()
            Case "black-pawn", "white-pawn"
                Return If(piece.StartsWith("white"), "♙", "♟")
            Case "black-rook", "white-rook"
                Return If(piece.StartsWith("white"), "♖", "♜")
            Case "black-knight", "white-knight"
                Return If(piece.StartsWith("white"), "♘", "♞")
            Case "black-bishop", "white-bishop"
                Return If(piece.StartsWith("white"), "♗", "♝")
            Case "black-queen", "white-queen"
                Return If(piece.StartsWith("white"), "♕", "♛")
            Case "black-king", "white-king"
                Return If(piece.StartsWith("white"), "♔", "♚")
            Case Else
                Return ""
        End Select
    End Function

    Private Sub InitializeStartingPosition(ByRef board(,) As String)
        ' Initialize empty board
        For row As Integer = 0 To 7
            For col As Integer = 0 To 7
                board(row, col) = ""
            Next
        Next

        ' Set up initial piece positions
        ' Black pieces (back row)
        board(0, 0) = "black-rook"
        board(0, 1) = "black-knight"
        board(0, 2) = "black-bishop"
        board(0, 3) = "black-queen"
        board(0, 4) = "black-king"
        board(0, 5) = "black-bishop"
        board(0, 6) = "black-knight"
        board(0, 7) = "black-rook"

        ' Black pawns
        For col As Integer = 0 To 7
            board(1, col) = "black-pawn"
        Next

        ' White pawns
        For col As Integer = 0 To 7
            board(6, col) = "white-pawn"
        Next

        ' White pieces (back row)
        board(7, 0) = "white-rook"
        board(7, 1) = "white-knight"
        board(7, 2) = "white-bishop"
        board(7, 3) = "white-queen"
        board(7, 4) = "white-king"
        board(7, 5) = "white-bishop"
        board(7, 6) = "white-knight"
        board(7, 7) = "white-rook"

        ' Debug the initial board state
        System.Diagnostics.Debug.WriteLine("Initial board state:")
        For row As Integer = 0 To 7
            Dim rowStr As New StringBuilder()
            For col As Integer = 0 To 7
                rowStr.Append(If(String.IsNullOrEmpty(board(row, col)), ".", board(row, col))).Append(" ")
            Next
            System.Diagnostics.Debug.WriteLine(rowStr.ToString())
        Next
    End Sub

    Private Sub UpdateGameInfo()
        If CurrentGame IsNot Nothing Then
            lblGameInfo.Text = String.Format(
                "Game started on {0}<br/>Plebs: {1}{2}",
                CurrentGame.StartTime.ToString("MM/dd/yyyy HH:mm:ss"),
                CurrentGame.Players("Plebs"),
                If(CurrentGame.Players.ContainsKey("Aristoi"),
                   "<br/>Aristoi: " & CurrentGame.Players("Aristoi"),
                   "")
            )
        End If
    End Sub

    Private Sub ShowError(message As String)
        lblError.Text = message
        lblError.Visible = True
    End Sub

    Private Sub LogError(ex As Exception)
        LogToFile("Error in ChessGameReplay", New With {
            .error = ex.Message,
            .stackTrace = ex.StackTrace,
            .userId = If(Session("UserId") IsNot Nothing, Session("UserId").ToString(), "None")
        })
    End Sub

    Protected Sub Page_Init(sender As Object, e As EventArgs) Handles Me.Init
        ' Initialize the timer
        _autoPlayTimer = New Timer()
        _autoPlayTimer.Interval = 1000  ' 1 second interval
        _autoPlayTimer.Enabled = False
        AddHandler _autoPlayTimer.Tick, AddressOf _autoPlayTimer_Tick
        UpdatePanel1.ContentTemplateContainer.Controls.Add(_autoPlayTimer)
    End Sub
    Protected Sub chkAutoPlay_CheckedChanged(sender As Object, e As EventArgs) Handles chkAutoPlay.CheckedChanged
        Try
            System.Diagnostics.Debug.WriteLine("chkAutoPlay_CheckedChanged triggered - New State: " & chkAutoPlay.Checked)

            _autoPlayTimer.Enabled = chkAutoPlay.Checked

            If chkAutoPlay.Checked Then
                LogToFile("Auto-play started", New With {
                .gameId = If(CurrentGame IsNot Nothing, CurrentGame.GameId, "None"),
                .currentMove = CurrentMoveIndex
            })
            Else
                LogToFile("Auto-play stopped", New With {
                .gameId = If(CurrentGame IsNot Nothing, CurrentGame.GameId, "None"),
                .currentMove = CurrentMoveIndex
            })
            End If
        Catch ex As Exception
            LogError(ex)
            ShowError("Error toggling auto-play.")
        End Try
    End Sub

    Private Sub _autoPlayTimer_Tick(sender As Object, e As EventArgs)
        Try
            System.Diagnostics.Debug.WriteLine($"Timer tick - Current move index before: {CurrentMoveIndex}")

            If CurrentGame Is Nothing Then
                System.Diagnostics.Debug.WriteLine("Timer tick - No current game")
                _autoPlayTimer.Enabled = False
                chkAutoPlay.Checked = False
                Return
            End If

            If CurrentMoveIndex >= CurrentGame.Moves.Count - 1 Then
                System.Diagnostics.Debug.WriteLine("Timer tick - Reached end of moves")
                _autoPlayTimer.Enabled = False
                chkAutoPlay.Checked = False
                Return
            End If

            ' Increment the move index first
            CurrentMoveIndex += 1
            System.Diagnostics.Debug.WriteLine($"Timer tick - Current move index after increment: {CurrentMoveIndex}")

            ' Update the hidden field with the new move index
            hdnInitialMoveIndex.Value = CurrentMoveIndex.ToString()

            ' Display the new position
            DisplayCurrentPosition()

            ' Force the update panel to refresh
            If UpdatePanel1.UpdateMode = UpdatePanelUpdateMode.Conditional Then
                UpdatePanel1.Update()
            End If

        Catch ex As Exception
            LogError(ex)
            _autoPlayTimer.Enabled = False
            chkAutoPlay.Checked = False
            ShowError("Error during auto-play.")
        End Try
    End Sub

    ' Add to the existing Page_Unload
    Protected Sub Page_Unload(sender As Object, e As EventArgs) Handles Me.Unload
        If _autoPlayTimer IsNot Nothing Then
            _autoPlayTimer.Dispose()
        End If
    End Sub

    Protected Sub ddlPlaySpeed_SelectedIndexChanged(sender As Object, e As EventArgs) Handles ddlPlaySpeed.SelectedIndexChanged
        Try
            Dim speed As Integer
            If Integer.TryParse(ddlPlaySpeed.SelectedValue, speed) Then
                _autoPlayTimer.Interval = speed
                LogToFile("Auto-play speed changed", New With {
                    .speed = speed,
                    .gameId = If(CurrentGame IsNot Nothing, CurrentGame.GameId, "None")
                })
            End If
        Catch ex As Exception
            LogError(ex)
            ShowError("Error changing playback speed.")
        End Try
    End Sub

    Private Function GetMoveText(move As MoveData) As String
        Return String.Format("Move {0}: {1} from {2} to {3}{4}",
            CurrentMoveIndex + 2,
            move.PieceMoved,
            GetSquareNotation(move.FromRow, move.FromCol),
            GetSquareNotation(move.ToRow, move.ToCol),
            If(Not String.IsNullOrEmpty(move.PieceCaptured),
               " capturing " & move.PieceCaptured,
               String.Empty))
    End Function

    ' Helper method to convert row/column to chess notation
    Private Function GetSquareNotation(row As Integer, col As Integer) As String
        Dim files As String = "abcdefgh"
        Dim ranks As String = "87654321"
        Return files(col) & ranks(row)
    End Function

    Private Sub InitializeGameData()
        If CurrentGame IsNot Nothing Then
            System.Diagnostics.Debug.WriteLine("Initializing game data")
            hdnInitialGameData.Value = JsonConvert.SerializeObject(CurrentGame)
            hdnInitialMoveIndex.Value = CurrentMoveIndex.ToString()
            System.Diagnostics.Debug.WriteLine($"Game data: {hdnInitialGameData.Value}")
            System.Diagnostics.Debug.WriteLine($"Move index: {hdnInitialMoveIndex.Value}")
        Else
            System.Diagnostics.Debug.WriteLine("CurrentGame is Nothing")
        End If
    End Sub

    Private Sub btnPlayGame_Click(sender As Object, e As EventArgs) Handles btnPlayGame.Click
        Response.Redirect("Default.aspx", False)
    End Sub
End Class

<Serializable()>
Public Class GameReplay
    Public Property GameId As String
    Public Property StartTime As DateTime
    Public Property Players As Dictionary(Of String, String)  ' Role -> PlayerName
    Public Property Moves As List(Of MoveData)
End Class

<Serializable()>
Public Class MoveData
    Public Property MoveNumber As Integer
    Public Property PlayerName As String
    Public Property PlayerRole As String
    Public Property FromRow As Integer
    Public Property FromCol As Integer
    Public Property ToRow As Integer
    Public Property ToCol As Integer
    Public Property PieceMoved As String
    Public Property PieceCaptured As String
    Public Property WasPromotion As Boolean
    Public Property Timestamp As DateTime
End Class