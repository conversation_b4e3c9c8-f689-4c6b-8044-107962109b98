2025-05-26 17:24:39.382174 - Game[5WYHAR] - Conn[no-connection]: Player 2 join attempt
	{
  "gameIdToJoin": "5WYHAR",
  "userId": 26,
  "hubId": "ae6d48f4-0608-4e83-abc5-ee052ddbdf12"
}
2025-05-26 17:24:39.382174 - Game[5WYHAR] - Conn[no-connection]: joinGame() - STEP 4 - isFirstPlayer = false;
2025-05-26 17:24:39.394678 - Game[5WYHAR] - Conn[no-connection]: joinGame() - STEP 5 - gameHub.server.joinGame(): gameIdToJoin: 5WYHAR userId:26
2025-05-26 17:25:12.310041 - Game[5WYHAR] - Conn[no-connection]: Player 2 join failed
	{
  "error": {
    "source": "Exception"
  },
  "gameId": "5WYHAR"
}
2025-05-26 17:25:12.821617 - Game[5WYHAR] - Conn[no-connection]: Log error
	{
  "error": {
    "source": "Exception"
  },
  "message": "Object reference not set to an instance of an object."
}
2025-05-26 17:25:37.818618 - Game[5WYHAR] - Conn[no-connection]: Game ID stored in session:
	"5WYHAR"
2025-05-26 17:25:37.822618 - Game[5WYHAR] - Conn[no-connection]: Player 2 join attempt
	{
  "gameIdToJoin": "5WYHAR",
  "userId": 26,
  "hubId": "ae6d48f4-0608-4e83-abc5-ee052ddbdf12"
}
2025-05-26 17:25:37.833221 - Game[5WYHAR] - Conn[no-connection]: joinGame() - STEP 4 - isFirstPlayer = false;
2025-05-26 17:25:37.842221 - Game[5WYHAR] - Conn[no-connection]: joinGame() - STEP 5 - gameHub.server.joinGame(): gameIdToJoin: 5WYHAR userId:26
2025-05-26 17:25:37.905860 - Game[5WYHAR] - Conn[no-connection]: updateTurnMessage - Complete
2025-05-26 17:25:37.914058 - Game[5WYHAR] - Conn[no-connection]: Player 2 joined successfully
2025-05-26 17:25:37.922760 - Game[5WYHAR] - Conn[no-connection]: updateTurnMessage - Complete
2025-05-26 17:25:37.931542 - Game[5WYHAR] - Conn[no-connection]: Prison displays updated
2025-05-26 17:25:56.617451 - Game[5WYHAR] - Conn[no-connection]: Player 2 join attempt
	{
  "gameIdToJoin": "5WYHAR",
  "userId": 1,
  "hubId": "01e95070-c11d-4bbf-bb1d-c5dac3a58402"
}
2025-05-26 17:25:56.627007 - Game[5WYHAR] - Conn[no-connection]: joinGame() - STEP 4 - isFirstPlayer = false;
2025-05-26 17:25:56.627007 - Game[5WYHAR] - Conn[no-connection]: joinGame() - STEP 5 - gameHub.server.joinGame(): gameIdToJoin: 5WYHAR userId:1
