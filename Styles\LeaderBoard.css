﻿body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    font-size: 16px;
}

.container {
    display: flex;
    flex-direction: column;
    margin: 20px auto 20px auto;
    top: 20px
}

.game-title {
    font-family: '<PERSON>in<PERSON>', serif;
    color: #ffffff;
    text-align: center;
    margin: 0;
    padding: 20px 0;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    background-color: rgb(16 39 44 / 95%);
    border-bottom: 2px solid #6384b5;
}

.tab-container {
    display: flex;
    justify-content: center;
    gap: 20px;
    padding: 15px 0;
    background: rgb(105 166 183 / 95%);
    border-bottom: 2px solid #1b102c;
}

.tab-button {
    background-color: #102c1f;
    color: white;
    border: none;
    padding: 12px 24px;
    cursor: pointer;
    font-size: 1.1em;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-family: 'Palatino Linotype', serif;
    min-width: 160px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.tab-button:hover {
    background-color: #1c3f4a;
    transform: translateY(-2px);
}

.tab-button.active {
    background-color: #ffffff;
    color: #102c16;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.leaderboard-section {
    flex: 1;
    background-color: rgba(255, 255, 255, 0.95);
    padding: 30px;
    margin: 20px auto;
    max-width: 1200px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.leaderboard-title {
    font-family: 'Cinzel', serif;
    color: #102c27;
    text-align: center;
    margin-bottom: 25px;
    font-size: 1.8em;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.stat-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-top: 15px;
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.stat-table th {
    background-color: #254f70;
    color: white;
    padding: 15px;
    text-align: left;
    font-weight: bold;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
}

.stat-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #e0e0e0;
}

.stat-table tr:last-child td {
    border-bottom: none;
}

.stat-table tr:hover {
    background-color: rgba(181, 136, 99, 0.1);
}

.info-section_lb {
    text-align: center;
    padding: 12px;
    background-color: rgb(0, 75, 99, 0.20);
}

.lnkbtn {
    display: inline-block;
    padding: 12px 24px;
    background-color: #b58863;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-family: 'Palatino Linotype', serif;
    font-size: 1.1em;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.lnkbtn:hover {
    background-color: #8b5e3c;
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Medal styles */
.medal {
    font-size: 2em;
    margin-right: 5px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .game-title {
        font-size: 1.8em;
        padding: 15px 10px;
    }

    .tab-container {
        flex-direction: column;
        gap: 10px;
        padding: 15px;
    }

    .tab-button {
        width: 100%;
        min-width: unset;
    }

    .leaderboard-section {
        margin: 10px;
        padding: 15px;
    }

    .stat-table {
        font-size: 0.9em;
    }
}
