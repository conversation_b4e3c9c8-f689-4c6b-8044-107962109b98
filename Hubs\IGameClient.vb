Option Strict On
Option Explicit On

Imports System.Threading.Tasks

Namespace PvAChess.Hubs
    Public Interface IGameClient
        Function PlayerJoined(data As Object) As Task
        Function UpdateGameState(state As Object) As Task
        Function ReceiveChatMessage(connectionId As String, message As String) As Task
        Function VictoryAnnounced(endGameAction As String) As Task
        Function InitializeNewGame() As Task
        Function PlayerDisconnected() As Task
        Function ReceiveMessage(message As String) As Task
        Sub playerReconnected(userId As Integer)
        Sub secondPlayerJoined(userId As Integer)
        Sub NotifyPlayerDisconnected(userId As Integer)
    End Interface
End Namespace