USE [ElthosChess]
GO
/****** Object:  StoredProcedure [dbo].[sp_ChessGame_AdminResetGameTables]    Script Date: 4/5/2025 1:17:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [dbo].[sp_ChessGame_AdminResetGameTables]
AS
BEGIN
/*
============================================================================
AUTHOR:    MARK ABRAMS
DATE:      1/3/2025
PURPOSE:   RESET CHES GAMES AND MOVES TABLES
============================================================================

USAGE:

   EXEC sp_ChessGame_AdminResetGameTables

============================================================================
BACKUP QUERIES
    
	SELECT * INTO DBO.[ChessGames_BK] FROM [ChessGames]
	SELECT * INTO DBO.[ChessMoves_BK] FROM [ChessMoves] 
============================================================================
*/

    SET NOCOUNT ON;

	BEGIN TRY
        BEGIN TRANSACTION
            -- Delete all moves first (child table)
            DELETE FROM [dbo].[ChessMoves]
            
            -- Delete all games (parent table)
            DELETE FROM [dbo].[ChessGames]
            
            -- Reset the identity on ChessMoves
            DBCC CHECKIDENT ('[dbo].[ChessMoves]', RESEED, 0)
            
            -- Log the reset
            PRINT 'Chess game tables reset successfully at ' + CONVERT(VARCHAR, GETUTCDATE(), 120)
            
        COMMIT TRANSACTION
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION
            
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE()
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY()
        DECLARE @ErrorState INT = ERROR_STATE()
        
        RAISERROR (@ErrorMessage, @ErrorSeverity, @ErrorState)
    END CATCH
END
GO
