const PAWN_STATES = {
    NORMAL: 'normal',
    RADICAL: 'radical',
    SUPER: 'super'
};

const pieceSymbols = {
    'white-king': '♔',
    'white-queen': '♕',
    'white-rook': '♖',
    'white-bishop': '♗',
    'white-knight': '♘',
    'white-pawn': '♙',
    'black-king': '♚',
    'black-queen': '♛',
    'black-rook': '♜',
    'black-bishop': '♝',
    'black-knight': '♞',
    'black-pawn': '♟'
};

// Enum for move types
const MOVE_TYPE = {
    NORMAL: 'normal',
    CAPTURE: 'capture',
    SWAP: 'swap'
};

function clearHighlights() {
    const squares = document.querySelectorAll('.square');
    squares.forEach(square => {
        square.classList.remove('selected');
        square.classList.remove('possible-move', 'capturable-piece','swappable-pawn');
    });
}

function highlightSquare(row, col) {
    const squares = document.querySelectorAll('.square');
    squares.forEach(square => {
        if (parseInt(square.dataset.row) === row && parseInt(square.dataset.col) === col) {
            square.classList.add('selected');
        }
    });
}

//function highlightPossibleMoves(row, col) {
//    // Clear any existing capturable highlights first
//    document.querySelectorAll('.capturable-piece').forEach(square => {
//        square.classList.remove('capturable-piece');
//    });
//
//    // Check each square on the board
//    for (let i = 0; i < 8; i++) {
//        for (let j = 0; j < 8; j++) {
//            if (isValidMove(row, col, i, j)) {
//                const square = document.querySelector(`.square[data-row="${i}"][data-col="${j}"]`);
//                if (square) {
//                    square.classList.add('possible-move');
//
//                    // If there's a piece that can be captured, add the capturable-piece class
//                    if (board[i][j]) {
//                        square.classList.add('capturable-piece');
//                    }
//                }
//            }
//        }
//    }
//}

function highlightPossibleMoves(row, col) {
    // Clear existing highlights
    document.querySelectorAll('.capturable-piece, .swappable-pawn').forEach(square => {
        square.classList.remove('capturable-piece', 'swappable-pawn');
    });

    const movingPiece = board[row][col];
    const isPawn = movingPiece?.endsWith('pawn');
    const movingColor = movingPiece?.split('-')[0];

    for (let i = 0; i < 8; i++) {
        for (let j = 0; j < 8; j++) {
            if (isValidMove(row, col, i, j)) {
                const square = document.querySelector(`.square[data-row="${i}"][data-col="${j}"]`);
                if (!square) continue;

                square.classList.add('possible-move');

                const targetPiece = board[i][j];
                if (targetPiece) {
                    // Handle pawn swap highlights
                    if (isPawn && targetPiece.endsWith('pawn')) {

                        square.classList.add('swappable-pawn');
                        continue;

                    }

                    // Default capture highlight
                    square.classList.add('capturable-piece');
                }
            }
        }
    }
}
function deselectPiece() {
    clearHighlights();
    selectedPiece = null;

    // Re-highlight valid pieces for the current turn
    const squares = document.querySelectorAll('.square');
    squares.forEach(square => {
        const row = parseInt(square.dataset.row);
        const col = parseInt(square.dataset.col);
        const piece = board[row][col];

        if (piece) {
            const isPawn = piece.includes('pawn');
            if (!isSoloPlay) {
                const canMoveCurrentPiece = isFirstPlayer ?
                    (window.firstPlayerTurnValue ?
                        (isPlebsTurn && isPawn) :
                        (!isPlebsTurn && !isPawn)
                    ) :
                    (window.firstPlayerTurnValue ?
                        (!isPlebsTurn && !isPawn) :
                        (isPlebsTurn && isPawn)
                    );

                if (canMoveCurrentPiece) {
                    square.classList.add('selectable');
                }
            }
        }
    });
}

// Chess Animation Functions
//function animateMove(fromRow, fromCol, toRow, toCol, isCapture, pieceMoved, pieceCaptured) {

//    // --------------------------------------------------------------------------------------------------------------------------------------
//    // -- APPARENTLY ONLY THE CHESS REPLAY PAGE USES THIS - BUT NOT DEFAULT ASPX WHICH HANDLES ANIMATING MOVES INDEPENDENTLY FOR SOME REASON.
//    // --------------------------------------------------------------------------------------------------------------------------------------
//    console.log('Animating move:', {
//        fromRow,
//        fromCol,
//        toRow,
//        toCol,
//        isCapture,
//        pieceMoved,
//        pieceCaptured
//    });

//    const sourceSquare = document.querySelector(`.square[data-row="${fromRow}"][data-col="${fromCol}"]`);
//    const destSquare = document.querySelector(`.square[data-row="${toRow}"][data-col="${toCol}"]`);

//    if (!sourceSquare || !destSquare) {
//        console.error('Could not find squares for move animation');
//        return;
//    }

//    // Check if this is a pawn-pawn capture (swap scenario)
//    const isPawnSwap = isCapture &&
//        pieceMoved?.toLowerCase().includes('pawn') &&
//        pieceCaptured?.toLowerCase().includes('pawn');

//    // Store the captured piece information
//    const capturedPiece = destSquare.textContent;
//    const capturedPieceClasses = Array.from(destSquare.classList)
//        .filter(c => c.includes('-piece') || c.includes('pawn'));

//    // Get the moving piece information
//    const movingPiece = sourceSquare.textContent;
//    const movingPieceClasses = Array.from(sourceSquare.classList)
//        .filter(c => c.includes('-piece') || c.includes('pawn'));

//    // Clear source square
//    sourceSquare.textContent = '';
//    sourceSquare.className = `square ${(parseInt(fromRow) + parseInt(fromCol)) % 2 === 0 ? 'white' : 'black'}`;

//    // If it's a pawn swap, move the captured pawn to the source square
//    if (isPawnSwap) {
//        console.log('Handling pawn swap:', {
//            moving: pieceMoved,
//            captured: pieceCaptured,
//            sourceSquare: `${fromRow},${fromCol}`,
//            destSquare: `${toRow},${toCol}`
//        });

//        sourceSquare.textContent = capturedPiece;
//        capturedPieceClasses.forEach(cls => sourceSquare.classList.add(cls));
//    }

//    // Update destination square
//    destSquare.textContent = movingPiece;
//    destSquare.className = `square ${(parseInt(toRow) + parseInt(toCol)) % 2 === 0 ? 'white' : 'black'}`;
//    movingPieceClasses.forEach(cls => destSquare.classList.add(cls));

//    console.log('Play Sounds...');

//    // Play appropriate sound effect
//    if (isPawnSwap) {
//        console.log('isPawnSwap');
//        const swapSound = document.getElementById('swapSound');
//        if (swapSound) {
//            swapSound.currentTime = 0;
//            swapSound.play().catch(err => console.log('Error playing move sound:', err));
//        }
//    } else if (isCapture) {
//        console.log('isCapture');
//        const captureSound = document.getElementById('captureSound');
//        if (captureSound) {
//            captureSound.currentTime = 0;
//            captureSound.play().catch(err => console.log('Error playing capture sound:', err));
//        }
//    } else {
//        console.log('moveSound');
//        const moveSound = document.getElementById('moveSound');
//        if (moveSound) {
//            moveSound.currentTime = 0;
//            moveSound.play().catch(err => console.log('Error playing move sound:', err));
//        }
//    }
//    console.log('visual effects...')
//    // Add visual effects
//    playMoveEffects(isPawnSwap ? 'swap' : (isCapture ? 'capture' : 'move'), destSquare);
//}


// UpdatePanel Support
function initializeChessAnimations() {
    if (typeof (Sys) !== 'undefined' && Sys.WebForms && Sys.WebForms.PageRequestManager) {
        var prm = Sys.WebForms.PageRequestManager.getInstance();

        prm.add_endRequest(function () {
            // Re-initialize any page-specific handlers if needed
            if (typeof onChessAnimationsReady === 'function') {
                onChessAnimationsReady();
            }
        });
    }
}



// Call this when the document is ready
document.addEventListener('DOMContentLoaded', function () {
    initializeChessAnimations();

    // Call page-specific initialization if it exists
    if (typeof onChessAnimationsReady === 'function') {
        onChessAnimationsReady();
    }
});

function playSounds(targetPiece) {

    console.log('Common JS - playSounds() - targetPiece: ', targetPiece);
    // this may be redundant - let's return and see

    return;

    if (targetPiece) {
        const captureSound = document.getElementById('captureSound');
        if (captureSound) {
            captureSound.currentTime = 0;
            captureSound.play().catch(err => console.log('Common JS - playSounds() - Error playing capture sound:', err));
        }
    } else {
        const moveSound = document.getElementById('moveSound');
        if (moveSound) {
            moveSound.currentTime = 0;
            moveSound.play().catch(err => console.log('Common JS - playSounds() - Error playing move sound:', err));
        }
    }
}

function playMoveEffects(moveType, destSquare) {
    console.log('Common JS - playMoveEffects() - Playing move effects - params:', { moveType, destSquare: destSquare });

    switch (moveType) {
        case 'swap':
            destSquare.classList.add('swap-highlight');
            setTimeout(() => destSquare.classList.remove('swap-highlight'), 1000);
            break;
        case 'capture':
            destSquare.classList.add('capture-highlight');
            setTimeout(() => destSquare.classList.remove('capture-highlight'), 1000);
            break;
        default:
            destSquare.classList.add('move-highlight');
            setTimeout(() => destSquare.classList.remove('move-highlight'), 1000);
    }
}

function initializeGameData(gameData, moveIndex) {
    window.gameData = gameData;
    window.currentMoveIndex = moveIndex;
}

function updateBoardDisplay(boardState) {
    window.boardState = boardState;
    renderChessBoard(boardState, 'chessBoard');
}

function initializeStartingPosition() {
    const board = Array(8).fill(null).map(() => Array(8).fill(null));

    // Set up pawns
    for (let col = 0; col < 8; col++) {
        board[1][col] = 'black-pawn';
        board[6][col] = 'white-pawn';
    }

    // Set up other pieces
    const backRow = ['rook', 'knight', 'bishop', 'queen', 'king', 'bishop', 'knight', 'rook'];

    for (let col = 0; col < 8; col++) {
        board[0][col] = 'black-' + backRow[col];
        board[7][col] = 'white-' + backRow[col];
    }

    return board;
}

function renderBoard() {
    //clientLogToFile('renderBoard start...', {
    //    isSoloPlay: isSoloPlay,
    //    soloCheckboxState: document.getElementById('soloPlayCheck')?.checked,
    //    boardState: board.length > 0 ? 'initialized' : 'empty'
    //});

    console.log('ChessCommon.js - INSIDE renderBoard()');

    const boardElement = document.getElementById('board');
    boardElement.innerHTML = '';

    for (let i = 0; i < 8; i++) {
        for (let j = 0; j < 8; j++) {
            const square = document.createElement('div');
            const piece = board[i][j];

            square.className = `square ${(i + j) % 2 === 0 ? 'white' : 'black'}`;
            if (piece) {
                square.textContent = pieceSymbols[piece];
                if (piece.startsWith('white')) {
                    square.classList.add('white-piece');
                } else {
                    square.classList.add('black-piece');
                }
                // Add radicalized class for pawns based on their state, not position
                if (piece.endsWith('pawn')) {
                    const pawnState = pawnStates.get(`${i},${j}`);

                    // clientLogToFile('Render Board - Pawn Display', {
                    //     position: `${i},${j}`,
                    //     piece: piece,
                    //     state: pawnState,
                    //     allStates: Array.from(pawnStates.entries())
                    // });

                    // Update visual state based on pawn state
                    if (pawnState === PAWN_STATES.RADICAL) {
                        square.classList.add('radicalized-pawn');
                    } else if (pawnState === PAWN_STATES.SUPER) {
                        square.classList.add('promoted-pawn');
                    }
                }
            }

            square.dataset.row = i;
            square.dataset.col = j;

            square.addEventListener('click', function (event) {
                const soloPlayCheck = document.getElementById('soloPlayCheck');
                isSoloPlay = soloPlayCheck.checked;
                if (isSoloPlay) {
                    handleSquareClick_Solo(event);
                } else {
                    handleSquareClick(event);
                }
                clientLogToFile('set soloPlayCheck-addEventListener: ' + isSoloPlay)
            });

            boardElement.appendChild(square);
        }
    }
    updatePrisons();
}

// Centralized sound management
function playChessSound(moveType) {
    console.log('ChessCommon.js - playChessSound()');

    const moveSound = document.getElementById('moveSound');
    const captureSound = document.getElementById('captureSound');
    const swapSound = document.getElementById('swapSound');

    switch (moveType) {
        case MOVE_TYPE.SWAP:
            swapSound?.play();
            console.log('ChessCommon.js - swapSound')
            break;
        case MOVE_TYPE.CAPTURE:
            captureSound?.play();
            console.log('ChessCommon.js - captureSound')
            break;
        case MOVE_TYPE.NORMAL:
            moveSound?.play();
            console.log('ChessCommon.js - captureSound')
            break;
    }
}

// Helper function to determine move type
function determineMoveType(sourcePiece, targetPiece) {
    const sourceIsPawn = sourcePiece?.toLowerCase().includes('pawn');
    const targetIsPawn = targetPiece?.toLowerCase().includes('pawn');

    if (sourceIsPawn && targetIsPawn) {
        return MOVE_TYPE.SWAP;
    } else if (targetPiece) {
        return MOVE_TYPE.CAPTURE;
    }
    return MOVE_TYPE.NORMAL;
}
