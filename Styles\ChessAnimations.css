/* Chess Piece Animations */

.possible-move {
    position: relative;
}

    .possible-move::before {
        background-color: rgba(152, 251, 152, 0.4);
        bottom: 0;
        content: '';
        left: 0;
        pointer-events: none;
        position: absolute;
        right: 0;
        top: 0;
        z-index: -1;
    }


/* -- MOVEMENT -- */
.move-highlight {
    position: relative;
}

    .move-highlight::before {
        animation: moveHighlight 1.75s ease-out forwards;
        content: '';
        height: 100%;
        left: 0;
        pointer-events: none;
        position: absolute;
        top: 0;
        width: 100%;
        z-index: -1;
    }

@keyframes moveHighlight {
    0% {
        background-color: rgba(255, 255, 0, 0.4);
        transform: scale(1.1);
    }

    50% {
        background-color: rgba(255, 255, 0, 0.2);
        transform: scale(1.05);
    }

    100% {
        background-color: transparent;
        transform: scale(1);
    }
}

/* -- CAPTURE -- */

.capture-highlight {
    position: relative;
}

    .capture-highlight::before {
        animation: moveHighlight 1.75s ease-out forwards;
        content: '';
        height: 100%;
        left: 0;
        pointer-events: none;
        position: absolute;
        top: 0;
        width: 100%;
        z-index: -1;
    }

@keyframes captureHighlight {
    0% {
        background-color: rgba(255, 0, 0, 0.4);
        transform: scale(1.1);
        box-shadow: 0 0 20px red;
    }

    50% {
        background-color: rgba(155, 0, 0, 0.2);
        transform: scale(1.05);
        box-shadow: 0 0 10px red;
    }

    100% {
        background-color: transparent;
        transform: scale(1);
        box-shadow: none;
    }
}


/* -- SWAP -- */

.swap-highlight {
    position: relative;
}

    .swap-highlight::before {
        animation: moveHighlight 1.75s ease-out forwards;
        content: '';
        height: 100%;
        left: 0;
        pointer-events: none;
        position: absolute;
        top: 0;
        width: 100%;
        z-index: -1;
    }

@keyframes swapHighlight {
    0% {
        background-color: rgba(0, 155, 155, 0.4);
        transform: scale(1.1);
        box-shadow: 0 0 20px navy;
    }

    50% {
        background-color: rgba(0, 155, 155, 0.2);
        transform: scale(1.05);
        box-shadow: 0 0 10px navy;
    }

    100% {
        background-color: transparent;
        transform: scale(1);
        box-shadow: none;
    }
}

/*-- CAPTURABLE PIECES --*/

.capturable-piece {
    position: relative;
}

    .capturable-piece::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border: 2px solid #4CAF50;
        border-radius: 50px;
        box-shadow: inset 0 0 10px rgba(76, 175, 80, 0.5);
        pointer-events: none;
        z-index: -1;
        animation: capturablePulse 2s infinite ease-in-out;
    }


@keyframes capturablePulse {
    0% {
        border-color: #4CAF50;
        box-shadow: inset 0 0 10px rgba(76, 175, 80, 0.5);
    }

    50% {
        border-color: #45a049;
        box-shadow: inset 0 0 15px rgba(76, 175, 80, 0.3);
    }

    100% {
        border-color: #4CAF50;
        box-shadow: inset 0 0 10px rgba(76, 175, 80, 0.5);
    }
}

/* -- SWAPPABLE PAWNS -- */
.swappable-pawn {
    position: relative;
}

    .swappable-pawn::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border: 2px solid #4CAF50;
        border-radius: 50px;
        box-shadow: inset 0 0 10px rgb(76, 111, 175, 0.50);
        pointer-events: none;
        z-index: -1;
        animation: swappablePulse 2s infinite ease-in-out;
    }

@keyframes swappablePulse {
    0% {
        border-color: rgb(5, 114, 178);
        box-shadow: inset 0 0 10px rgb(76, 133, 175, 0.50);
    }

    50% {
        border-color: rgb(5, 114, 178);
        box-shadow: inset 0 0 15px rgb(76, 145, 175, 0.30 );
    }

    100% {
        border-color: rgb(5, 114, 178);
        box-shadow: inset 0 0 10px rgb(76, 157, 175, 0.50 );
    }
}

 