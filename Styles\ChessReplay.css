/* Consolidated CSS File */

#ddlPlaySpeed {
    background-color: #2a2a2a;
    border: 1px solid #444;
    border-radius: 3px;
    color: white;
    padding: 5px;
}

.auto-play-controls {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 15px;
    justify-content: center;
    margin: 10px 0;
    margin-top: 10px;
    max-width: 100%;
    width: 100%;
}

.center-column {
    flex: 0 0 500px;
}

    .center-column .replay-controls {
        background-color: rgba(0, 0, 0, 0.7);
        border-radius: 5px;
        margin-top: 60px;
        padding: 15px;
        text-align: center;
    }

.chess-board {
    height: 500px;
    margin: 0 auto;
    width: 500px;
}

.chess-cell {
    height: 62.5px;
    width: 62.5px;
}

.control-button {
    background-color: #2a2a2a;
    border: 1px solid #444;
    border-radius: 3px;
    color: white;
    cursor: pointer;
    height: 40px;
    margin: 0 5px;
    min-width: 40px;
}

    .control-button:hover {
        background-color: #3a3a3a;
    }

.controls-panel {
    align-items: center;
    display: flex;
    gap: 10px;
    justify-content: center;
    margin: 20px 0;
}

.current-move {
    background-color: rgba(255, 255, 0, 0.3) !important;
}

.date-range {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

    .date-range input[type="date"] {
        width: 100%;
    }

.filter-button {
    background-color: #4a90e2;
    border: none;
    border-radius: 4px;
    color: white;
    cursor: pointer;
    display: block;
    font-size: 14px;
    height: 36px;
    margin: 12px;
    padding: 8px 16px;
    position: relative;
    right: -10%;
    top: 30px;
    transition: background-color 0.3s;
}

    .filter-button:hover {
        background-color: #357abd;
    }

.filter-controls {
    display: flex;
    gap: 10px;
}

.game-info {
    margin-bottom: 15px;
}

.game-info-text, .current-move-text {
    display: block;
    font-family: 'Crimson Text', serif;
    margin: 5px 0;
}

.game-selection-panel {
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 5px;
    box-sizing: border-box;
    color: #fff;
    margin: 20px;
    max-width: 100%;
    padding: 15px;
    width: 100%;
}

.games-grid {
    background-color: rgba(0, 0, 0, 0.5);
    border-collapse: collapse;
    color: #fff;
    font-size: 0.9em;
    margin-top: 12px;
    max-width: 100%;
    width: 100%;
}

    .games-grid td {
        border: 1px solid #444;
        padding: 8px;
    }

    .games-grid th {
        background-color: #2a2a2a;
        border: 1px solid #444;
        padding: 8px;
        text-align: left;
    }

    .games-grid th,
    .games-grid td {
        padding: 6px 4px;
        text-align: left;
        word-break: break-word;
    }

    .games-grid tr:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

.left-column {
    flex: 0 0 640px;
}

.left-column, .center-column, .right-column {
    padding: 15px;
}

.main-container {
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    gap: 20px;
    padding: 10px;
    width: 100%;
}

.nav-button {
    font-size: 1.2em;
    padding: 8px 16px;
}

.navigation-controls {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin: 10px 0;
    margin-bottom: 15px;
    max-width: 100%;
    width: 100%;
}

.replay-controls {
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 5px;
    color: white;
    margin: 20px 0;
    padding: 15px;
}

.replay-link {
    color: #3498db;
    text-decoration: none;
}

    .replay-link:hover {
        color: #2980b9;
        text-decoration: underline;
    }

.right-column {
    flex: 0 0 200px;
}

    .right-column .game-info {
        background-color: rgba(0, 0, 0, 0.7);
        border-radius: 5px;
        color: white;
        margin-top: 60px;
        padding: 15px;
    }

body {
    font-family: Arial, sans-serif;
    margin: 0;
    max-width: 100vw;
    overflow-x: hidden;
    padding: 0;
}

h2 {
    color: #fff;
    font-family: 'Cinzel', serif;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

label {
    color: #fff;
    font-family: 'Crimson Text', serif;
}

select, input[type="date"] {
    background-color: #2a2a2a;
    border: 1px solid #444;
    border-radius: 3px;
    color: white;
    padding: 5px;
}

    select:focus, input[type="date"]:focus {
        border-color: #c41e3a;
        outline: none;
    }

@media screen and (min-width: 768px) {
    .main-container {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
    }

    .chess-board {
        width: 500px;
    }

    .auto-play-controls {
        flex-direction: row;
    }

    .filter-controls {
        flex-direction: row;
        flex-wrap: wrap;
    }
}

@media screen and (max-width: 480px) {
    .nav-button {
        padding: 6px 12px;
        font-size: 1em;
    }

    select, input[type="date"] {
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
    }

    .games-grid th:first-child,
    .games-grid td:first-child {
        display: none;
    }

    .games-grid th:nth-child(2),
    .games-grid td:nth-child(2) {
        font-size: 0.8em;
    }

    .games-grid {
        font-size: 0.8em;
    }


        .games-grid th:last-child,
        .games-grid td:last-child {
            width: 60px;
            min-width: 60px;
            text-align: center;
        }


        .games-grid th:nth-child(2),
        .games-grid td:nth-child(2) {
            width: 25%;
        }

        .games-grid th {
            white-space: nowrap;
        }
}

@media screen and (max-width: 740px) {
    body {
        min-width: initial;
    }

    #btnLeaderBoard {
        visibility: hidden;
        width: 0px;
        right: 1000px;
        position: static;
        margin-right: 0px;
    }

    .UserDisplay {
        max-width: min-content;
    }

    .main-container {
        flex-direction: column;
    }

    .left-column,
    .center-column,
    .right-column {
        flex: none;
        width: 100%;
    }


    .center-column {
        order: 1;
        width: 100% !important;
        margin: 0 auto;
        margin-top: 30px;
    }

    .chess-board {
        position: static !important;
        margin: 0 auto !important;
        transform: scale(0.9) !important;
        transform-origin: top center !important;
        width: fit-content !important;
    }

    .chess-cell {
        width: 62.5px;
        height: 62.5px;
    }


    .replay-controls {
        order: 2;
        margin: 10px auto !important;
        padding: 15px !important;
    }


    .left-column {
        order: 3;
        width: 100% !important;
    }

    .game-selection-panel {
        margin: 10px !important;
        width: 90% !important;
    }


    .right-column {
        display: none;
    }


    .filter-controls {
        flex-direction: column;
        gap: 10px;
    }

    .date-range {
        flex-direction: column;
        align-items: flex-start;
    }

    .header-controls {
        display: flex;
        gap: 5px;
        position: fixed;
        right: 0px;
        top: 6px;
        z-index: 1000;
        width: 90%;
    }
}

@media screen and (max-width: 360px) {
    .chess-board {
        transform: scale(0.8) !important;
    }

    .nav-button,
    .control-button {
        padding: 8px;
        min-width: 36px;
        height: 36px;
    }

    .auto-play-controls {
        flex-direction: column;
        align-items: stretch;
    }

    #ddlPlaySpeed {
        width: 100%;
    }
}

@media screen and (max-width: 740px) {
    .left-column {
        position: static;
        width: 100%;
        max-height: none;
    }

    .center-column {
        margin-left: 0;
    }
}
