<!DOCTYPE html>
<html>
<head>
    <title>CSS Class Usage Analyzer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .instructions {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f0f0f0;
            border-radius: 5px;
        }
        .results {
            margin-top: 20px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .file-inputs {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 20px;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="instructions">
            <h2>CSS Class Usage Analyzer</h2>
            <p>1. Select all relevant files (CSS, ASPX, VB)</p>
            <p>2. Click Analyze to generate a report of class usage</p>
            <p>3. Click Run Process to create optimized CSS files</p>
        </div>
        <div class="file-inputs">
            <input type="file" id="files" multiple accept=".css,.aspx,.vb">
        </div>
        <div class="button-group">
            <button id="analyzeBtn">Analyze Files</button>
            <button id="processBtn" disabled>Run Process</button>
        </div>
        <div id="results" class="results"></div>
    </div>

    <script>
        const classUsageMap = new Map(); // className -> [{file, lineNumber, context}]
        const cssContentMap = new Map(); // fileName -> content
        const cssDefinitionsMap = new Map(); // className -> [{file, lineNumber, definition}]
        const inlineStylesMap = new Map(); // fileName -> [{lineNumber, style}]
        let recommendations = new Map(); // className -> targetFile

        document.getElementById('analyzeBtn').addEventListener('click', async () => {
            const files = document.getElementById('files').files;
            classUsageMap.clear();
            cssContentMap.clear();
            cssDefinitionsMap.clear();
            inlineStylesMap.clear();
            recommendations.clear();
            
            for (const file of files) {
                const text = await file.text();
                const fileName = file.name;
                
                if (fileName.endsWith('.css')) {
                    cssContentMap.set(fileName, text);
                    analyzeCSSFile(text, fileName);
                } else if (fileName.endsWith('.aspx')) {
                    analyzeASPXFile(text, fileName);
                } else if (fileName.endsWith('.vb')) {
                    analyzeVBFile(text, fileName);
                }
            }
            
            displayResults();
            document.getElementById('processBtn').disabled = false;
        });

        document.getElementById('processBtn').addEventListener('click', () => {
            generateOptimizedFiles();
        });

        function analyzeCSSFile(text, fileName) {
            const ruleRegex = /\.([a-zA-Z0-9_-]+)\s*{([^}]*)}/g;
            let match;
            
            text.split('\n').forEach((line, index) => {
                while ((match = ruleRegex.exec(line)) !== null) {
                    const className = match[1];
                    const definition = match[0];
                    addClassUsage(className, fileName, index + 1, 'CSS Definition');
                    addClassDefinition(className, fileName, index + 1, definition);
                }
            });
        }

        function analyzeASPXFile(text, fileName) {
            const classRegex = /class=["']([^"']+)["']/g;
            const cssClassRegex = /CssClass=["']([^"']+)["']/g;
            const inlineStyleRegex = /style=["']([^"']+)["']/g;
            
            // Process style tags first
            const styleTagRegex = /<style[^>]*>([\s\S]*?)<\/style>/g;
            let styleMatch;
            while ((styleMatch = styleTagRegex.exec(text)) !== null) {
                const styleContent = styleMatch[1];
                // Store this content as if it were a CSS file
                if (!cssContentMap.has(fileName)) {
                    cssContentMap.set(fileName, '');
                }
                cssContentMap.set(fileName, cssContentMap.get(fileName) + styleContent);
                analyzeCSSContent(styleContent, fileName, 'style tag');
            }
            
            text.split('\n').forEach((line, index) => {
                let match;
                // Check for HTML classes
                while ((match = classRegex.exec(line)) !== null) {
                    match[1].split(' ').forEach(className => {
                        if (className) {
                            addClassUsage(className, fileName, index + 1, 'HTML class');
                        }
                    });
                }
                
                // Check for ASP.NET CssClass
                while ((match = cssClassRegex.exec(line)) !== null) {
                    match[1].split(' ').forEach(className => {
                        if (className) {
                            addClassUsage(className, fileName, index + 1, 'ASP.NET CssClass');
                        }
                    });
                }

                // Check for inline styles
                while ((match = inlineStyleRegex.exec(line)) !== null) {
                    addInlineStyle(fileName, index + 1, match[1]);
                }
            });
        }

        function analyzeVBFile(text, fileName) {
            const cssClassRegex = /\.CssClass\s*=\s*["']([^"']+)["']/g;
            const styleRegex = /\.Style\.[A-Za-z]+\s*=\s*["']([^"']+)["']/g;
            
            text.split('\n').forEach((line, index) => {
                let match;
                while ((match = cssClassRegex.exec(line)) !== null) {
                    match[1].split(' ').forEach(className => {
                        if (className) {
                            addClassUsage(className, fileName, index + 1, 'VB Code');
                        }
                    });
                }

                // Check for programmatic style assignments
                while ((match = styleRegex.exec(line)) !== null) {
                    addInlineStyle(fileName, index + 1, `Programmatic style: ${match[0]}`);
                }
            });
        }

        function addClassUsage(className, fileName, lineNumber, context) {
            if (!classUsageMap.has(className)) {
                classUsageMap.set(className, []);
            }
            classUsageMap.get(className).push({ fileName, lineNumber, context });
        }

        function addClassDefinition(className, fileName, lineNumber, definition) {
            console.log(`Adding definition for ${className} from ${fileName}`);
            console.log(`Definition: ${definition}`);
            
            if (!cssDefinitionsMap.has(className)) {
                cssDefinitionsMap.set(className, []);
            }
            cssDefinitionsMap.get(className).push({
                fileName,
                lineNumber,
                definition
            });
        }

        function addInlineStyle(fileName, lineNumber, style) {
            if (!inlineStylesMap.has(fileName)) {
                inlineStylesMap.set(fileName, []);
            }
            inlineStylesMap.get(fileName).push({ lineNumber, style });
        }

        function findDuplicateClassDefinitions() {
            const duplicates = [];
            for (const [className, definitions] of cssDefinitionsMap) {
                if (definitions.length > 1) {
                    duplicates.push({
                        className,
                        locations: definitions.map(d => `${d.fileName}:${d.lineNumber}`)
                    });
                }
            }
            return duplicates;
        }

        function makeRecommendations() {
            const recommendations = new Map();
            
            // Process each class usage
            for (const [className, usages] of classUsageMap) {
                console.log(`Making recommendation for ${className}`);
                
                // Check if this class is used in multiple files
                const isMultiFile = usages.some(usage => usage.fileName.includes('Default.aspx')) &&
                                   usages.some(usage => usage.fileName.includes('ChessGameReplay.aspx'));

                // Determine target file based on usage patterns
                let targetFile;
                if (className.includes('replay') || 
                    usages.every(usage => usage.fileName.includes('ChessGameReplay'))) {
                    targetFile = 'GamesReplay.css';
                } else {
                    targetFile = 'PvAChess.css';
                }

                // Store recommendation
                recommendations.set(className, {
                    targetFile,
                    reason: isMultiFile ? 'Used in multiple files' : 'Gameplay-related class'
                });
                
                console.log(`Recommended ${className} for ${targetFile}`);
            }

            return recommendations;
        }

        function isGameplayClass(className) {
            // Add specific checks for gameplay-related classes
            const gameplayPatterns = [
                /chess/i,
                /board/i,
                /piece/i,
                /player/i,
                /turn/i,
                /move/i
            ];
            
            return gameplayPatterns.some(pattern => pattern.test(className));
        }

        function findOriginalDefinition(className) {
            const definitions = cssDefinitionsMap.get(className) || [];
            return definitions.length > 0 ? definitions[0].fileName : null;
        }

        function displayResults() {
            const results = document.getElementById('results');
            let output = 'CSS Class Usage Analysis\n\n';
            
            // Get recommendations
            const classRecommendations = makeRecommendations();
            
            // Display recommendations with warnings
            output += 'RECOMMENDATIONS:\n';
            for (const [className, rec] of classRecommendations) {
                output += `${className}: ${rec.targetFile} (${rec.reason})`;
                if (rec.warning) {
                    output += ' ⚠️ Review needed';
                }
                output += '\n';
            }

            // Display duplicate classes
            const duplicates = findDuplicateClassDefinitions();
            if (duplicates.length > 0) {
                output += 'DUPLICATE CLASS DEFINITIONS FOUND:\n';
                duplicates.forEach(({className, locations}) => {
                    output += `Class "${className}" defined in multiple locations:\n`;
                    locations.forEach(loc => output += `  ${loc}\n`);
                });
                output += '\n';
            }

            // Display inline styles
            if (inlineStylesMap.size > 0) {
                output += 'INLINE STYLES FOUND:\n';
                for (const [fileName, styles] of inlineStylesMap) {
                    output += `${fileName}:\n`;
                    styles.forEach(({lineNumber, style}) => {
                        output += `  Line ${lineNumber}: ${style}\n`;
                    });
                }
                output += '\n';
            }

            // Class usage analysis
            const sortedClasses = Array.from(classUsageMap.keys()).sort();
            sortedClasses.forEach(className => {
                const usages = classUsageMap.get(className);
                output += `Class: ${className}\n`;
                output += `Total Usage: ${usages.length}\n`;
                output += 'Found in:\n';
                
                usages.forEach(usage => {
                    output += `  ${usage.fileName}:${usage.lineNumber} (${usage.context})\n`;
                });
                output += '\n';
            });
            
            results.textContent = output;
        }

        function generateOptimizedFiles() {
            console.log('Starting file generation');
            const newFiles = new Map();
            newFiles.set('PvAChess_New.css', '/* Consolidated CSS File */\n\n');
            newFiles.set('GamesReplay_New.css', '/* Game Replay Specific CSS */\n\n');

            // Get recommendations
            const recommendations = makeRecommendations();
            console.log('Generated recommendations:', recommendations);

            // Process all class definitions
            for (const [className, definitions] of cssDefinitionsMap) {
                console.log(`Processing class: ${className}`);
                const recommendation = recommendations.get(className);
                
                if (!recommendation) {
                    console.log(`No recommendation found for ${className}`);
                    continue;
                }

                // Get the best definition (usually the most complete one)
                const definition = getBestDefinition(className, definitions);
                if (definition) {
                    const targetFile = recommendation.targetFile.replace('.css', '_New.css');
                    const currentContent = newFiles.get(targetFile);
                    newFiles.set(targetFile, currentContent + definition + '\n\n');
                    console.log(`Added ${className} to ${targetFile}`);
                }
            }

            // Download new files
            for (const [fileName, content] of newFiles) {
                downloadFile(fileName, content);
                console.log(`Generated ${fileName} with length ${content.length}`);
            }
        }

        function getBestDefinition(className, definitions) {
            // Sort definitions by length (prefer longer, more complete definitions)
            const sortedDefs = definitions.sort((a, b) => 
                b.definition.length - a.definition.length
            );
            return sortedDefs[0]?.definition || null;
        }

        function downloadFile(filename, content) {
            const blob = new Blob([content], { type: 'text/css' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.click();
            window.URL.revokeObjectURL(url);
        }

        // New helper function to process CSS content
        function analyzeCSSContent(content, fileName, context) {
            const ruleRegex = /\.([a-zA-Z0-9_-]+)\s*{([^}]*)}/g;
            let match;
            
            console.log(`Analyzing CSS content from ${fileName} (${context})`);
            console.log('Content:', content);
            
            while ((match = ruleRegex.exec(content)) !== null) {
                const className = match[1];
                const definition = match[0];
                console.log(`Found class: ${className}`);
                console.log(`Definition: ${definition}`);
                
                addClassUsage(className, fileName, 0, `CSS Definition (${context})`);
                addClassDefinition(className, fileName, 0, definition);
            }
        }
    </script>
</body>
</html>