2025-03-04 01:16:52.095322 - Game[] - Conn[no-connection]: Prison displays updated
2025-03-04 01:16:52.145653 - Game[] - Conn[no-connection]: Document ready triggered
2025-03-04 01:16:52.152653 - Game[] - Conn[no-connection]: gameHub initialized
2025-03-04 01:16:52.162158 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-03-04 01:16:52.170158 - Game[] - Conn[no-connection]: Setting up game state handler
2025-03-04 01:16:52.182290 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-03-04 01:16:52.220711 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-03-04 01:16:52.651353 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-03-04 01:16:52.728407 - Game[] - Conn[no-connection]: Connection ID:
	"ca088546-af98-424c-846d-d2076286aca8"
2025-03-04 01:16:53.160794 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-03-04 01:17:00.395400 - Game[] - Conn[no-connection]: Prison displays updated
2025-03-04 01:17:00.420918 - Game[] - Conn[no-connection]: Document ready triggered
2025-03-04 01:17:00.428616 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-03-04 01:17:00.443295 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-03-04 01:17:00.450425 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-03-04 01:17:00.462544 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-03-04 01:17:00.475665 - Game[] - Conn[no-connection]: Setting up game state handler
2025-03-04 01:17:00.482323 - Game[] - Conn[no-connection]: gameHub initialized
2025-03-04 01:17:00.958474 - Game[] - Conn[no-connection]: Connection ID:
	"092cfb39-761e-4331-b90f-99a69ae00d8c"
2025-03-04 01:17:01.469588 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-03-04 01:17:01.595476 - Game[] - Conn[no-connection]: Prison displays updated
2025-03-04 01:17:01.619144 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-03-04 01:17:01.625656 - Game[] - Conn[no-connection]: Setting up game state handler
2025-03-04 01:17:01.635377 - Game[] - Conn[no-connection]: gameHub initialized
2025-03-04 01:17:01.642582 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-03-04 01:17:01.660319 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-03-04 01:17:01.669834 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-03-04 01:17:02.121117 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-03-04 01:17:02.182754 - Game[] - Conn[no-connection]: Connection ID:
	"9367e100-20cc-42e3-aba9-c83bf7026b17"
2025-03-04 01:17:02.632741 - Game[] - Conn[no-connection]: Document ready triggered
2025-03-04 01:17:05.179443 - Game[] - Conn[no-connection]: Game ID stored in session:
	"3SVVRX"
2025-03-04 01:18:57.921266 - Game[] - Conn[no-connection]: Prison displays updated
2025-03-04 01:18:57.968775 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-03-04 01:18:57.973278 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-03-04 01:18:57.985285 - Game[] - Conn[no-connection]: Setting up game state handler
2025-03-04 01:18:57.990386 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-03-04 01:18:58.032317 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-03-04 01:18:58.481664 - Game[] - Conn[no-connection]: gameHub initialized
2025-03-04 01:18:58.545712 - Game[] - Conn[no-connection]: Connection ID:
	"db225452-4b56-4b06-a723-ebd2c5b33113"
2025-03-04 01:18:58.984005 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-03-04 01:18:59.496456 - Game[] - Conn[no-connection]: Document ready triggered
2025-03-04 01:19:01.575636 - Game[] - Conn[no-connection]: Prison displays updated
2025-03-04 01:19:01.602163 - Game[] - Conn[no-connection]: Document ready triggered
2025-03-04 01:19:01.608865 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-03-04 01:19:01.614333 - Game[] - Conn[no-connection]: Setting up game state handler
2025-03-04 01:19:01.623574 - Game[] - Conn[no-connection]: gameHub initialized
2025-03-04 01:19:01.632739 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-03-04 01:19:01.641889 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-03-04 01:19:01.654003 - Game[] - Conn[no-connection]: Connection ID:
	"85018013-9b74-4724-81fd-485e3d3a5c30"
2025-03-04 01:19:02.105383 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-03-04 01:19:02.135650 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-03-04 01:19:05.203013 - Game[] - Conn[no-connection]: Game ID stored in session:
	"3SVVRX"
2025-03-04 04:22:29.479030 - Game[] - Conn[no-connection]: Prison displays updated
2025-03-04 04:22:29.535138 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-03-04 04:22:29.544141 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-03-04 04:22:29.595339 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-03-04 04:22:30.042004 - Game[] - Conn[no-connection]: gameHub initialized
2025-03-04 04:22:30.057088 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-03-04 04:22:30.103751 - Game[] - Conn[no-connection]: Connection ID:
	"feab6a04-c345-426c-b069-6a30c98e15e4"
2025-03-04 04:22:30.554799 - Game[] - Conn[no-connection]: Document ready triggered
2025-03-04 04:22:31.063981 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-03-04 04:22:31.577724 - Game[] - Conn[no-connection]: Setting up game state handler
2025-03-04 04:22:33.111162 - Game[] - Conn[no-connection]: Prison displays updated
2025-03-04 04:22:33.139687 - Game[] - Conn[no-connection]: Document ready triggered
2025-03-04 04:22:33.145692 - Game[] - Conn[no-connection]: Setting up game state handler
2025-03-04 04:22:33.158561 - Game[] - Conn[no-connection]: gameHub initialized
2025-03-04 04:22:33.165072 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-03-04 04:22:33.171705 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-03-04 04:22:33.642708 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-03-04 04:22:33.688942 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-03-04 04:22:34.169253 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-03-04 04:22:34.200430 - Game[] - Conn[no-connection]: Connection ID:
	"2e5e60ca-15cb-4d99-abff-d5f6ff8ac5a8"
2025-03-04 04:22:35.582709 - Game[] - Conn[no-connection]: Game ID stored in session:
	"3SVVRX"
2025-03-04 05:04:33.563616 - Game[] - Conn[no-connection]: Prison displays updated
2025-03-04 05:04:33.606626 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-03-04 05:04:33.612131 - Game[] - Conn[no-connection]: Setting up game state handler
2025-03-04 05:04:33.621133 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-03-04 05:04:33.630638 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-03-04 05:04:33.668871 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-03-04 05:04:34.114202 - Game[] - Conn[no-connection]: Document ready triggered
2025-03-04 05:04:34.178446 - Game[] - Conn[no-connection]: Connection ID:
	"d2ef60d2-7028-4ceb-b99b-131800919974"
2025-03-04 05:04:34.628860 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-03-04 05:04:35.132956 - Game[] - Conn[no-connection]: gameHub initialized
2025-03-04 05:04:42.074298 - Game[] - Conn[no-connection]: Prison displays updated
2025-03-04 05:04:42.098916 - Game[] - Conn[no-connection]: Document ready triggered
2025-03-04 05:04:42.107422 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-03-04 05:04:42.115551 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-03-04 05:04:42.122679 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-03-04 05:04:42.135801 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-03-04 05:04:42.141925 - Game[] - Conn[no-connection]: Connection ID:
	"aea0079d-e3d3-43cb-ac51-940cd547ccfc"
2025-03-04 05:04:42.608085 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-03-04 05:04:42.655020 - Game[] - Conn[no-connection]: Setting up game state handler
2025-03-04 05:04:43.119171 - Game[] - Conn[no-connection]: gameHub initialized
2025-03-04 05:04:45.514443 - Game[] - Conn[no-connection]: Game ID stored in session:
	"3SVVRX"
2025-03-27 22:33:22.743283 - Game[] - Conn[no-connection]: Prison displays updated
2025-03-27 22:33:22.781300 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-03-27 22:33:22.786805 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-03-27 22:33:22.800804 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-03-27 22:33:22.805808 - Game[] - Conn[no-connection]: Setting up game state handler
2025-03-27 22:33:22.888706 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-03-27 22:33:23.283122 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-03-27 22:33:23.406451 - Game[] - Conn[no-connection]: Connection ID:
	"6abbe6a8-e3e8-41a0-b2a5-e825d795c57f"
2025-03-27 22:33:23.794363 - Game[] - Conn[no-connection]: Document ready triggered
2025-03-27 22:33:24.309328 - Game[] - Conn[no-connection]: gameHub initialized
2025-04-05 12:44:40.520384 - Game[] - Conn[no-connection]: Prison displays updated
2025-04-05 12:44:40.561892 - Game[] - Conn[no-connection]: Document ready triggered
2025-04-05 12:44:40.567398 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-04-05 12:44:40.576397 - Game[] - Conn[no-connection]: Setting up game state handler
2025-04-05 12:44:40.583400 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-04-05 12:44:40.813550 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-04-05 12:44:41.074602 - Game[] - Conn[no-connection]: gameHub initialized
2025-04-05 12:44:41.088615 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-04-05 12:44:41.324780 - Game[] - Conn[no-connection]: Connection ID:
	"1f96e88b-2f30-46a2-b5e5-8c1ca6113c50"
2025-04-05 12:44:41.589850 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-04-05 12:45:07.683934 - Game[] - Conn[no-connection]: Prison displays updated
2025-04-05 12:45:07.691436 - Game[] - Conn[no-connection]: Document ready triggered
2025-04-05 12:45:07.697439 - Game[] - Conn[no-connection]: Setting up game state handler
2025-04-05 12:45:07.705438 - Game[] - Conn[no-connection]: gameHub initialized
2025-04-05 12:45:07.711443 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-04-05 12:45:07.766963 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-04-05 12:45:07.774966 - Game[] - Conn[no-connection]: Connection ID:
	"cf7eb3cd-7736-43b0-918b-503e7bbced16"
2025-04-05 12:45:08.205062 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-04-05 12:45:08.720162 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-04-05 12:45:09.224712 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-04-05 12:45:12.682636 - Game[] - Conn[no-connection]: createNewGame called
2025-04-05 12:45:18.256413 - Game[] - Conn[no-connection]: Game ID stored in session:
	"DB8GRM"
2025-04-05 12:45:57.424603 - Game[] - Conn[no-connection]: Prison displays updated
2025-04-05 12:45:57.431607 - Game[] - Conn[no-connection]: Document ready triggered
2025-04-05 12:45:57.439607 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-04-05 12:45:57.450114 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-04-05 12:45:57.470618 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-04-05 12:45:57.945488 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-04-05 12:45:57.959992 - Game[] - Conn[no-connection]: Setting up game state handler
2025-04-05 12:45:57.976005 - Game[] - Conn[no-connection]: Connection ID:
	"c66c26fb-8bec-435d-bd8b-6b5592034492"
2025-04-05 12:45:58.459171 - Game[] - Conn[no-connection]: gameHub initialized
2025-04-05 12:45:58.963103 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-04-05 13:57:26.822584 - Game[] - Conn[no-connection]: Prison displays updated
2025-04-05 13:57:26.863894 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-04-05 13:57:26.870897 - Game[] - Conn[no-connection]: Setting up game state handler
2025-04-05 13:57:26.885403 - Game[] - Conn[no-connection]: gameHub initialized
2025-04-05 13:57:26.966948 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-04-05 13:57:27.371942 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-04-05 13:57:27.387460 - Game[] - Conn[no-connection]: Document ready triggered
2025-04-05 13:57:27.480908 - Game[] - Conn[no-connection]: Connection ID:
	"4bacc7ab-ec05-4315-8ff2-6cbafdb08405"
2025-04-05 13:57:27.881083 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-04-05 13:57:28.390958 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-04-05 13:57:34.910085 - Game[] - Conn[no-connection]: Prison displays updated
2025-04-05 13:57:34.926781 - Game[] - Conn[no-connection]: Document ready triggered
2025-04-05 13:57:34.933524 - Game[] - Conn[no-connection]: gameHub initialized
2025-04-05 13:57:34.949308 - Game[] - Conn[no-connection]: Setting up game state handler
2025-04-05 13:57:34.971557 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-04-05 13:57:34.980222 - Game[] - Conn[no-connection]: Connection ID:
	"6866117b-bf22-451e-817d-540e53898c4b"
2025-04-05 13:57:35.428241 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-04-05 13:57:35.442840 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-04-05 13:57:35.458533 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-04-05 13:57:35.942491 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-04-05 13:57:37.298574 - Game[] - Conn[no-connection]: createNewGame called
2025-04-05 13:57:44.479954 - Game[] - Conn[no-connection]: Game ID stored in session:
	"DWYXLH"
2025-04-05 14:10:56.546103 - Game[] - Conn[no-connection]: Prison displays updated
2025-04-05 14:10:56.589219 - Game[] - Conn[no-connection]: Document ready triggered
2025-04-05 14:10:56.594221 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-04-05 14:10:56.600726 - Game[] - Conn[no-connection]: Setting up game state handler
2025-04-05 14:10:56.684556 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-04-05 14:10:56.688684 - Game[] - Conn[no-connection]: Connection ID:
	"ed5aca56-0c6c-410f-b2e6-19e04d9422b2"
2025-04-05 14:10:57.106723 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-04-05 14:10:57.620068 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-04-05 14:10:58.133553 - Game[] - Conn[no-connection]: gameHub initialized
2025-04-05 14:10:58.643756 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-04-05 14:11:06.602451 - Game[] - Conn[no-connection]: Prison displays updated
2025-04-05 14:11:06.633985 - Game[] - Conn[no-connection]: Document ready triggered
2025-04-05 14:11:06.643497 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-04-05 14:11:06.650505 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-04-05 14:11:06.660516 - Game[] - Conn[no-connection]: gameHub initialized
2025-04-05 14:11:06.672028 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-04-05 14:11:06.680536 - Game[] - Conn[no-connection]: Connection ID:
	"dd9d38ad-60ce-47a5-a140-dcb9f089918c"
2025-04-05 14:11:07.136226 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-04-05 14:11:07.151228 - Game[] - Conn[no-connection]: Setting up game state handler
2025-04-05 14:11:07.667533 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-04-05 14:11:08.763716 - Game[] - Conn[no-connection]: createNewGame called
2025-04-05 14:11:13.710386 - Game[] - Conn[no-connection]: Game ID stored in session:
	"HOJFJC"
2025-04-05 17:04:29.047872 - Game[] - Conn[no-connection]: Prison displays updated
2025-04-05 17:04:29.088743 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-04-05 17:04:29.093742 - Game[] - Conn[no-connection]: Setting up game state handler
2025-04-05 17:04:29.102251 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-04-05 17:04:29.108251 - Game[] - Conn[no-connection]: gameHub initialized
2025-04-05 17:04:29.342194 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-04-05 17:04:29.594945 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-04-05 17:04:29.858297 - Game[] - Conn[no-connection]: Connection ID:
	"85b6661c-d486-4201-8902-210cde88f5bc"
2025-04-05 17:04:30.109659 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-04-05 17:04:30.623745 - Game[] - Conn[no-connection]: Document ready triggered
2025-04-05 17:04:40.198660 - Game[] - Conn[no-connection]: Prison displays updated
2025-04-05 17:04:40.211826 - Game[] - Conn[no-connection]: Document ready triggered
2025-04-05 17:04:40.219511 - Game[] - Conn[no-connection]: Setting up game state handler
2025-04-05 17:04:40.228181 - Game[] - Conn[no-connection]: gameHub initialized
2025-04-05 17:04:40.237184 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-04-05 17:04:40.244310 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-04-05 17:04:40.257565 - Game[] - Conn[no-connection]: Connection ID:
	"5c314e5a-eafe-4dd2-90ac-4635bd62c92a"
2025-04-05 17:04:40.728538 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-04-05 17:04:40.743837 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-04-05 17:04:41.241331 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-04-05 17:04:46.506540 - Game[] - Conn[no-connection]: createNewGame called
2025-04-05 17:04:53.577897 - Game[] - Conn[no-connection]: Game ID stored in session:
	"ON3344"
2025-04-05 17:05:43.291194 - Game[] - Conn[no-connection]: Prison displays updated
2025-04-05 17:05:43.338684 - Game[] - Conn[no-connection]: Document ready triggered
2025-04-05 17:05:43.344684 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-04-05 17:05:43.349191 - Game[] - Conn[no-connection]: gameHub initialized
2025-04-05 17:05:43.358195 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-04-05 17:05:43.366194 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-04-05 17:05:43.451875 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-04-05 17:05:43.859793 - Game[] - Conn[no-connection]: Setting up game state handler
2025-04-05 17:05:43.950465 - Game[] - Conn[no-connection]: Connection ID:
	"43417c0c-80d1-4993-a9c8-441849eac016"
2025-04-05 17:05:44.371481 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-04-05 17:07:12.789810 - Game[] - Conn[no-connection]: Prison displays updated
2025-04-05 17:07:12.844457 - Game[] - Conn[no-connection]: Document ready triggered
2025-04-05 17:07:12.849757 - Game[] - Conn[no-connection]: gameHub initialized
2025-04-05 17:07:12.945713 - Game[] - Conn[no-connection]: Connection ID:
	"0aee2c06-f49c-41aa-afea-4e04bf997368"
2025-04-05 17:07:12.952220 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-04-05 17:07:13.345759 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-04-05 17:07:13.360005 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-04-05 17:07:13.857970 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-04-05 17:07:14.363187 - Game[] - Conn[no-connection]: Setting up game state handler
2025-04-05 17:07:14.864098 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-04-05 17:07:17.582394 - Game[] - Conn[no-connection]: Prison displays updated
2025-04-05 17:07:17.613041 - Game[] - Conn[no-connection]: Document ready triggered
2025-04-05 17:07:17.618040 - Game[] - Conn[no-connection]: gameHub initialized
2025-04-05 17:07:17.623550 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-04-05 17:07:17.627550 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-04-05 17:07:17.642815 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-04-05 17:07:18.140059 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-04-05 17:07:18.651259 - Game[] - Conn[no-connection]: Connection ID:
	"5e672313-edd8-4f8c-a7fe-ebb310cd50dd"
2025-04-05 17:07:18.783904 - Game[] - Conn[no-connection]: createNewGame called
2025-04-05 17:07:19.164318 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-04-05 17:07:19.679013 - Game[] - Conn[no-connection]: Setting up game state handler
2025-04-05 17:07:23.661024 - Game[] - Conn[no-connection]: Game ID stored in session:
	"M0ZBAI"
2025-04-05 17:13:41.031116 - Game[] - Conn[no-connection]: Prison displays updated
2025-04-05 17:13:41.074630 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-04-05 17:13:41.085632 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-04-05 17:13:41.091632 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-04-05 17:13:41.105138 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-04-05 17:13:41.144513 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-04-05 17:13:41.582834 - Game[] - Conn[no-connection]: gameHub initialized
2025-04-05 17:13:41.645836 - Game[] - Conn[no-connection]: Connection ID:
	"3f4b34c1-abf3-43a0-8849-fdfc4d739f7f"
2025-04-05 17:13:42.096908 - Game[] - Conn[no-connection]: Setting up game state handler
2025-04-05 17:13:42.596389 - Game[] - Conn[no-connection]: Document ready triggered
2025-04-05 17:13:50.114547 - Game[] - Conn[no-connection]: Prison displays updated
2025-04-05 17:13:50.145734 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-04-05 17:13:50.152586 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-04-05 17:13:50.161770 - Game[] - Conn[no-connection]: gameHub initialized
2025-04-05 17:13:50.168272 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-04-05 17:13:50.196097 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-04-05 17:13:50.203794 - Game[] - Conn[no-connection]: Connection ID:
	"4f7ae47b-7b3e-4655-9bff-9820a5598eb1"
2025-04-05 17:13:50.649039 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-04-05 17:13:50.680806 - Game[] - Conn[no-connection]: Setting up game state handler
2025-04-05 17:13:51.160550 - Game[] - Conn[no-connection]: Document ready triggered
2025-04-05 17:13:52.552529 - Game[] - Conn[no-connection]: createNewGame called
2025-04-05 17:13:59.531416 - Game[] - Conn[no-connection]: Game ID stored in session:
	"6VDUIH"
2025-04-05 17:23:49.748652 - Game[] - Conn[no-connection]: Prison displays updated
2025-04-05 17:23:49.787666 - Game[] - Conn[no-connection]: Document ready triggered
2025-04-05 17:23:49.791669 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-04-05 17:23:49.801668 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-04-05 17:23:49.807175 - Game[] - Conn[no-connection]: gameHub initialized
2025-04-05 17:23:49.870455 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-04-05 17:23:49.876457 - Game[] - Conn[no-connection]: Connection ID:
	"b4e4df7b-6ca7-4ff2-b2f8-a2f36c0f8394"
2025-04-05 17:23:50.302243 - Game[] - Conn[no-connection]: Setting up game state handler
2025-04-05 17:23:50.802927 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-04-05 17:23:51.317066 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-04-05 17:23:59.153652 - Game[] - Conn[no-connection]: Prison displays updated
2025-04-05 17:23:59.182325 - Game[] - Conn[no-connection]: Document ready triggered
2025-04-05 17:23:59.187835 - Game[] - Conn[no-connection]: gameHub initialized
2025-04-05 17:23:59.192275 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-04-05 17:23:59.199780 - Game[] - Conn[no-connection]: Setting up game state handler
2025-04-05 17:23:59.207783 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-04-05 17:23:59.216289 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-04-05 17:23:59.700050 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-04-05 17:23:59.729902 - Game[] - Conn[no-connection]: Connection ID:
	"cf406c7b-7f72-4d15-bdd2-9ea0055c3dce"
2025-04-05 17:24:00.211292 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-04-05 17:24:01.591876 - Game[] - Conn[no-connection]: createNewGame called
2025-04-05 17:24:06.177407 - Game[] - Conn[no-connection]: Game ID stored in session:
	"5I3WHB"
2025-04-05 17:46:03.328057 - Game[] - Conn[no-connection]: Prison displays updated
2025-04-05 17:46:03.375072 - Game[] - Conn[no-connection]: Document ready triggered
2025-04-05 17:46:03.385576 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-04-05 17:46:03.477952 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-04-05 17:46:03.482459 - Game[] - Conn[no-connection]: Connection ID:
	"742bb74a-bf99-4652-919d-e66f893ee6bb"
2025-04-05 17:46:03.877272 - Game[] - Conn[no-connection]: Setting up game state handler
2025-04-05 17:46:04.385980 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-04-05 17:46:04.896148 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-04-05 17:46:05.409034 - Game[] - Conn[no-connection]: gameHub initialized
2025-04-05 17:46:05.911480 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-04-05 17:46:10.479246 - Game[] - Conn[no-connection]: Prison displays updated
2025-04-05 17:46:10.531109 - Game[] - Conn[no-connection]: Document ready triggered
2025-04-05 17:46:10.535613 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-04-05 17:46:10.543718 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-04-05 17:46:10.549424 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-04-05 17:46:10.554429 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-04-05 17:46:10.560049 - Game[] - Conn[no-connection]: Connection ID:
	"1733f140-e057-47f7-a8a1-ca4ba6670df4"
2025-04-05 17:46:10.571210 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-04-05 17:46:11.045292 - Game[] - Conn[no-connection]: gameHub initialized
2025-04-05 17:46:11.556401 - Game[] - Conn[no-connection]: Setting up game state handler
2025-04-05 17:46:12.243548 - Game[] - Conn[no-connection]: createNewGame called
2025-04-05 17:46:16.575305 - Game[] - Conn[no-connection]: Game ID stored in session:
	"RKVXM7"
2025-04-05 17:54:06.428230 - Game[] - Conn[no-connection]: Prison displays updated
2025-04-05 17:54:06.495415 - Game[] - Conn[no-connection]: Document ready triggered
2025-04-05 17:54:06.502230 - Game[] - Conn[no-connection]: gameHub initialized
2025-04-05 17:54:06.512853 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-04-05 17:54:06.517963 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-04-05 17:54:06.588533 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-04-05 17:54:07.009560 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-04-05 17:54:07.023667 - Game[] - Conn[no-connection]: Setting up game state handler
2025-04-05 17:54:07.101433 - Game[] - Conn[no-connection]: Connection ID:
	"f8767c5d-ecb9-4520-812f-3a004b9b479f"
2025-04-05 17:54:07.532297 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-04-05 17:54:11.597852 - Game[] - Conn[no-connection]: Prison displays updated
2025-04-05 17:54:11.622605 - Game[] - Conn[no-connection]: Document ready triggered
2025-04-05 17:54:11.628003 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-04-05 17:54:11.637022 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-04-05 17:54:11.642134 - Game[] - Conn[no-connection]: gameHub initialized
2025-04-05 17:54:11.657270 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-04-05 17:54:11.662408 - Game[] - Conn[no-connection]: Connection ID:
	"db2ae541-b4b3-4f3e-91a7-ad7937a0bf11"
2025-04-05 17:54:12.126282 - Game[] - Conn[no-connection]: Setting up game state handler
2025-04-05 17:54:12.141528 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-04-05 17:54:12.652868 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-04-05 17:54:12.930280 - Game[] - Conn[no-connection]: createNewGame called
2025-04-05 17:54:17.717784 - Game[] - Conn[no-connection]: Game ID stored in session:
	"YO8Y1S"
2025-04-05 19:09:36.234162 - Game[] - Conn[no-connection]: Prison displays updated
2025-04-05 19:09:36.309754 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-04-05 19:09:36.318259 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-04-05 19:09:36.376636 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-04-05 19:09:36.791395 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-04-05 19:09:36.821224 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-04-05 19:09:36.882187 - Game[] - Conn[no-connection]: Connection ID:
	"95bf7717-e1b5-4b75-bbcc-dfd59d74f4f2"
2025-04-05 19:09:37.301440 - Game[] - Conn[no-connection]: Document ready triggered
2025-04-05 19:09:37.332214 - Game[] - Conn[no-connection]: Setting up game state handler
2025-04-05 19:09:37.810906 - Game[] - Conn[no-connection]: gameHub initialized
2025-04-05 19:09:40.600083 - Game[] - Conn[no-connection]: Prison displays updated
2025-04-05 19:09:40.624871 - Game[] - Conn[no-connection]: Document ready triggered
2025-04-05 19:09:40.632885 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-04-05 19:09:40.640724 - Game[] - Conn[no-connection]: gameHub initialized
2025-04-05 19:09:40.648920 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-04-05 19:09:40.657426 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-04-05 19:09:40.675560 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-04-05 19:09:40.683067 - Game[] - Conn[no-connection]: Connection ID:
	"9ca4c46a-2e7e-4231-8b48-90a843860e09"
2025-04-05 19:09:41.127647 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-04-05 19:09:41.143299 - Game[] - Conn[no-connection]: Setting up game state handler
2025-04-05 19:09:41.885951 - Game[] - Conn[no-connection]: createNewGame called
2025-04-05 19:09:47.215927 - Game[] - Conn[no-connection]: Game ID stored in session:
	"WDFDSO"
2025-04-29 23:11:28.132889 - Game[] - Conn[no-connection]: Prison displays updated
2025-04-29 23:11:28.171466 - Game[] - Conn[no-connection]: Document ready triggered
2025-04-29 23:11:28.178466 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-04-29 23:11:28.186472 - Game[] - Conn[no-connection]: Setting up game state handler
2025-04-29 23:11:28.275111 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-04-29 23:11:28.677955 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-04-29 23:11:28.708034 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-04-29 23:11:28.786466 - Game[] - Conn[no-connection]: Connection ID:
	"f4862c9a-baaf-4010-aa00-cdfbcaf692aa"
2025-04-29 23:11:29.190717 - Game[] - Conn[no-connection]: gameHub initialized
2025-04-29 23:11:29.708390 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-05-26 17:22:41.256954 - Game[] - Conn[no-connection]: Prison displays updated
2025-05-26 17:22:41.303447 - Game[] - Conn[no-connection]: Document ready triggered
2025-05-26 17:22:41.312626 - Game[] - Conn[no-connection]: Setting up game state handler
2025-05-26 17:22:41.319568 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-05-26 17:22:41.422306 - Game[] - Conn[no-connection]: Connection ID:
	"01e95070-c11d-4bbf-bb1d-c5dac3a58402"
2025-05-26 17:22:41.803240 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-05-26 17:22:41.928039 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-05-26 17:22:42.315135 - Game[] - Conn[no-connection]: gameHub initialized
2025-05-26 17:22:42.818181 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-05-26 17:22:43.330609 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-05-26 17:23:04.819403 - Game[] - Conn[no-connection]: Prison displays updated
2025-05-26 17:23:04.910320 - Game[] - Conn[no-connection]: Document ready triggered
2025-05-26 17:23:04.918101 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-05-26 17:23:04.930069 - Game[] - Conn[no-connection]: Setting up game state handler
2025-05-26 17:23:05.384343 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-05-26 17:23:05.412857 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-05-26 17:23:05.657958 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-05-26 17:23:05.664957 - Game[] - Conn[no-connection]: Connection ID:
	"ae6d48f4-0608-4e83-abc5-ee052ddbdf12"
2025-05-26 17:23:05.899070 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-05-26 17:23:06.415721 - Game[] - Conn[no-connection]: gameHub initialized
2025-05-26 17:24:39.366535 - Game[] - Conn[no-connection]: Game ID stored in session:
	"5WYHAR"
2025-05-26 17:25:56.107622 - Game[] - Conn[no-connection]: Game ID stored in session:
	"5WYHAR"
2025-05-26 17:26:31.389094 - Game[] - Conn[no-connection]: Prison displays updated
2025-05-26 17:26:31.458175 - Game[] - Conn[no-connection]: Initial state check
	{
  "isFirstPlayer": true,
  "typeof_isFirstPlayer": "boolean",
  "isFirstPlayer_isDefined": true
}
2025-05-26 17:26:31.462849 - Game[] - Conn[no-connection]: Initializing SignalR...
2025-05-26 17:26:31.475624 - Game[] - Conn[no-connection]: Setting up game state handler
2025-05-26 17:26:31.482339 - Game[] - Conn[no-connection]: gameHub initialized
2025-05-26 17:26:31.529424 - Game[] - Conn[no-connection]: Successfully connected to SignalR hub
2025-05-26 17:26:31.959287 - Game[] - Conn[no-connection]: Document ready triggered
2025-05-26 17:26:32.039471 - Game[] - Conn[no-connection]: Connection ID:
	"69f2072d-70da-4dba-b1c2-105aceda0613"
2025-05-26 17:26:32.461561 - Game[] - Conn[no-connection]: Setting up player joined handler
2025-05-26 17:26:32.974407 - Game[] - Conn[no-connection]: Setting up chat message handler
2025-05-26 17:26:36.215053 - Game[] - Conn[no-connection]: Game ID stored in session:
	"5WYHAR"
