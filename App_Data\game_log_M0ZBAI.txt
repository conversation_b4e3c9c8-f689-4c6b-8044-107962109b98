2025-04-05 17:07:19.396189 - Game[M0ZBAI] - Conn[no-connection]: updateTurnMessage - Complete
2025-04-05 17:07:24.168233 - Game[M0ZBAI] - Conn[no-connection]: Player 2 join attempt
	{
  "gameIdToJoin": "M0<PERSON><PERSON><PERSON>",
  "userId": 1,
  "hubId": "0aee2c06-f49c-41aa-afea-4e04bf997368"
}
2025-04-05 17:07:24.176236 - Game[M0ZBAI] - Conn[no-connection]: joinGame() - STEP 4 - isFirstPlayer = false;
2025-04-05 17:07:24.183854 - Game[M0ZBAI] - Conn[no-connection]: joinGame() - STEP 5 - gameHub.server.joinGame(): gameIdToJoin: M0ZBAI userId:1
2025-04-05 17:07:24.208610 - Game[M0ZBAI] - Conn[no-connection]: updateTurnMessage - Complete
2025-04-05 17:07:24.215613 - Game[M0ZBAI] - Conn[no-connection]: updateTurnMessage - Complete
2025-04-05 17:07:24.230510 - Game[M0ZBAI] - Conn[no-connection]: Player 2 joined successfully
2025-04-05 17:07:24.238099 - Game[M0ZBAI] - Conn[no-connection]: updateTurnMessage - Complete
2025-04-05 17:07:24.248693 - Game[M0ZBAI] - Conn[no-connection]: Prison displays updated
