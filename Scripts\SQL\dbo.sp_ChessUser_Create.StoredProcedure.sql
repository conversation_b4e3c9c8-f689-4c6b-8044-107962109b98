USE [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]
GO
/****** Object:  StoredProcedure [dbo].[sp_ChessUser_Create]    Script Date: 4/5/2025 1:17:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [dbo].[sp_ChessUser_Create]
    @Username NVARCHAR(50),
    @Email NVARCHAR(100),
    @PasswordHash NVARCHAR(128),
    @DisplayName NVARCHAR(50)
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Check if username or email already exists
    IF EXISTS (SELECT 1 FROM ChessUsers WHERE Username = @Username OR Email = @Email)
    BEGIN
        RETURN -1; -- Already exists
    END
    
    INSERT INTO ChessUsers (
        Username,
        Email,
        PasswordHash,
        DisplayName,
        DateCreated
    )
    VALUES (
        @Username,
        @Email,
        @PasswordHash,
        @DisplayName,
        GETDATE()
    );
    
    RETURN 0; -- Success
END
GO
