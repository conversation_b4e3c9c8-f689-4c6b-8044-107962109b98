Imports Microsoft.AspNet.SignalR
Imports Microsoft.AspNet.SignalR.Hubs
Imports System.Threading.Tasks
Imports System.Collections.Concurrent
Imports System.Web.Script.Serialization
Imports System.Data.SqlClient
Imports System.Configuration
Imports Newtonsoft.Json.Linq
Imports Newtonsoft.Json
Imports System.Security.Cryptography

Public Class GameHub
    Inherits Hub

    ' Dictionary to store game states - Key is game ID, Value is game state
    Private Shared gameStates As New ConcurrentDictionary(Of String, GameState)
    ' Dictionary to track players in each game - Key is game ID, Value is tuple of player connection IDs
    Private Shared gameConnections As New ConcurrentDictionary(Of String, Tuple(Of String, String))
    ' Dictionary to track games and players
    Private Shared ActiveGames As New Dictionary(Of String, HashSet(Of String))
    ' Dictionary to store userId for each connection
    Private Shared userConnections As New ConcurrentDictionary(Of String, Integer)  ' ConnectionId -> UserId
    ' Add this with the other shared fields at the top of the class
    Private Shared currentGameId As String

    Public Class GameState
        Public Property Board As String()()
        Public Property IsPlebsTurn As Boolean
        Public Property PromotedPawns As HashSet(Of String)
        Public Property CapturedPlebs As List(Of String)
        Public Property CapturedAristoi As List(Of String)
        Public Property CurrentSuperPawn As String
        Public Property FirstPlayerIsPlebs As Boolean
        Public Property FirstPlayerUserId As Integer
        Public Property SecondPlayerUserId As Integer
        Public Property PawnStates As Dictionary(Of String, String)  ' Key: "row,col", Value: state ("normal", "radical", "super")
        Public Property CurrentMoveNumber As Integer = 0  ' Initialize to 0, first move will be 1

        Public Sub New()
            PromotedPawns = New HashSet(Of String)()
            CapturedPlebs = New List(Of String)()
            CapturedAristoi = New List(Of String)()
            PawnStates = New Dictionary(Of String, String)()
        End Sub
    End Class

    Private Function IsUserAuthenticated() As Boolean
        Return Context.User.Identity.IsAuthenticated
    End Function

    ' Join a game room
    Public Function JoinGame(gameId As String, userId As Integer, isSoloPlay As Boolean) As Task(Of Boolean)
        Try
            LogToFile("JoinGame - Starting", New With {
                .gameId = gameId,
                .userId = userId,
                .isSoloPlay = isSoloPlay,
                .connectionId = Context.ConnectionId
            })

            ' Log basic join attempt
            LogToFile("JoinGame called", New With {
                .gameIdToJoin = If(gameId Is Nothing, "Nothing", gameId),
                .userId = userId,
                .connectionId = Context.ConnectionId,
                .gameIdType = If(gameId Is Nothing, "Nothing", gameId.GetType().ToString()),
                .userIdType = userId.GetType().ToString()
            })

            ' Log authentication and session state
            LogToFile("JoinGame authentication state", New With {
                .userIdentityName = If(Context.User?.Identity?.Name, "Nothing"),
                .isAuthenticated = If(Context.User?.Identity Is Nothing, False, Context.User.Identity.IsAuthenticated),
                .sessionUserId = If(HttpContext.Current Is Nothing OrElse HttpContext.Current.Session Is Nothing,
                                  "Nothing",
                                  If(HttpContext.Current.Session("UserId"), "Nothing"))
            })

            ' Log existing game state if it exists
            If Not String.IsNullOrEmpty(gameId) AndAlso gameStates.ContainsKey(gameId) Then
                Dim state = gameStates(gameId)
                If state IsNot Nothing Then
                    Dim firstUserId As Integer = 0
                    Dim secondUserId As Integer = 0

                    ' Get first player's userId
                    If gameConnections.ContainsKey(gameId) Then
                        Dim connections = gameConnections(gameId)
                        If connections IsNot Nothing Then
                            Dim firstConnId = connections.Item1
                            If Not String.IsNullOrEmpty(firstConnId) Then
                                userConnections.TryGetValue(firstConnId, firstUserId)
                            End If

                            ' Get second player's userId if they exist
                            If connections.Item2 IsNot Nothing Then
                                Dim secondConnId = connections.Item2
                                userConnections.TryGetValue(secondConnId, secondUserId)
                            End If
                        End If
                    End If

                    LogToFile("Existing game state", New With {
                        .gameId = gameId,
                        .isPlebsTurn = state.IsPlebsTurn,
                        .firstPlayerIsPlebs = state.FirstPlayerIsPlebs,
                        .firstPlayerUserId = firstUserId,
                        .secondPlayerUserId = secondUserId
                    })
                End If
            End If

            If Not IsUserAuthenticated() Then
                Return Task.FromResult(False)
            End If

            Try
                Dim connections As Tuple(Of String, String) = Nothing

                LogToFile("Joining game", New With {
                .gameIdToJoin = gameId,
                .userId = userId,
                .connectionId = Context.ConnectionId
            })


                Dim playerLabel As String = "Player2"

                If Not gameConnections.TryGetValue(gameId, connections) Then
                    playerLabel = "Player1"
                End If



                ' Store the userId for this connection
                If Not userConnections.ContainsKey(Context.ConnectionId) Then
                    LogToFile("GameHub.JoinGame - About to add to userConnections")
                    userConnections.TryAdd(Context.ConnectionId, userId)
                    LogToFile("GameHub.JoinGame - Added to userConnections")
                End If


                If Not gameConnections.TryGetValue(gameId, connections) Then
                    ' Create new game state
                    Dim gameState As GameState = Nothing
                    If Not gameStates.TryGetValue(gameId, gameState) Then
                        gameState = New GameState()
                        gameState.CurrentMoveNumber = 0  ' Initialize move counter
                        gameStates.TryAdd(gameId, gameState)

                        ' Add database record for new game with proper isSoloPlay value
                        Dim success = ChessGameLogger.DB_StartNewGame(gameId, isSoloPlay, userId)
                        LogToFile("New game database creation", New With {
                            .gameId = gameId,
                            .success = success,
                            .userId = userId,
                            .isSoloPlay = isSoloPlay
                        })
                    End If

                    ' Add first player connection
                    Dim newConnection = New Tuple(Of String, String)(Context.ConnectionId, Nothing)
                    gameConnections.TryAdd(gameId, newConnection)
                    Groups.Add(Context.ConnectionId, gameId).Wait()  ' Add .Wait() to ensure completion
                    LogToFile("First player added to group", New With {
                        .gameId = gameId,
                        .connectionId = Context.ConnectionId
                    })

                    ' Send complete initial state
                    Dim initialState = New With {
                    .FirstPlayerIsPlebs = gameState.FirstPlayerIsPlebs,
                    .IsPlebsTurn = gameState.IsPlebsTurn,
                    .FirstPlayerTurn = True  ' Game always starts with first player
                    }

                    LogToFile("About to send playerJoined notification", New With {
                        .gameId = gameId,
                        .player1ConnectionId = If(connections IsNot Nothing, connections.Item1, Nothing),
                        .player2ConnectionId = Context.ConnectionId,
                        .groupMembers = If(gameConnections.ContainsKey(gameId), gameConnections(gameId), Nothing)
                    })

                    ' Add this right before the notification
                    LogToFile("Group membership state", New With {
                        .gameId = gameId,
                        .currentConnection = Context.ConnectionId,
                        .firstPlayer = If(connections IsNot Nothing, connections.Item1, "null"),
                        .secondPlayer = If(connections IsNot Nothing, connections.Item2, "null"),
                        .whenAdded = DateTime.Now
                    })

                    ' Notify both players
                    Clients.Group(gameId).playerJoined(initialState)

                    LogToFile("Sent playerJoined notification", New With {
                        .gameId = gameId,
                        .gameStateInfo = initialState
                    })

                    LogToFile("Added to group", New With {
                        .gameId = gameId,
                        .connectionId = Context.ConnectionId,
                        .location = "joining_game"  ' or "second_player" or "chat"
                    })

                    Return Task.FromResult(True)
                End If

                ' Existing game
                If connections.Item2 Is Nothing Then
                    ' Add logging right before the update
                    LogToFile("Second player joining - before update", New With {
                        .gameId = gameId,
                        .existingConnections = connections,
                        .newConnectionId = Context.ConnectionId,
                        .dictionaryCount = gameConnections.Count,
                        .hasGame = gameConnections.ContainsKey(gameId)
                    })

                    ' Log the state before update
                    LogToFile("Before second player update", New With {
                        .gameId = gameId,
                        .existingConnections = If(connections IsNot Nothing,
                            New With {
                                .first = If(connections.Item1 IsNot Nothing, connections.Item1, "null"),
                                .second = If(connections.Item2 IsNot Nothing, connections.Item2, "null")
                            },
                            Nothing),
                        .newConnectionId = Context.ConnectionId
                    })

                    ' Update connections with Player 2's info
                    Dim newConnections = New Tuple(Of String, String)(connections.Item1, Context.ConnectionId)
                    Dim updateResult = gameConnections.TryUpdate(gameId, newConnections, connections)

                    ' Log the state after update
                    LogToFile("After second player update", New With {
                        .gameId = gameId,
                        .success = updateResult,
                        .currentConnections = If(gameConnections.TryGetValue(gameId, connections),
                            New With {
                                .first = If(connections.Item1 IsNot Nothing, connections.Item1, "null"),
                                .second = If(connections.Item2 IsNot Nothing, connections.Item2, "null")
                            },
                            Nothing)
                    })

                    Groups.Add(Context.ConnectionId, gameId).Wait()  ' Add .Wait() to ensure completion
                    LogToFile("Second player added to group", New With {
                        .gameId = gameId,
                        .connectionId = Context.ConnectionId,
                        .existingPlayer = connections.Item1
                    })

                    ' Get game state
                    Dim gameState As GameState = Nothing
                    If gameStates.TryGetValue(gameId, gameState) Then
                        ' Log state before sending
                        LogToFile("Second player joining - Initial state", New With {
                            .gameId = gameId,
                            .firstPlayerIsPlebs = gameState.FirstPlayerIsPlebs,
                            .isPlebsTurn = gameState.IsPlebsTurn
                        })

                        Dim gameStateInfo = New With {
                            .FirstPlayerIsPlebs = gameState.FirstPlayerIsPlebs,
                            .IsPlebsTurn = gameState.IsPlebsTurn,
                            .FirstPlayerTurn = True  ' Game starts with first player
                        }

                        LogToFile("About to notify players", New With {
                            .gameId = gameId,
                            .state = gameStateInfo,
                            .group = gameId,
                            .connections = If(connections IsNot Nothing,
                                New With {
                                    .first = If(connections.Item1 IsNot Nothing, connections.Item1, "null"),
                                    .second = If(connections.Item2 IsNot Nothing, connections.Item2, "null")
                                },
                                Nothing)
                        })

                        LogToFile("Clients.Group(gameId).playerJoined(gameStateInfo) GameId: " & gameId)

                        Clients.Group(gameId).playerJoined(gameStateInfo)

                        LogToFile("Players notified successfully", New With {
                            .gameId = gameId,
                            .connections = If(connections IsNot Nothing,
                                New With {
                                    .first = If(connections.Item1 IsNot Nothing, connections.Item1, "null"),
                                    .second = If(connections.Item2 IsNot Nothing, connections.Item2, "null")
                                },
                                Nothing)
                        })

                        ChessGameLogger.DB_AddSecondPlayer(gameId, userId)
                        LogToFile("Added second player to game record", New With {
                                .gameId = gameId,
                                .secondPlayerId = userId
                            })

                        LogToFile("Player 2 joining - Complete state", New With {
                            .gameStateInfo = gameStateInfo
                        })
                    End If

                    LogToFile("Added to group", New With {
                        .gameId = gameId,
                        .connectionId = Context.ConnectionId,
                        .location = "second_player"  ' or "second_player" or "chat"
                    })

                    Return Task.FromResult(True)
                End If



                ' Default return
                LogToFile("GameHub.JoinGame - Reached end of method")
                Return Task.FromResult(False)

            Catch ex As Exception
                ' Add exception logging with more detail
                LogToFile("GameHub.JoinGame - Exception: {ex.GetType().Name}")
                LogToFile("GameHub.JoinGame - Exception Message: {ex.Message}")
                LogToFile("GameHub.JoinGame - Stack Trace: {ex.StackTrace}")
                Return Task.FromResult(False)
            End Try

        Catch ex As Exception
            ' Add exception logging with more detail
            LogToFile("JoinGame failed", New With {
                .gameId = gameId,
                .error = ex.Message,
                .stackTrace = ex.StackTrace
            })
            Return Task.FromResult(False)
        End Try
    End Function

    ' Make a move
    Public Function MakeMove(gameId As String, moveData As Object, moveType As String) As Task
        Try
            Dim state As GameState = Nothing
            If gameStates.TryGetValue(gameId, state) Then
                ' Convert moveData to JObject if it isn't already
                Dim moveDataObj = TryCast(moveData, JObject)
                If moveDataObj Is Nothing Then
                    moveDataObj = JObject.FromObject(moveData)
                End If

                ' Update board state
                state.Board = moveDataObj("board").ToObject(Of String()())
                state.IsPlebsTurn = moveDataObj("nextState")("isPlebsTurn").ToObject(Of Boolean)

                ' Update promoted pawns
                state.PromotedPawns.Clear()
                If moveDataObj("promotedPawns") IsNot Nothing Then
                    For Each pawn As JToken In moveDataObj("promotedPawns")
                        state.PromotedPawns.Add(pawn.ToString())
                    Next
                End If

                ' Update captured pieces - use the client's capture lists directly
                ' since the client's isValidMove() already enforces the rules
                state.CapturedPlebs = moveDataObj("capturedPlebs").ToObject(Of List(Of String))()
                state.CapturedAristoi = moveDataObj("capturedAristoi").ToObject(Of List(Of String))()

                ' Update pawn states
                state.PawnStates.Clear()
                If moveDataObj("pawnStates") IsNot Nothing Then
                    For Each pair As JArray In moveDataObj("pawnStates")
                        state.PawnStates.Add(pair(0).ToString(), pair(1).ToString())
                    Next
                End If

                ' Update current super pawn
                state.CurrentSuperPawn = If(moveDataObj("currentSuperPawn")?.ToObject(Of String)(), Nothing)

                ' Broadcast the updated state
                Clients.Group(gameId).updateGameState(New With {
                    .moveType = moveType,
                    .board = state.Board,
                    .isPlebsTurn = state.IsPlebsTurn,
                    .promotedPawns = state.PromotedPawns,
                    .capturedPlebs = state.CapturedPlebs,
                    .capturedAristoi = state.CapturedAristoi,
                    .currentSuperPawn = state.CurrentSuperPawn,
                    .pawnStates = state.PawnStates,
                    .nextState = moveDataObj("nextState")
                })

                Return Task.CompletedTask
            End If

            Return Task.CompletedTask
        Catch ex As Exception
            LogToFile("Error in MakeMove", New With {
                .error = ex.Message,
                .stackTrace = ex.StackTrace,
                .moveData = moveData
            }, gameId)
            Throw
        End Try
    End Function

    Private Function InitializeBoard() As String()()
        Return New String()() {
            New String() {"black-rook", "black-knight", "black-bishop", "black-queen", "black-king", "black-bishop", "black-knight", "black-rook"},
            New String() {"black-pawn", "black-pawn", "black-pawn", "black-pawn", "black-pawn", "black-pawn", "black-pawn", "black-pawn"},
            New String() {Nothing, Nothing, Nothing, Nothing, Nothing, Nothing, Nothing, Nothing},
            New String() {Nothing, Nothing, Nothing, Nothing, Nothing, Nothing, Nothing, Nothing},
            New String() {Nothing, Nothing, Nothing, Nothing, Nothing, Nothing, Nothing, Nothing},
            New String() {Nothing, Nothing, Nothing, Nothing, Nothing, Nothing, Nothing, Nothing},
            New String() {"white-pawn", "white-pawn", "white-pawn", "white-pawn", "white-pawn", "white-pawn", "white-pawn", "white-pawn"},
            New String() {"white-rook", "white-knight", "white-bishop", "white-queen", "white-king", "white-bishop", "white-knight", "white-rook"}
        }
    End Function

    Private Function CheckVictory(game As GameState) As String
        ' Check if Plebs won (all noble pieces captured)
        Dim remainingNobles = False
        For Each row In game.Board
            For Each piece In row
                If piece IsNot Nothing AndAlso Not piece.EndsWith("pawn") Then
                    remainingNobles = True
                    Exit For
                End If
            Next
            If remainingNobles Then Exit For
        Next

        ' Check if Aristoi won (all pawns captured)
        Dim remainingPawns = False
        For Each row In game.Board
            For Each piece In row
                If piece IsNot Nothing AndAlso piece.EndsWith("pawn") Then
                    remainingPawns = True
                    Exit For
                End If
            Next
            If remainingPawns Then Exit For
        Next

        If Not remainingNobles Then
            Return "Plebs"
        ElseIf Not remainingPawns Then
            Return "Aristoi"
        End If

        Return Nothing
    End Function

    ' Clean up when a player disconnects
    Public Overrides Function OnDisconnected(stopCalled As Boolean) As Task
        ' Find games this player was in
        Dim gamesToRemove = gameConnections.Where(Function(g) _
            g.Value.Item1 = Context.ConnectionId OrElse
            g.Value.Item2 = Context.ConnectionId).Select(Function(g) g.Key).ToList()

        For Each gameId In gamesToRemove
            Dim state As GameState = Nothing
            Dim connections As Tuple(Of String, String) = Nothing

            ' Get the userId of the disconnecting player
            Dim disconnectingUserId As Integer = GetUserIdFromConnectionId(Context.ConnectionId)

            ' Thread-safe removal
            gameStates.TryRemove(gameId, state)
            If gameConnections.TryRemove(gameId, connections) Then
                ' Log game end with disconnection info
                LogToFile("Player disconnected from game", New With {
                    .gameId = gameId,
                    .disconnectingUserId = disconnectingUserId,
                    .connectionId = Context.ConnectionId
                })

                LogGameEnd(gameId, "Abandoned", disconnectingUserId)

                ' Notify other player if they're still connected
                Dim otherPlayerId = If(connections.Item1 = Context.ConnectionId,
                                   connections.Item2, connections.Item1)
                If otherPlayerId IsNot Nothing Then
                    Clients.Client(otherPlayerId).playerDisconnected()
                End If
            End If
        Next

        Return MyBase.OnDisconnected(stopCalled)
    End Function

    Public Function SendTestMessage(message As String) As Task
        System.Diagnostics.Debug.WriteLine($"Hub received message: {message}")
        Return Clients.All.receiveMessage($" {message}")
    End Function

    Public Function StartNewGame(gameId As String) As Task
        LogToFile("StartNewGame call to Clients.Group(gameId).initializeNewGame() ", New With {
                .gameId = gameId
            })
        Return Clients.Group(gameId).initializeNewGame()
    End Function

    Public Function AnnounceVictory(gameId As String, endGameAction As String, endGameUserId As Integer) As Task
        Try
            ' Log the game end before announcing
            LogGameEnd(gameId, endGameAction, endGameUserId)

            ' Then announce to clients
            Return Clients.Group(gameId).victoryAnnounced(endGameAction)
        Catch ex As Exception
            LogToFile("Error in AnnounceVictory", New With {
                .gameId = gameId,
                .endGameAction = endGameAction,
                .endGameUserId = endGameUserId,
                .error = ex.Message,
                .stack = ex.StackTrace
            })
            Throw
        End Try
    End Function

    Public Function LogGameStart(gameId As String, isSoloPlay As Boolean, userId As Integer) As Task(Of Boolean)
        Try
            LogToFile("LogGameStart - Entry point", New With {
                .gameId = gameId,
                .isSoloPlay = isSoloPlay,
                .connectionId = Context.ConnectionId,
                .userId = userId
            })

            ' Use the passed userId instead of getting it from connection
            If userId = -1 Then
                userId = GetUserIdFromConnectionId(Context.ConnectionId)
            End If

            LogToFile("LogGameStart - Got userId", New With {
                .gameId = gameId,
                .userId = userId,
                .connectionId = Context.ConnectionId
            })

            Dim result = ChessGameLogger.DB_StartNewGame(gameId, isSoloPlay, userId)

            LogToFile("LogGameStart - After DB call", New With {
                .gameId = gameId,
                .isSoloPlay = isSoloPlay,
                .userId = userId,
                .result = result
            })

            Return Task.FromResult(result)
        Catch ex As Exception
            LogToFile("LogGameStart - Exception", New With {
                .error = ex.Message,
                .stack = ex.StackTrace,
                .gameId = gameId
            })
            Return Task.FromResult(False)
        End Try
    End Function

    Public Sub LogMove(moveData As Object)
        Try
            Dim userId As Integer = -1
            userConnections.TryGetValue(Context.ConnectionId, userId)

            ' Get current game state to access move number
            Dim gameState As GameState = Nothing
            If gameStates.TryGetValue(moveData.GameId, gameState) Then
                ' Increment move number
                gameState.CurrentMoveNumber += 1

                Dim move As New ChessMove With {
                    .GameId = moveData.GameId,
                    .PlayerRole = moveData.PlayerRole,
                    .FromRow = moveData.FromRow,
                    .FromCol = moveData.FromCol,
                    .ToRow = moveData.ToRow,
                    .ToCol = moveData.ToCol,
                    .PieceMoved = moveData.PieceMoved,
                    .PieceCaptured = moveData.PieceCaptured,
                    .WasPromotion = moveData.WasPromotion,
                    .UserId = userId,
                    .MoveNumber = gameState.CurrentMoveNumber  ' Add the move number
                }

                ChessGameLogger.DB_LogMove(move)
            End If
        Catch ex As Exception
            LogToFile("Error in LogMove", New With {
                .error = ex.Message,
                .stackTrace = ex.StackTrace
            })
        End Try
    End Sub

    Public Function LogGameEnd(gameId As String, endGameAction As String, endGameUserId As Integer) As Task
        Try
            LogToFile("Game end", New With {
                .gameId = gameId,
                .endGameAction = endGameAction,
                .endGameUserId = endGameUserId
            })

            ChessGameLogger.DB_EndGame(gameId, endGameAction, endGameUserId)
            Return Task.CompletedTask
        Catch ex As Exception
            LogToFile("Game end error", New With {
                .error = ex.Message,
                .gameId = gameId,
                .endGameAction = endGameAction,
                .endGameUserId = endGameUserId
            })
            Return Task.CompletedTask
        End Try
    End Function

    Public Function LogGameMove(moveLogData As Object) As Task
        Try
            Dim jObject = DirectCast(moveLogData, JObject)
            Dim userId As Integer = jObject.Value(Of String)("userId")
            Dim gameId = jObject.Value(Of String)("gameId")

            ' Get current game state to access move number
            Dim gameState As GameState = gameStates.GetOrAdd(gameId, Function(key)
                                                                         Dim newState = New GameState()
                                                                         newState.CurrentMoveNumber = 0
                                                                         Return newState
                                                                     End Function)
            ' Increment move number
            gameState.CurrentMoveNumber += 1

            Dim move As New ChessMove With {
                .GameId = gameId,
                .PlayerRole = jObject.Value(Of String)("playerRole"),
                .FromRow = jObject.Value(Of Integer)("fromRow"),
                .FromCol = jObject.Value(Of Integer)("fromCol"),
                .ToRow = jObject.Value(Of Integer)("toRow"),
                .ToCol = jObject.Value(Of Integer)("toCol"),
                .PieceMoved = jObject.Value(Of String)("pieceMoved"),
                .PieceCaptured = jObject.Value(Of String)("pieceCaptured"),
                .UserId = userId,
                .MoveNumber = gameState.CurrentMoveNumber  ' Add the move number
            }

            ChessGameLogger.DB_LogMove(move)

            Return Task.CompletedTask

        Catch ex As Exception
            LogToFile("Error in LogGameMove", New With {
                .error = ex.Message,
                .stackTrace = ex.StackTrace
            })
            Return Task.CompletedTask
        End Try
    End Function

    Public Function UpdateFirstPlayerSelection(gameId As String, isPlebs As Boolean) As Task
        Try
            System.Diagnostics.Debug.WriteLine($"UpdateFirstPlayerSelection - Received: gameId={gameId}, isPlebs={isPlebs}")

            Dim gameState As GameState = Nothing
            If Not gameStates.TryGetValue(gameId, gameState) Then
                ' Create new game state if it doesn't exist
                gameState = New GameState()
                gameStates.TryAdd(gameId, gameState)
                System.Diagnostics.Debug.WriteLine($"Created new game state for gameId: {gameId}")
            End If

            ' Now we know gameState exists
            gameState.FirstPlayerIsPlebs = isPlebs
            gameState.IsPlebsTurn = isPlebs

            System.Diagnostics.Debug.WriteLine($"Game state updated: FirstPlayerIsPlebs={gameState.FirstPlayerIsPlebs}, IsPlebsTurn={gameState.IsPlebsTurn}")
            Return Task.CompletedTask
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error in UpdateFirstPlayerSelection: {ex.Message}")
            Return Task.CompletedTask
        End Try
    End Function

    Private Function GetUserIdFromConnectionId(connectionId As String) As Integer
        Dim userId As Integer = -1
        If userConnections.TryGetValue(connectionId, userId) Then
            Return userId
        End If
        Return -1
    End Function

    Private Function IsFirstPlayer(gameId As String, connectionId As String) As Boolean
        Dim connections As Tuple(Of String, String) = Nothing
        If gameConnections.TryGetValue(gameId, connections) Then
            Return connections.Item1 = connectionId
        End If
        Return False
    End Function

    Public Overrides Function OnReconnected() As Task
        Dim userId As Integer = -1
        If userConnections.TryGetValue(Context.ConnectionId, userId) Then
            ' Recover game state if needed
            ' Notify other players of reconnection
        End If
        Return MyBase.OnReconnected()
    End Function

    Public Function SendChatMessage(gameId As String, message As String) As Task
        Try
            LogToFile("SendChatMessage - Starting", New With {
                .gameId = gameId,
                .sender = Context.ConnectionId,
                .message = message
            })
            ' First ensure sender is in the group
            Groups.Add(Context.ConnectionId, gameId)

            ' Ensure message goes to ALL members of the group including sender
            Return Clients.Group(gameId).receiveChatMessage(Context.ConnectionId, message)

        Catch ex As Exception
            LogToFile("SendChatMessage - Error", New With {
                .error = ex.Message,
                .stackTrace = ex.StackTrace
            })
            Throw
        End Try
    End Function

    Public Overrides Function OnConnected() As Task
        LogToFile("SignalR Connection Established", New With {
            .connectionId = Context.ConnectionId,
            .userId = If(Context.User?.Identity?.Name, "anonymous")
        })
        Return MyBase.OnConnected()
    End Function

    Private Sub LogToFile(message As String, Optional data As Object = Nothing, Optional GameId As String = "")
        ' Create wrapper that includes ConnectionId
        Dim contextExists = (Context IsNot Nothing)
        Dim connectionIdValue = If(Context?.ConnectionId, "context-null")

        Dim logData As New With {
            .originalData = data,
            .connectionId = connectionIdValue,
            .hasContext = contextExists
        }

        ' Pass to existing logging system
        LoggingHandler.LogToFile(message, logData, GameId)
    End Sub

End Class
