<%@ Page Language="VB" AutoEventWireup="true" CodeFile="Login.aspx.vb" Inherits="Login" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title><PERSON><PERSON> - Plebs vs Aristoi</title>
    <% Response.AddHeader("X-Frame-Options", "DENY") %>
    <% Response.AddHeader("X-XSS-Protection", "1; mode=block") %>
    <% Response.AddHeader("X-Content-Type-Options", "nosniff") %>

    <link href="~/Styles/auth.css" rel="stylesheet" type="text/css" runat="server" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />

    <!-- Add styles for modal -->
    <style>
        .modal {
            display: none;
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.4);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 15% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 500px;
            border-radius: 5px;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

            .close:hover,
            .close:focus {
                color: black;
                text-decoration: none;
                cursor: pointer;
            }

        .message {
            font-size: small;
            display: block;
            margin-top: 10px;
        }

        .error {
            color: red;
        }

        .success {
            color: green;
        }


        @media screen and (max-width: 1024px) {
            html, body {
                height: 100%;
                margin: 0;
                padding: 0;
            }

            body {
 
            }
            .auth-container {
              margin-top:300px;
            }
 
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <asp:ScriptManager runat="server" EnablePageMethods="true" />

        <!-- Main Login Form -->
        <div class="auth-container">
            <h2>Login</h2>

            <div class="form-group">
                <asp:Label runat="server" AssociatedControlID="txtUsername">Username:</asp:Label>
                <asp:TextBox ID="txtUsername" runat="server" CssClass="form-control"
                    MaxLength="50" AutoCompleteType="Disabled"></asp:TextBox>
                <asp:RequiredFieldValidator runat="server" ControlToValidate="txtUsername"
                    ErrorMessage="Username is required" CssClass="error" Display="Dynamic"
                    ValidationGroup="LoginGroup" />
            </div>

            <div class="form-group">
                <asp:Label runat="server" AssociatedControlID="txtPassword">Password:</asp:Label>
                <div class="password-container">
                    <asp:TextBox ID="txtPassword" runat="server" TextMode="Password"
                        CssClass="form-control" MaxLength="50"></asp:TextBox>
                    <button type="button" class="password-toggle"
                        onclick="togglePassword('txtPassword', this); return false;">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
                <asp:RequiredFieldValidator runat="server" ControlToValidate="txtPassword"
                    ErrorMessage="Password is required" CssClass="error" Display="Dynamic"
                    ValidationGroup="LoginGroup" />
            </div>

            <asp:Button ID="btnLogin" runat="server" Text="Login"
                OnClick="btnLogin_Click" CssClass="btn-primary"
                ValidationGroup="LoginGroup" />

            <asp:Label ID="lblError" runat="server" CssClass="error"></asp:Label>

            <div class="auth-links">
                <br />
                Don't have an account? 
                <br />
                <a href="Register.aspx">Register here</a>
                <br />
                <a href="#" onclick="showResetModal(); return false;">Forgot Password?</a>
            </div>
        </div>

        <!-- Password Reset Modal -->
        <div id="resetModal" class="modal">
            <div class="modal-content">
                <span class="close" onclick="hideResetModal()">&times;</span>
                <h3>Reset Password</h3>

                <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
                    <ContentTemplate>
                        <div class="form-group">
                            <asp:Label runat="server" AssociatedControlID="txtResetEmail">Email Address:</asp:Label>
                            <asp:TextBox ID="txtResetEmail" runat="server" CssClass="form-control"
                                TextMode="Email" MaxLength="100"></asp:TextBox>
                            <asp:RequiredFieldValidator runat="server" ControlToValidate="txtResetEmail"
                                ErrorMessage="Email is required" CssClass="error" Display="Dynamic"
                                ValidationGroup="ResetGroup" EnableClientScript="true" />
                            <asp:RegularExpressionValidator runat="server" ControlToValidate="txtResetEmail"
                                ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*"
                                ErrorMessage="Invalid email format" CssClass="error" Display="Dynamic"
                                ValidationGroup="ResetGroup" EnableClientScript="true" />
                        </div>

                        <asp:Button ID="btnRequestReset" runat="server" Text="Request Reset"
                            OnClick="btnRequestReset_Click" CssClass="btn-primary"
                            ValidationGroup="ResetGroup" />

                        <asp:Label ID="lblResetMessage" runat="server" CssClass="message"></asp:Label>
                    </ContentTemplate>
                    <Triggers>
                        <asp:AsyncPostBackTrigger ControlID="btnRequestReset" EventName="Click" />
                    </Triggers>
                </asp:UpdatePanel>
            </div>
        </div>

        <!-- Add this modal for password reset -->
        <div id="resetPasswordModal" class="modal">
            <div class="modal-content">
                <span class="close" onclick="hideResetPasswordModal()">&times;</span>
                <h3>Set New Password</h3>

                <asp:UpdatePanel ID="UpdatePanel2" runat="server">
                    <ContentTemplate>
                        <div class="form-group">
                            <asp:Label runat="server" AssociatedControlID="txtNewPassword">New Password:</asp:Label>
                            <div class="password-container">
                                <asp:TextBox ID="txtNewPassword" runat="server" TextMode="Password"
                                    CssClass="form-control" MaxLength="50"></asp:TextBox>
                                <button type="button" class="password-toggle"
                                    onclick="togglePassword('txtNewPassword', this)">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <asp:RequiredFieldValidator runat="server" ControlToValidate="txtNewPassword"
                                ErrorMessage="New password is required" CssClass="error" Display="Dynamic"
                                ValidationGroup="ResetPasswordGroup" />
                        </div>

                        <div class="form-group">
                            <asp:Label runat="server" AssociatedControlID="txtConfirmPassword">Confirm Password:</asp:Label>
                            <div class="password-container">
                                <asp:TextBox ID="txtConfirmPassword" runat="server" TextMode="Password"
                                    CssClass="form-control" MaxLength="50"></asp:TextBox>
                                <button type="button" class="password-toggle"
                                    onclick="togglePassword('txtConfirmPassword', this)">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <asp:RequiredFieldValidator runat="server" ControlToValidate="txtConfirmPassword"
                                ErrorMessage="Confirm password is required" CssClass="error" Display="Dynamic"
                                ValidationGroup="ResetPasswordGroup" />
                            <asp:CompareValidator runat="server" ControlToValidate="txtConfirmPassword"
                                ControlToCompare="txtNewPassword" ErrorMessage="Passwords do not match"
                                CssClass="error" Display="Dynamic" ValidationGroup="ResetPasswordGroup" />
                        </div>

                        <asp:Button ID="btnSetNewPassword" runat="server" Text="Set New Password"
                            OnClick="btnSetNewPassword_Click" CssClass="btn-primary"
                            ValidationGroup="ResetPasswordGroup" />

                        <asp:Label ID="lblResetPasswordMessage" runat="server" CssClass="message"></asp:Label>
                        <asp:HiddenField ID="hdnResetToken" runat="server" />
                    </ContentTemplate>
                </asp:UpdatePanel>
            </div>
        </div>
    </form>

    <script type="text/javascript">
        function togglePassword(controlId, button) {
            console.log('togglePassword called for: ' + controlId);
            var passwordInput = document.getElementById(controlId);
            if (!passwordInput) {
                console.log('Password input not found: ' + controlId);
                return;
            }

            var icon = button.querySelector('i');
            if (!icon) {
                console.log('Icon element not found');
                return;
            }

            if (passwordInput.type === "password") {
                console.log('Changing to text input');
                passwordInput.type = "text";
                icon.classList.remove("fa-eye");
                icon.classList.add("fa-eye-slash");
            } else {
                console.log('Changing to password input');
                passwordInput.type = "password";
                icon.classList.remove("fa-eye-slash");
                icon.classList.add("fa-eye");
            }
        }

        function showResetModal() {
            console.log('Showing reset modal');
            var modal = document.getElementById('resetModal');
            if (!modal) {
                console.log('Reset modal not found!');
                return;
            }
            modal.style.display = 'block';
            console.log('Reset modal displayed');
        }

        function hideResetModal() {
            console.log('Hiding reset modal');
            var modal = document.getElementById('resetModal');
            if (!modal) {
                console.log('Reset modal not found!');
                return;
            }
            modal.style.display = 'none';
            console.log('Reset modal hidden');
        }

        // Close modal if clicking outside of it
        window.onclick = function (event) {
            console.log('Window click detected');
            var modal = document.getElementById('resetModal');
            if (event.target == modal) {
                // Check if this is not a postback
                if (!event.target.querySelector('form')) {
                    console.log('Click was outside modal - hiding');
                    hideResetModal();
                } else {
                    console.log('Postback detected - keeping modal open');
                }
            }
        }

        // Add handler for successful password reset request
        function onResetRequestComplete() {
            console.log('Reset request completed');
            // Keep modal open if there are validation errors
            if (!Page_IsValid) {
                console.log('Validation failed - keeping modal open');
                return;
            }
        }

        function showResetPasswordModal() {
            document.getElementById('resetPasswordModal').style.display = 'block';
        }

        function hideResetPasswordModal() {
            document.getElementById('resetPasswordModal').style.display = 'none';
        }

        // Check for reset token on page load
        window.onload = function () {
            var urlParams = new URLSearchParams(window.location.search);
            var token = urlParams.get('token');
            if (token) {
                console.log('Reset token found');
                document.getElementById('hdnResetToken').value = token;
                showResetPasswordModal();
            }
        }
    </script>
</body>
</html>
