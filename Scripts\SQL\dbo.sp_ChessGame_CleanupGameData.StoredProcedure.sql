USE [Elthos<PERSON><PERSON><PERSON>]
GO
/****** Object:  StoredProcedure [dbo].[sp_ChessGame_CleanupGameData]    Script Date: 4/5/2025 1:17:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [dbo].[sp_ChessGame_CleanupGameData]

as

SELECT * FROM ChessGames WHERE GameID  IN (SELECT GameID FROM ChessGames WHERE EndTime IS NULL)
SELECT * FROM ChessGames WHERE EndTime IS NULL
SELECT * FROM ChessGames WHERE GameID  NOT IN (SELECT GameID FROM ChessMoves)
SELECT * FROM ChessMoves WHERE GameID  NOT IN (SELECT GameID FROM ChessGames)
SELECT * FROM ChessGames WHERE EndGameAction = 'Abandoned'

DELETE FROM ChessMoves WHERE GameID  NOT IN (SELECT GameID FROM ChessGames)
DELETE FROM ChessGames WHERE GameID  IN (SELECT GameID FROM ChessGames WHERE EndTime IS NULL)
DELETE FROM ChessGames WHERE EndTime IS NULL
DELETE FROM ChessMoves WHERE GameID  NOT IN (SELECT GameID FROM ChessGames)
DELETE FROM ChessGames WHERE GameID  NOT IN (SELECT GameID FROM ChessMoves)
DELETE FROM ChessGames WHERE EndGameAction = 'Abandoned'

SELECT * FROM ChessGames WHERE GameID  IN (SELECT GameID FROM ChessGames WHERE EndTime IS NULL)
SELECT * FROM ChessGames WHERE EndTime IS NULL
SELECT * FROM ChessMoves WHERE GameID  NOT IN (SELECT GameID FROM ChessGames)
SELECT * FROM ChessGames WHERE GameID  NOT IN (SELECT GameID FROM ChessMoves)
SELECT * FROM ChessGames WHERE EndGameAction = 'Abandoned'

SELECT * FROM ChessGames

GO
