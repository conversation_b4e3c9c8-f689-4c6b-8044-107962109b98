<%@ Page Language="VB" AutoEventWireup="true" CodeFile="Default.aspx.vb" Inherits="_Default" %>

<!DOCTYPE html>
<html>
<head>
    <title><PERSON><PERSON><PERSON> vs Aristoi Chess</title>
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@600&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@400;600&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Crimson+Text:wght@400;600&display=swap" rel="stylesheet">

    <link rel="icon" type="image/png" href="/images/Favicon_PA.png">

    <link href="Styles/PvAChess.css" rel="stylesheet" type="text/css" />
    <link href="Styles/ChessAnimations.css" rel="stylesheet" type="text/css" />

    <script src="Scripts/ChessCommon.js"></script>
    <script src="Scripts/jquery-3.7.1.min.js"></script>
    <script src="Scripts/jquery.signalR-2.4.3.min.js"></script>
    <script src="signalr/hubs"></script>

    <style>
        @media screen and (max-width: 1024px) {
            .focus-mode .square {
                font-size: 80px;
                height: 96px;
                width: 96px;
            }

            .focus-mode .container {
                background-color: transparent;
                margin-bottom: 100px;
                margin-right: 300px;
                max-width: max-content;
                width: 98%;
                position: relative;
                transition: all 0.3s ease-in-out;
            }
        }
    </style>

</head>
<body>
    <!-- Add this right after the <body> tag -->
    <audio id="moveSound" preload="auto">
        <source src="Sounds/move.wav" type="audio/wav">
    </audio>
    <audio id="captureSound" preload="auto">
        <source src="Sounds/tada.wav" type="audio/wav">
    </audio>
    <audio id="swapSound" preload="auto">
        <source src="Sounds/swap.wav" type="audio/wav">
    </audio>

    <div>
        <form id="form1" runat="server">

            <div class="header-controls">
                <asp:Label ID="lblUserDisplay" runat="server" CssClass="UserDisplay" Text=""></asp:Label>
                <asp:LinkButton ID="btnLeaderBoard" runat="server" CssClass="lnkbtn" Text="Leader Board"></asp:LinkButton>
                <asp:LinkButton ID="btnChessGameReplay" runat="server" CssClass="lnkbtn" Text="Replay"></asp:LinkButton>
                <button id="btnFocusModeToggle" class="focus-mode-btn" onclick="toggleFocusMode(event)">
                    <span>Focus Mode</span>
                </button>
                <button id="btnStrategyGuide" class="strategy-guide-btn" title="Strategy Guide">?</button>
                <asp:LinkButton ID="btnLogout" runat="server" CssClass="lnkbtn" Text="Logout"></asp:LinkButton>
            </div>

            <div class="page-wrapper">
                <div class="container">
                    <div class="game-section">
                        <h2 class="game-title">Plebs vs Aristoi Chess</h2>
                        <div class="turn-indicator">Turn: <span id="turn">Plebs (Pawns)</span></div>
                        <div class="turn-status">
                            <div class="turn-light waiting"></div>
                            <div id="turn-message">Waiting for game to start...</div>
                        </div>
                        <div class="board" id="board"></div>
                        <div class="prison-container">
                            <div>
                                <div class="prison-label">Pleb Prison (Pawns):</div>
                                <div id="plebs-prison" class="prison"></div>
                            </div>
                            <div>
                                <div class="prison-label">Aristoi Prison (Nobles):</div>
                                <div id="aristoi-prison" class="prison"></div>
                            </div>
                        </div>

                        <!-- Add the chat interface here -->
                        <div id="chat-interface" class="chat-interface hidden">
                            <div class="chat-header">
                                <h3>Game Chat</h3>
                            </div>
                            <div id="chat-messages" class="chat-messages"></div>
                            <div class="chat-input-area">
                                <input type="text" id="chat-input" placeholder="Type your message..." />
                                <button onclick="sendChatMessage(); return false;">Send</button>
                            </div>
                        </div>
                    </div>

                    <div class="info-section">
                        <div class="team-selection">
                            <div style="white-space: nowrap;">
                                <button id="startNewGameBtn" onclick="createNewGame(); return false;" disabled>Start New Game</button>
                                <span style="margin-left: 20px; border: 1px solid gray; padding: 12px; border-radius: 12px; box-shadow: 2px 2px 3px gray;">
                                    <label>
                                        <input type="checkbox" id="soloPlayCheck" onchange="handleSoloPlayToggle(this)">
                                        <b>Solo Mode</b> (play both sides for practice)
                                    </label>
                                </span>
                            </div>
                            <br />
                            <br />
                            <div class="radio-options">
                                <label><b>First Mover</b>:</label>
                                <label>
                                    <input type="radio" name="first-move" value="plebs" checked>
                                    Plebs (Pawns)
                                </label>
                                <label>
                                    <input type="radio" name="first-move" value="aristoi">
                                    Aristoi (Noble Pieces)
                                </label>
                            </div>
                            <div class="victory-mode-container">
                                <span class="victory-mode-selection">Pleb Victory Conditions:</span>
                                <select id="plebVictoryMode" class="DropDownList">
                                    <option value="all">Capture All Nobles           </option>
                                    <option value="half">Capture More Than Half Nobles</option>
                                    <option value="royalty">Capture All Kings and Queens </option>
                                </select>
                            </div>
                        </div>
                        <div id="joinGameSection" class="join-game-section" style="margin-bottom: 20px;">
                            <div class="input-group" style="margin-top: 10px;">
                                <input type="text" id="gameId" class="form-control" placeholder="Enter Game ID to join"
                                    style="padding: 5px; margin-right: 10px; width: 150px;" />
                                <button onclick="joinGame(); return false;" class="btn btn-primary">Join Game</button>
                            </div>
                        </div>
                        <div id="gameStatus" class="game-status" style="margin: 15px 0; padding: 10px; background-color: #f5f5f5; border-radius: 4px;">
                            <div id="gameIdDisplay" style="margin-bottom: 10px;">
                                <strong>Game ID:</strong> <span id="currentGameId">Not started</span>
                                <br>
                                <i style="font-size: 12px;">Send the Game ID to your opponent to join the game</i>
                            </div>
                            <div id="playerStatus" style="margin-bottom: 10px;">
                                <strong>Role:</strong> <span id="playerRole">Waiting for game...</span>
                            </div>
                            <div id="waitingMessage" style="display: none; color: #666;">
                                Waiting for opponent to join...
                            </div>
                        </div>

                        <div class="Instructions">
                            <h3>About Plebs vs Aristoi Chess</h3>

                            <p class="game-description">
                                Welcome to a strange and terrible variant of chess where the Plebians (lowly pawns)
                                have finally had it up to <i>here</i> with those rotten tyrannical Aristoi (noble pieces), and vice versa!
                                Get ready for <b>The Glorious Revolution</b>, and prepare for an epic battle of utterly fiendish, mind-bending strategy!
                                Beware, ye Noble brood - the hoi polloi are clever, armed and dangerous! You'll see.
                            </p>

                            <h3>How to Play</h3>
                            <ul class="rules-list">
                                <li><strong>Starting the Game:</strong>
                                    <ul>
                                        <li>Choose which team moves first using the radio buttons</li>
                                        <li>The person who creates the game is the first mover</li>
                                        <li>Click "Start New Game" to begin</li>
                                        <li>Enter the Game ID in the input field and click "Join Game" to join an existing game</li>
                                    </ul>
                                </li>
                                <li><strong>Moving Pieces:</strong>
                                    <ul>
                                        <li>Click on a piece to select it - valid moves will be highlighted in green</li>
                                        <li>Click on a highlighted square to move your piece there</li>
                                        <li>Click anywhere else or on the piece again to deselect the piece</li>
                                    </ul>
                                </li>
                                <li><strong>Turn Order:</strong>
                                    <ul>
                                        <li>The current team's turn is shown above the board</li>
                                        <li>Teams alternate turns after each move</li>
                                    </ul>
                                </li>

                            </ul>

                            <h3>Game Rules</h3>
                            <ul class="rules-list">
                                <li><strong>Teams:</strong>
                                    <ul>
                                        <li>The Plebians: All Pawns (both black and white)</li>
                                        <li>The Aristoi: All Noble pieces (kings, queens, bishops, knights, and rooks, both black and white)</li>
                                    </ul>
                                </li>
                                <li><strong>Victory Conditions:</strong>
                                    <ul>
                                        <li>Aristoi win if they capture all the pawns</li>
                                        <li class="PlebVictoryConditions">The Plebians win if they capture all the Aristoi</li>
                                        <li>If no piece is taken after 21 consecutive moves then it is a Draw</li>
                                    </ul>
                                </li>
                                <li><strong>Pawn Movement:</strong>
                                    <ul>
                                        <li><strong>Loyal Pawns (unmoved):</strong>
                                            <ul>
                                                <li>At first, all pawns are decent, loyal and obedient. They move like normal, ordinary, good pawns.</li>
                                                <li>As such, pawns begin with traditional chess moves: forward one or two squares, and diagonal captures
                                                    against enemy pieces (Nobles)</li>
                                                <li>Although untraditional, loyal Pawns may also capture Nobles of the opposite color if they are horizontally next to them,
                                                    as well as diagnonally in front of them. In all other respects Loyal Pawns behave strictly according to tradition.
                                                </li>
                                                <li>It is important to note that Nobles can be taken by Loyal Pawns of the opposite color in any direction, but Loyal
                                                    Pawns of the same color cannot be taken except diagonally forward.</li>
                                            </ul>
                                        </li>
                                        <li><strong>The Rebel Pawns:</strong>
                                            <ul>
                                                <li>As an initial act of defiance, Loyal (unmoved) Pawns can capture their own color's Nobles when the Noble lands
                                                    diagonally in front of them</li>
                                                <li>"<b>Leap of Faith</b>": Loyal pawns may move forward one or two squares and
                                                    swap places with a pawn on the destination square.
                                                </li>
                                                <li>However, pawns cannot capture Nobles by a Leap of Faith, so be thankful for small mercies!</li>
                                                <li><strong>Rebel Plebs:</strong>
                                                    <ul>
                                                        <li>After they take their first move pawns become Radicalized</li>
                                                        <li>They can move forward one square at a time in any direction</li>
                                                        <li>They can attack adjacent Noble pieces in any direction</li>
                                                        <li>They can swap places with other pawns that they can reach when moving any direction</li>
                                                    </ul>
                                                </li>
                                            </ul>
                                        </li>
                                        <li><strong>Revolutionary Hero Pawns!</strong>
                                            <ul>
                                                <li>When taking a Queen or King, or when reaching the opposite edge of the board, pawns become "Hero Pawns" (marked with a bright halo)</li>
                                                <li>Revolutionary Hero Pawns can:
                                                    <ul>
                                                        <li>Move like Queens in any direction!</li>
                                                        <li>Swap places with friendly pawns up to 3 squares away along their line of movement!</li>
                                                    </ul>
                                                </li>
                                                <li>Up to three Revolutionary Hero Pawns are allowed at a time!</li>
                                                <li>When a Revolutionary Hero Pawn is captured, another pawn can be promoted as a new Revolutionary Hero Pawn when it
                                                    reaches the opposite end of the board, but not if it is already sitting on it.
                                                </li>
                                            </ul>
                                        </li>
                                    </ul>
                                </li>
                                <li><strong>Noble Piece Rules:</strong>
                                    <ul>
                                        <li>Nobles behave according to the rules of traditional chess</li>
                                        <li>Naturally, due to the rebellion, all Nobles are allies, and do not capture other noble pieces of the other color</li>
                                        <li>They are allowed to capture pawns following the traditional movement and capture rules</li>
                                        <li>Of course, as one would expect, because unmoved Pawns are still Loyal subjects (<i>albeit, under suspicion</i>),
                                            Nobles may not imprison the non-radicalized pawns of their own color, as is just, right and proper.
                                        </li>
                                    </ul>
                                </li>

                                <li><strong>Quick Review of Pawn Movement:</strong>
                                    <ul>
                                        <li>Initial "Leap of Faith":
                                            <ul>
                                                <li>Requires clear path forward</li>
                                                <li>Can move one or two squares forward on first move</li>
                                                <li>Automatically radicalizes pawn after move</li>
                                                <li>Can capture nobles of their own color on the first move only when the noble is diagonally in front of them!</li>
                                            </ul>
                                        </li>
                                        <li>After becoming Radical:
                                            <ul>
                                                <li>Can attack nobles in ANY direction</li>
                                                <li>Can move in any direction one square at a time</li>
                                                <li>Can pass through and swap places with friendly pawns</li>
                                            </ul>
                                        </li>
                                        <li>After becoming Revolutionary Hero:
                                            <ul>
                                                <li>Can move and attack like Queens</li>
                                                <li>Can swap places with any Pawn up to 3 squares along their line of movement</li>
                                                <li>Maximum of 3 Revolutionary Hero Pawns active at once</li>
                                            </ul>
                                        </li>
                                    </ul>
                                </li>
                            </ul>

                            <h3>Strategy Tips</h3>
                            <ul class="rules-list">
                                <li><strong>Pleb Strategy (Pawns)</strong>
                                    <ul>

                                        <li><strong>Perspective</strong>
                                            <ul>
                                                <li>Villainous usurpers, the Aristoi are wicked Tyrants who must be overthrown at all cost!
                                            Stick together!  Stay united!  <i>Viva la Revoluci&#243;n!</i></li>
                                            </ul>
                                        </li>
                                        <li><strong>Super Pawn Creation</strong>
                                            <ul>
                                                <li>Target enemy Kings and Queens for instant Super Pawn promotion</li>
                                                <li>Alternatively, push pawns to the opposite end of the board</li>
                                                <li>Remember: Only three Super Pawns allowed at once</li>
                                            </ul>
                                        </li>
                                        <li><strong>Radicalization Tactics</strong>
                                            <ul>
                                                <li>Move pawns from their starting position to radicalize them</li>
                                                <li>Use radicalized pawns' multi-directional attack capability</li>
                                                <li>Create protected chains of radicalized pawns</li>
                                                <li>Create L-shaped formations with radical pawns</li>
                                                <li>Use horizontal captures to break noble piece formations</li>
                                                <li>Leave initial Loyalist Pawns in place to box in the Aristoi</li>
                                                <li>Use pawn-swapping to quickly reposition forces</li>
                                                <li>Control the center where pawns can threaten in multiple directions</li>
                                                <li>Protect non-radicalized pawns until they can move safely</li>
                                                <li>Use pawn-swapping to quickly reposition forces</li>
                                                <li>Control the center where pawns can threaten in multiple directions</li>
                                                <li>Protect non-radicalized pawns until they can move safely</li>

                                            </ul>
                                        </li>
                                        <li><strong>Coordinated Movement</strong>
                                            <ul>
                                                <li>Use pawn-swapping to quickly reposition forces</li>
                                                <li>Control the center where pawns can threaten in multiple directions</li>
                                                <li>Protect non-radicalized pawns until they can move safely</li>
                                            </ul>
                                        </li>
                                    </ul>
                                </li>
                                <li><strong>Aristoi Strategy (Nobles)</strong>
                                    <ul>
                                        <li><strong>Perspective</strong>
                                            <ul>
                                                <li>Civilization depends on your valor and intelligence, valiant heros!</li>
                                            </ul>
                                        </li>
                                        <li><strong>Piece Protection</strong>
                                            <ul>
                                                <li>Protect Kings and Queens to prevent Super Pawn creation</li>
                                                <li>Keep mobile pieces (Knights, Bishops) active for quick responses</li>
                                                <li>Use long-range pieces to control key squares</li>
                                            </ul>
                                        </li>
                                        <li><strong>Tactical Priorities</strong>
                                            <ul>
                                                <li>Target isolated pawns before they can radicalize</li>
                                                <li>Prevent formation of pawn chains</li>
                                                <li>Remember: You cannot capture non-radicalized pawns of your color</li>
                                            </ul>
                                        </li>
                                    </ul>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div id="strategyGuide" class="strategy-guide">
                <div class="strategy-guide-content">
                    <h2>Strategy Guide</h2>
                    <button class="close-btn">&times;</button>

                    <h3>Understanding the Revolution</h3>
                    <p>
                        This is no ordinary chess variant - it's a battle between the unified masses (Plebs)
                       and the powerful elite (Aristoi). Victory requires different thinking for each side.
                    </p>

                    <h3>Pleb Strategy (Pawns)</h3>
                    <ul>
                        <li><strong>Coordinated Movement</strong>
                            <ul>
                                <li>Focus on group tactics rather than individual attacks</li>
                                <li>Create protected advance positions using pawn-swapping</li>
                                <li>Control the center where pawns can threaten in multiple directions</li>
                            </ul>
                        </li>
                        <li><strong>Revolutionary Tactics</strong>
                            <ul>
                                <li>Use radicalized pawns' multi-directional attacks to create threat zones</li>
                                <li>Surround and trap noble pieces with multiple pawns</li>
                                <li>Consider sacrificial plays where losing one pawn leads to capturing a valuable noble</li>
                            </ul>
                        </li>
                        <li><strong>Revolutionary Hero Pawn Mastery</strong>
                            <ul>
                                <li>Use the 3-square vertical swap to save threatened pawns</li>
                                <li>Rapidly deploy fresh pawns to the front lines</li>
                                <li>Create sudden tactical opportunities through quick repositioning</li>
                                <li>Stay together.  Don't let the Aristoi pick off stragglers.</li>
                            </ul>
                        </li>
                    </ul>

                    <h3>Aristoi Strategy (Nobles)</h3>
                    <ul>
                        <li><strong>Piece Coordination</strong>
                            <ul>
                                <li>Maintain safe distances between noble pieces</li>
                                <li>Use long-range pieces to control key squares</li>
                                <li>Create escape routes for threatened pieces</li>
                            </ul>
                        </li>
                        <li><strong>Tactical Priorities</strong>
                            <ul>
                                <li>Prevent pawn clusters from forming</li>
                                <li>Target isolated pawns when possible</li>
                                <li>Keep mobile pieces (Knights, Bishops) active for quick responses</li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>


            <script>
                // Add at the top with other game constants
                const MAX_SUPER_PAWNS = 3; // or whatever number you want to allow
                const DRAW_MOVE_THRESHOLD = 21; // 21 moves until Draw

                let movesWithoutCapture = 0;

                let firstPlayerTurn = false;
                let selectedPiece = null;
                let isPlebsTurn = true;
                let board = [];
                let promotedPawns = new Set();
                let capturedPlebs = [];
                let capturedAristoi = [];
                let currentSuperPawn = null;
                let gameHub;
                let currentGameId = null;
                let isFirstPlayer = true;
                let isProcessingLocalMove = false;
                let isSoloPlay = false;
                let gameStartTime = null;
                let firstPlayerTurnValue = false;
                let plebVictoryMode = 'all';

                // Add a new map to track pawn states
                let pawnStates = new Map(); // Key: "row,col", Value: PAWN_STATES

                $(document).ready(function () {
                    clientLogToFile('Document ready triggered');  // Add this line

                    clientLogToFile('Initial state check', {
                        isFirstPlayer: isFirstPlayer,
                        window_firstPlayerTurnValue: window.firstPlayerTurnValue,
                        typeof_isFirstPlayer: typeof isFirstPlayer,
                        isFirstPlayer_isDefined: typeof isFirstPlayer !== 'undefined'
                    });
                    // clientLogToFile('After first log');  // Add this line
                    //
                    // clientLogToFile('Game page loaded');
                    //
                    // clientLogToFile('After second log');  // Add this line
                });

                function initializeBoard() {
                    board = [
                        ['black-rook', 'black-knight', 'black-bishop', 'black-queen', 'black-king', 'black-bishop', 'black-knight', 'black-rook'],
                        ['black-pawn', 'black-pawn', 'black-pawn', 'black-pawn', 'black-pawn', 'black-pawn', 'black-pawn', 'black-pawn'],
                        [null, null, null, null, null, null, null, null],
                        [null, null, null, null, null, null, null, null],
                        [null, null, null, null, null, null, null, null],
                        [null, null, null, null, null, null, null, null],
                        ['white-pawn', 'white-pawn', 'white-pawn', 'white-pawn', 'white-pawn', 'white-pawn', 'white-pawn', 'white-pawn'],
                        ['white-rook', 'white-knight', 'white-bishop', 'white-queen', 'white-king', 'white-bishop', 'white-knight', 'white-rook']
                    ];

                    // Initialize all pawns as NORMAL
                    pawnStates.clear();
                    for (let i = 0; i < 8; i++) {
                        for (let j = 0; j < 8; j++) {
                            if (board[i][j]?.endsWith('pawn')) {
                                pawnStates.set(`${i},${j}`, PAWN_STATES.NORMAL);
                            }
                        }
                    }
                    renderBoard();
                }



                function handleSquareClick_Solo(event) {
                    if (!currentGameId) {
                        alert('Please start a new game first');
                        return;
                    }

                    console.log('handleSquareClick_Solo');
                    console.log('userid: ' + userId);

                    const row = parseInt(event.target.dataset.row);
                    const col = parseInt(event.target.dataset.col);

                    // Debug logging
                    //clientLogToFile('handleSquareClick_Solo', {
                    //    row: row,
                    //    col: col,
                    //    selectedPiece: selectedPiece,
                    //    currentTurn: isPlebsTurn ? 'Plebs' : 'Aristoi'
                    //});

                    // Check if clicking the currently selected piece
                    if (selectedPiece && selectedPiece.row === row && selectedPiece.col === col) {
                        selectedPiece = null;
                        clearHighlights();
                        return;
                    }

                    // Clear previous highlights
                    // selectedPiece = null;
                    clearHighlights();

                    if (selectedPiece && isValidMove(selectedPiece.row, selectedPiece.col, row, col)) {
                        // Log the move for analysis
                        const moveLogData = {
                            gameId: currentGameId,
                            playerRole: isPlebsTurn ? "Plebs" : "Aristoi",
                            fromRow: selectedPiece.row,
                            fromCol: selectedPiece.col,
                            toRow: row,
                            toCol: col,
                            pieceMoved: board[selectedPiece.row][selectedPiece.col],
                            pieceCaptured: board[row][col] || '',
                            userId: userId
                        };

                        // Add this line to actually log the move
                        gameHub.server.logGameMove(moveLogData);

                        // Execute the move
                        executeMove(selectedPiece.row, selectedPiece.col, row, col);

                        // Handle turn changes
                        isPlebsTurn = !isPlebsTurn;

                        // Update UI and check game state
                        updateTurnStatus();

                        // Ensure piece is deselected and highlights are cleared
                        selectedPiece = null;
                        clearHighlights();

                    } else if (board[row][col]) {
                        const piece = board[row][col];
                        const isPawn = piece.includes('pawn');

                        // Simple solo play piece selection
                        if ((isPlebsTurn && isPawn) || (!isPlebsTurn && !isPawn)) {
                            selectedPiece = { row, col };
                            highlightSquare(row, col);
                            highlightPossibleMoves(row, col);

                            // Add visual feedback
                            const turnMessage = document.getElementById('turn-message');
                            if (turnMessage) {
                                turnMessage.textContent = `Selected ${isPawn ? 'Pawn' : 'Noble'} at position ${row},${col}`;
                            }
                        }
                    } else {
                        // Clear selection when clicking empty or invalid square
                        selectedPiece = null;
                        clearHighlights();
                    }
                }

                function handleSquareClick(event) {
                    clientLogToFile('handleSquareClick entry', {
                        isSoloPlay: isSoloPlay,
                        currentGameId: currentGameId,
                        isFirstPlayer: isFirstPlayer,
                        isPlebsTurn: isPlebsTurn,
                        firstPlayerTurn: firstPlayerTurn
                    });

                    console.log('handleSquareClick entry');

                    // Prevent moves if waiting for Player 2 in multiplayer mode
                    const waitingMessage = document.getElementById('waitingMessage');
                    if (!isSoloPlay && waitingMessage && waitingMessage.textContent === 'Waiting for opponent to join...') {
                        clientLogToFile('Waiting for opponent - move prevented');
                        return;
                    }

                    if (!currentGameId) {
                        clientLogToFile('No current game ID - move prevented');
                        alert('Please join or create a game first');
                        return;
                    }

                    if (!isPlayerTurn()) {
                        clientLogToFile('Not player turn - move prevented');
                        console.log('handleSquareClick - Not player turn - move prevented');

                        selectedPiece = null;
                        clearHighlights();
                        return;
                    }

                    const row = parseInt(event.target.dataset.row);
                    const col = parseInt(event.target.dataset.col);

                    console.log('Processing move attempt', {
                        row: row,
                        col: col,
                        selectedPiece: selectedPiece,
                        piece: board[row][col]
                    });

                    console.log('handleSquareClick - BEFORE CURRENTLY SELECTED PIECE CHECK selectedPiece:', selectedPiece);

                    // Check if clicking the currently selected piece
                    if (selectedPiece && selectedPiece.row === row && selectedPiece.col === col) {
                        selectedPiece = null;
                        clearHighlights();
                        return;
                    }

                    console.log('handleSquareClick - AFTER CURRENTLY SELECTED PIECE CHECK  selectedPiece:', selectedPiece);

                    // Clear previous highlights
                    // selectedPiece = null;
                    clearHighlights();

                    console.log('Before Valid move detected');


                    // Case 1: Moving a selected piece
                    if (selectedPiece && isValidMove(selectedPiece.row, selectedPiece.col, row, col)) {
                        clientLogToFile('Valid move detected', {
                            fromRow: selectedPiece.row,
                            fromCol: selectedPiece.col,
                            toRow: row,
                            toCol: col,
                            piece: board[selectedPiece.row][selectedPiece.col],
                            isProcessingLocalMove: isProcessingLocalMove
                        });
                        // Log the move
                        const moveLogData = {
                            gameId: currentGameId,
                            playerRole: isPlebsTurn ? "Plebs" : "Aristoi",
                            fromRow: selectedPiece.row,
                            fromCol: selectedPiece.col,
                            toRow: row,
                            toCol: col,
                            pieceMoved: board[selectedPiece.row][selectedPiece.col],
                            pieceCaptured: board[row][col] || '',
                            userId: userId
                        };
                        gameHub.server.logGameMove(moveLogData);

                        // Execute move and update game state
                        executeMove(selectedPiece.row, selectedPiece.col, row, col);

                        clientLogToFile('Local move executed', {
                            fromRow: selectedPiece.row,
                            fromCol: selectedPiece.col,
                            toRow: row,
                            toCol: col,
                            newIsPlebsTurn: !isPlebsTurn,
                            newFirstPlayerTurn: !firstPlayerTurn
                        });

                        isPlebsTurn = !isPlebsTurn;
                        firstPlayerTurn = !firstPlayerTurn;

                        updateTurnStatus();
                        clientLogToFile('Turn status updated', {
                            isPlebsTurn: isPlebsTurn,
                            firstPlayerTurn: firstPlayerTurn,
                            isFirstPlayer: isFirstPlayer
                        });
                        return;
                    }

                    // Case 2: Selecting a new piece
                    if (board[row][col]) {
                        const piece = board[row][col];
                        const isPawn = piece.includes('pawn');

                        // Determine if this player can move this piece
                        // firstPlayerTurnValue: true means Player 1 chose Plebs, false means Player 1 chose Aristoi
                        const canMoveCurrentPiece = isFirstPlayer ?
                            (window.firstPlayerTurnValue ?
                                (isPlebsTurn && isPawn) :     // Player 1 chose Plebs
                                (!isPlebsTurn && !isPawn)     // Player 1 chose Aristoi
                            ) :
                            (window.firstPlayerTurnValue ?
                                (!isPlebsTurn && !isPawn) :   // Player 2 gets Aristoi
                                (isPlebsTurn && isPawn)       // Player 2 gets Plebs
                            );

                        if (canMoveCurrentPiece) {
                            selectedPiece = { row, col };
                            highlightSquare(row, col);
                            highlightPossibleMoves(row, col);
                        } else {
                            // Clear selection when clicking empty or invalid square
                            selectedPiece = null;
                            clearHighlights();
                        }
                    }
                }

                function isValidMove(fromRow, fromCol, toRow, toCol) {
                    //clientLogToFile('isValidMove() - Checking move validity:', {
                    //    from: [fromRow, fromCol],
                    //    to: [toRow, toCol],
                    //    piece: board[fromRow][fromCol],
                    //    targetSpace: board[toRow][toCol]
                    //});

                    const piece = board[fromRow][fromCol];
                    if (!piece) return false;

                    const isPawn = piece.endsWith('pawn');
                    const deltaRow = toRow - fromRow;
                    const deltaCol = toCol - fromCol;
                    const targetPiece = board[toRow][toCol];
                    const isWhitePiece = piece.includes('white');
                    const isPromotedPawn = isPawn && promotedPawns.has(`${fromRow},${fromCol}`);

                    // For non-pawns, can't move to a square occupied by same piece type (pawn vs non-pawn)
                    if (!isPawn && targetPiece) {
                        const targetIsPawn = targetPiece.endsWith('pawn');
                        if (isPawn === targetIsPawn) {
                            return false;
                        }
                    }
                    /* Pawn Movement Rules in Plebs vs Aristoi Chess:
                     *
                     * 1. Super Pawn (After reaching opposite end):
                     *    - Moves like a queen in any direction
                     *    - Cannot capture other pawns
                     *    - Can swap places with other pawns vertically within 3 squares
                     *    - Only one super pawn allowed at a time
                     *    - Other pawns reaching the end remain regular pawns until super pawn is captured
                     *
                     * 2. Initial Position (First Move):
                     *    - Can move forward one or two squares only
                     *    - Can pass through other pawns
                     *    - Can only capture forward-diagonally (like traditional chess)
                     *
                     * 3. After First Move:
                     *    - Can move forward one square
                     *    - Can pass through other pawns
                     *    - Can capture noble pieces (non-pawns) in any direction
                     *    - Valid capture directions:
                     *      → Sideways (deltaRow = 0, deltaCol = ±1)
                     *      → Forward (deltaRow = 1, deltaCol = 0)
                     *      → Forward-diagonal (deltaRow = 1, deltaCol = ±1)
                     *      → Backward-diagonal (deltaRow = -1, deltaCol = ±1)
                     */
                    if (isPawn) {
                        const pawnState = pawnStates.get(`${fromRow},${fromCol}`);
                        const forwardDirection = isWhitePiece ? -1 : 1;

                        // Super Pawn movement rules - moves like a queen
                        if (isPromotedPawn) {

                            //clientLogToFile('Super Pawn Move Validation', {
                            //    fromPos: `${fromRow},${fromCol}`,
                            //    toPos: `${toRow},${toCol}`,
                            //    deltaRow,
                            //    deltaCol,
                            //    isHorizontal: deltaRow === 0 && deltaCol !== 0,
                            //    isVertical: deltaCol === 0 && deltaRow !== 0,
                            //    isDiagonal: Math.abs(deltaRow) === Math.abs(deltaCol) && deltaRow !== 0,
                            //    targetPiece,
                            //    isPathClear: isPathClear(fromRow, fromCol, toRow, toCol)
                            //});


                            // Queen-like movement: must be horizontal, vertical, or diagonal
                            const isHorizontal = deltaRow === 0 && deltaCol !== 0;
                            const isVertical = deltaCol === 0 && deltaRow !== 0;
                            const isDiagonal = Math.abs(deltaRow) === Math.abs(deltaCol) && deltaRow !== 0;

                            if (!isHorizontal && !isVertical && !isDiagonal) {
                                return false;
                            }

                            // Special case: Swapping with fellow pawns within 3 squares (vertically or diagonally)
                            if (targetPiece && targetPiece.endsWith('pawn')) {
                                return (isVertical || isDiagonal) && Math.abs(deltaRow) <= 3 && isPathClear(fromRow, fromCol, toRow, toCol);
                            }

                            if (!isPathClear(fromRow, fromCol, toRow, toCol)) {
                                return false;
                            }

                            // Can't capture other pawns
                            if (targetPiece && targetPiece.endsWith('pawn')) {
                                return false;
                            }

                            return true;
                        }
                        // Radical pawn movement rules
                        else if (pawnState === PAWN_STATES.RADICAL) {
                            // Check if the move is within one square in any direction
                            if (Math.abs(deltaRow) <= 1 && Math.abs(deltaCol) <= 1) {
                                // For pawn-to-pawn interaction, check color
                                if (targetPiece && targetPiece.endsWith('pawn')) {
                                    const targetIsWhite = targetPiece.includes('white');
                                    if (isWhitePiece === targetIsWhite) {
                                        return false;  // Can't capture same-color pawns
                                    }
                                }
                                return true;  // Allow any adjacent move or capture
                            }
                            return false;  // Move is too far
                        }
                        // Normal pawn movement rules
                        else {
                            // Check for horizontal captures of noble pieces
                            if (deltaRow === 0 && Math.abs(deltaCol) === 1) {
                                if (targetPiece && !targetPiece.endsWith('pawn')) {
                                    // Allow capture if the target is a noble piece of opposite color
                                    const targetIsWhite = targetPiece.includes('white');
                                    if (isWhitePiece !== targetIsWhite) {
                                        // This capture will radicalize the pawn (handled in executeMove)
                                        return true;
                                    }
                                }
                                return false;
                            }

                            // Check initial position first
                            if ((isWhitePiece && fromRow === 6) || (!isWhitePiece && fromRow === 1)) {
                                // Two-square forward move
                                if (Math.abs(deltaRow) === 2 && deltaCol === 0) {
                                    if (Math.sign(deltaRow) !== forwardDirection) {
                                        return false;
                                    }

                                    if (!isPathClear(fromRow, fromCol, toRow, toCol)) {
                                        return false;
                                    }

                                    if (targetPiece && targetPiece.endsWith('pawn')) {
                                        const targetIsWhite = targetPiece.includes('white');
                                        if (isWhitePiece === targetIsWhite) {
                                            return false;
                                        }
                                    }

                                    return !targetPiece || targetPiece.endsWith('pawn');
                                }
                            }

                            // One-square moves (from any position)
                            if (Math.abs(deltaRow) === 1) {
                                if (Math.sign(deltaRow) !== forwardDirection) {
                                    return false;
                                }

                                // Forward movement
                                if (deltaCol === 0) {
                                    if (targetPiece && targetPiece.endsWith('pawn')) {
                                        const targetIsWhite = targetPiece.includes('white');
                                        if (isWhitePiece === targetIsWhite) {
                                            return false;
                                        }
                                    }
                                    return !targetPiece || targetPiece.endsWith('pawn');
                                }

                                // Forward-diagonal captures only
                                if (Math.abs(deltaCol) === 1) {
                                    return targetPiece && !targetPiece.endsWith('pawn');
                                }
                            }
                            return false;
                        }
                    } else {
                        // Non-pawn piece logic remains unchanged
                        if (targetPiece) {
                            if (!targetPiece.endsWith('pawn')) {
                                return false;  // Prevent noble from capturing another noble
                            }

                            if (targetPiece.endsWith('pawn')) {
                                const isTargetWhite = targetPiece.includes('white');
                                const pawnStartRow = isTargetWhite ? 6 : 1;

                                // Only protect NORMAL pawns on their home row
                                if (toRow === pawnStartRow) {
                                    const pawnState = pawnStates.get(`${toRow},${toCol}`);
                                    if (pawnState === PAWN_STATES.NORMAL) {  // Add this check
                                        const isAttackerWhite = piece.includes('white');
                                        if (isTargetWhite === isAttackerWhite) {
                                            return false;
                                        }
                                    }
                                }

                            }
                        }

                        let isValidPattern = true;
                        switch (piece.split('-')[1]) {
                            case 'queen':
                                if (deltaRow !== 0 && deltaCol !== 0 && Math.abs(deltaRow) !== Math.abs(deltaCol)) isValidPattern = false;
                                break;
                            case 'rook':
                                if (deltaRow !== 0 && deltaCol !== 0) isValidPattern = false;
                                break;
                            case 'bishop':
                                if (Math.abs(deltaRow) !== Math.abs(deltaCol)) isValidPattern = false;
                                break;
                            case 'knight':
                                if (!((Math.abs(deltaRow) === 2 && Math.abs(deltaCol) === 1) ||
                                    (Math.abs(deltaRow) === 1 && Math.abs(deltaCol) === 2))) isValidPattern = false;
                                break;
                            case 'king':
                                if (Math.abs(deltaRow) > 1 || Math.abs(deltaCol) > 1) isValidPattern = false;
                                break;
                        }

                        if (!isValidPattern) return false;

                        if (piece.split('-')[1] !== 'knight') {
                            if (!isPathClear(fromRow, fromCol, toRow, toCol)) return false;
                        }

                        return true;
                    }
                    //clientLogToFile('Move validity result:', true);
                    return true;
                }

                // Add this function to handle move execution
                function executeMove(fromRow, fromCol, toRow, toCol) {

                    console.log('Default.aspx - executeMove()');

                    isProcessingLocalMove = true;
                    const piece = board[fromRow][fromCol];
                    const targetPiece = board[toRow][toCol];
                    const isWhitePiece = piece.includes('white');

                    console.log('targetPiece:', targetPiece);

                    if (targetPiece) {
                        // Reset counter when a capture occurs
                        movesWithoutCapture = 0;
                    } else {
                        // Increment counter for moves without capture
                        movesWithoutCapture = movesWithoutCapture + 1;
                    }

                    console.log('Default.aspx - executeMove() targetPiece 1: ', targetPiece);

                    clientLogToFile('Move Analysis', {
                        movingPiece: piece,
                        targetPiece: targetPiece,
                        from: `${fromRow},${fromCol}`,
                        to: `${toRow},${toCol}`
                    });

                    // First determine the type of interaction
                    if (piece?.endsWith('pawn') && targetPiece?.endsWith('pawn')) {

                        // PAWN SWAP - no captures, just position and state updates

                        clientLogToFile('Executing Pawn Swap');

                        const movingPawnState = pawnStates.get(`${fromRow},${fromCol}`);
                        const targetPawnState = pawnStates.get(`${toRow},${toCol}`);
                        let newTargetPawnState = targetPawnState;

                        // RADICALIZATION RULES
                        if ((movingPawnState === PAWN_STATES.SUPER || movingPawnState === PAWN_STATES.RADICAL) &&
                            targetPawnState === PAWN_STATES.NORMAL) {
                            newTargetPawnState = PAWN_STATES.RADICAL;
                            clientLogToFile('Pawn Radicalized by Super/Radical');
                        }

                        // HOME ROW RADICALIZATION
                        if ((targetPiece.includes('white') && fromRow === 6) ||
                            (targetPiece.includes('black') && fromRow === 1)) {
                            newTargetPawnState = PAWN_STATES.RADICAL;
                            clientLogToFile('Pawn Radicalized from Home Row');
                        }

                        // Swap positions
                        board[toRow][toCol] = piece;
                        board[fromRow][fromCol] = targetPiece;

                        // Update states
                        pawnStates.set(`${toRow},${toCol}`, movingPawnState);
                        pawnStates.set(`${fromRow},${fromCol}`, newTargetPawnState);

                        // Handle super pawn location update if needed
                        if (promotedPawns.has(`${fromRow},${fromCol}`)) {
                            promotedPawns.delete(`${fromRow},${fromCol}`);
                            promotedPawns.add(`${toRow},${toCol}`);
                            clientLogToFile('Super Pawn Location Updated');
                        }

                    } else if (targetPiece) {

                        // CAPTURE - either pawn takes aristoi or aristoi takes pawn

                        clientLogToFile('Executing Capture', {
                            capturer: piece,
                            captured: targetPiece
                        });

                        // Handle super pawn tracking for captured piece
                        if (promotedPawns.has(`${toRow},${toCol}`)) {
                            promotedPawns.delete(`${toRow},${toCol}`);
                            clientLogToFile('Super Pawn Status Removed from Captured Piece');
                        }

                        // Add to appropriate prison
                        if (targetPiece.endsWith('pawn')) {
                            // Pawn captured by aristoi
                            if (!piece.endsWith('pawn')) {
                                capturedPlebs.push(targetPiece);
                                clientLogToFile('Pawn Captured by Aristoi');
                            }
                        } else {
                            // Aristoi captured by pawn
                            capturedAristoi.push(targetPiece);
                            clientLogToFile('Aristoi Captured and Imprisoned');
                        }

                        // Move the piece
                        board[toRow][toCol] = piece;
                        board[fromRow][fromCol] = null;

                        // Handle pawn state updates for the moving piece
                        if (piece?.endsWith('pawn')) {
                            const currentState = pawnStates.get(`${fromRow},${fromCol}`);
                            const isHomeRow = (isWhitePiece && toRow === 0) || (!isWhitePiece && toRow === 7);

                            clientLogToFile('Pawn State Check', {
                                currentState: currentState,
                                isHomeRow: isHomeRow,
                                promotedPawnsCount: promotedPawns.size,
                                targetPieceType: targetPiece,
                                isQueenOrKing: targetPiece && (targetPiece.endsWith('king') || targetPiece.endsWith('queen'))
                            });

                            // Determine new state
                            let newState;
                            if (targetPiece && (targetPiece.endsWith('king') || targetPiece.endsWith('queen')) &&
                                promotedPawns.size < MAX_SUPER_PAWNS) {
                                // Promote to super pawn immediately when capturing king or queen, regardless of current state
                                newState = PAWN_STATES.SUPER;
                                promotedPawns.add(`${toRow},${toCol}`);
                                clientLogToFile('Pawn promoted to SUPER for capturing ' + targetPiece);
                            } else if (currentState === PAWN_STATES.SUPER) {
                                newState = PAWN_STATES.SUPER;
                            } else if (currentState === PAWN_STATES.RADICAL) {
                                if (isHomeRow && promotedPawns.size < MAX_SUPER_PAWNS) {
                                    newState = PAWN_STATES.SUPER;
                                    promotedPawns.add(`${toRow},${toCol}`);
                                    clientLogToFile('Radical pawn promoted to SUPER for reaching home row');
                                } else {
                                    newState = PAWN_STATES.RADICAL;
                                }
                            } else {
                                newState = PAWN_STATES.RADICAL;
                            }

                            // Add debug logging
                            clientLogToFile('Pawn State Update', {
                                from: `${fromRow},${fromCol}`,
                                to: `${toRow},${toCol}`,
                                previousState: currentState,
                                newState: newState,
                                reason: targetPiece ? 'capture' : 'move'
                            });

                            // Update pawn state
                            pawnStates.delete(`${fromRow},${fromCol}`);
                            pawnStates.set(`${toRow},${toCol}`, newState);

                            // Update super pawn tracking
                            if (promotedPawns.has(`${fromRow},${fromCol}`)) {
                                promotedPawns.delete(`${fromRow},${fromCol}`);
                                promotedPawns.add(`${toRow},${toCol}`);
                            }
                        }
                    } else {

                        // SIMPLE MOVE - NO CAPTURE

                        clientLogToFile('Executing Simple Move');
                        board[toRow][toCol] = piece;
                        board[fromRow][fromCol] = null;

                        // Handle pawn state updates for the moving piece
                        if (piece?.endsWith('pawn')) {
                            const currentState = pawnStates.get(`${fromRow},${fromCol}`);
                            const isHomeRow = (isWhitePiece && toRow === 0) || (!isWhitePiece && toRow === 7);

                            let newState;
                            if (currentState === PAWN_STATES.SUPER) {
                                newState = PAWN_STATES.SUPER;
                            } else if (isHomeRow && promotedPawns.size < MAX_SUPER_PAWNS) {
                                // Promote to super pawn when reaching opposite side
                                newState = PAWN_STATES.SUPER;
                                promotedPawns.add(`${toRow},${toCol}`);
                                clientLogToFile('Pawn Promoted to Super Pawn');
                            } else {
                                // First move or regular move - become/stay radical
                                newState = PAWN_STATES.RADICAL;
                            }

                            pawnStates.delete(`${fromRow},${fromCol}`);
                            pawnStates.set(`${toRow},${toCol}`, newState);

                            if (promotedPawns.has(`${fromRow},${fromCol}`)) {
                                promotedPawns.delete(`${fromRow},${fromCol}`);
                                promotedPawns.add(`${toRow},${toCol}`);
                            }
                        }
                    }

                    // UPDATE UI
                    console.log('Default.aspx - executeMove() - targetPiece 2:', targetPiece);

                    // Then update the board and other UI elements
                    renderBoard();

                    // console.log('Default.aspx - updatePrisons()')
                    updatePrisons();

                    console.log('Default.aspx - checkVictory()')

                    // Check for victory condition
                    const winner = checkVictory();
                    if (winner) {
                        showVictoryMessage(winner);
                    }

                    console.log('Default.aspx - CRITICAL LINES FOR TURN SWITCHING');
                    // CRITICAL LINES FOR TURN SWITCHING
                    // isPlebsTurn = !isPlebsTurn;
                    // firstPlayerTurn = !firstPlayerTurn;


                    // Get the destination square element AFTER board render
                    const destSquare = document.querySelector(`.square[data-row="${toRow}"][data-col="${toCol}"]`);

                    // Remove any existing animation classes
                    // destSquare.classList.remove('move-highlight', 'capture-highlight');

                    // Force a reflow to ensure animation plays again
                    //void destSquare.offsetWidth;

                    console.log('Default.aspx - executeMove() - before playMoveEffects()')
                    // Add appropriate highlight class and play sound

                    const moveType = determineMoveType(piece, targetPiece)

                    playMoveEffects(moveType, destSquare);
                    if (!isSoloPlay) {
                        // ONLY PLAY SOUNDS FOR MULTIPLAYER GAME
                        playChessSound(moveType);
                    }

                    // Log final state for debugging
                    clientLogToFile('Move execution complete', {
                        finalPosition: `${toRow},${toCol}`,
                        promotedPawns: Array.from(promotedPawns),
                        pawnStates: Array.from(pawnStates.entries())
                    });

                    // Add this section for multiplayer move broadcasting
                    if (!isSoloPlay) {
                        const moveData = {
                            moveType: moveType,
                            fromRow: fromRow,
                            fromCol: fromCol,
                            toRow: toRow,
                            toCol: toCol,
                            board: board,
                            promotedPawns: Array.from(promotedPawns),
                            capturedPlebs: capturedPlebs,
                            capturedAristoi: capturedAristoi,
                            currentSuperPawn: currentSuperPawn,
                            pawnStates: Array.from(pawnStates.entries()),
                            nextState: {
                                isPlebsTurn: !isPlebsTurn,
                                firstPlayerTurn: !firstPlayerTurn
                            }
                        };
                        clientLogToFile('moveData: ', { moveData: moveData });

                        gameHub.server.makeMove(currentGameId, moveData, moveType)
                            .done(function () {
                                clientLogToFile('Move broadcast successful');
                            })
                            .fail(function (error) {
                                clientLogToFile('Failed to broadcast move', { error: error, moveData: moveData });
                            })
                            .always(function () {
                                isProcessingLocalMove = false;
                            });
                    } else {
                        isProcessingLocalMove = false;
                    }

                    return true;
                }


                function isPathClear(fromRow, fromCol, toRow, toCol) {
                    const deltaRow = Math.sign(toRow - fromRow);
                    const deltaCol = Math.sign(toCol - fromCol);
                    let row = fromRow + deltaRow;
                    let col = fromCol + deltaCol;

                    //clientLogToFile('Checking path:', JSON.stringify({
                    //    from: [fromRow, fromCol],
                    //    to: [toRow, toCol],
                    //    delta: [deltaRow, deltaCol],
                    //    current: [row, col]
                    //}, null, 2));

                    while (row >= 0 && row < 8 && col >= 0 && col < 8 && (row !== toRow || col !== toCol)) {
                        if (board[row][col]) {
                            //clientLogToFile('Path blocked at:', [row, col]);
                            return false;
                        }
                        row += deltaRow;
                        col += deltaCol;
                    }
                    return true;
                }

                function updatePrisons() {
                    //clientLogToFile('Updating prisons:', JSON.stringify({
                    //    capturedPlebs: capturedPlebs,
                    //    capturedAristoi: capturedAristoi
                    //}, null, 2));

                    const plebsPrison = document.getElementById('plebs-prison');
                    const aristoiPrison = document.getElementById('aristoi-prison');

                    if (plebsPrison && aristoiPrison) {
                        // Display captured pieces using their Unicode symbols
                        plebsPrison.innerHTML = capturedPlebs.map(piece =>
                            `<div class="captured-piece">${pieceSymbols[piece]}</div>`
                        ).join('');

                        aristoiPrison.innerHTML = capturedAristoi.map(piece =>
                            `<div class="captured-piece">${pieceSymbols[piece]}</div>`
                        ).join('');

                        clientLogToFile('Prison displays updated');
                    } else {
                        clientLogToFile('Prison elements not found');
                    }
                }

                function checkVictory() {

                    console.log('Default.aspx - checkVictory called');
                    console.log('Default.aspx - userid: ' + userId);
                    console.log('Default.aspx - checkVictory - movesWithoutCapture: ' + movesWithoutCapture + ', DRAW_MOVE_THRESHOLD: ' + DRAW_MOVE_THRESHOLD);

                    // Check for draw condition first
                    if (movesWithoutCapture >= DRAW_MOVE_THRESHOLD) {
                        const drawResult = "Draw!";

                        // Notify server of draw through SignalR
                        gameHub.server.announceVictory(currentGameId, drawResult, userId)
                            .done(function () {
                                clientLogToFile('Draw announced successfully', {
                                    gameId: currentGameId,
                                    result: drawResult
                                });
                            })
                            .fail(function (error) {
                                clientLogToFile('Failed to announce draw', {
                                    gameId: currentGameId,
                                    result: drawResult,
                                    error: error
                                });
                            });

                        return drawResult;
                    }

                    //let remainingNobles = false;
                    //let remainingPawns = false;

                    let remainingNobles = 0;
                    let remainingKingsQueens = 0;
                    let totalNobles = 0;
                    let remainingPawns = false;


                    // Check board state
                    for (let i = 0; i < 8; i++) {
                        for (let j = 0; j < 8; j++) {
                            const piece = board[i][j];
                            if (piece) {
                                if (piece.endsWith('pawn')) {
                                    remainingPawns = true;
                                } else {
                                    remainingNobles++;
                                    totalNobles++;
                                    if (piece.endsWith('king') || piece.endsWith('queen')) {
                                        remainingKingsQueens++;
                                    }
                                }
                            }
                        }
                    }

                    // Add captured nobles to total count
                    totalNobles += capturedAristoi.length;

                    // Determine winner based on victory mode
                    let winner = null;
                    let plebWinType = null;

                    switch (plebVictoryMode) {
                        case 'half':
                            if (capturedAristoi.length > totalNobles / 2) {
                                winner = "Plebs";
                                plebWinType = " (Majority)";
                            }
                            break;
                        case 'royalty':
                            if (remainingKingsQueens === 0) {
                                winner = "Plebs";
                                plebWinType = " (Royals)";
                            }
                            break;
                        default: // 'all'
                            if (remainingNobles === 0) {
                                winner = "Plebs";
                                plebWinType = " (All)";
                            }
                    }

                    // Check Aristoi victory condition (unchanged)
                    if (!remainingPawns) {
                        winner = "Aristoi";
                        plebWinType = "";
                    }


                    if (winner) {

                        // Notify server of victory through SignalR

                        gameHub.server.announceVictory(currentGameId, winner + " Victory!" + plebWinType, userId)
                            .done(function () {
                                clientLogToFile('Victory announced successfully', {
                                    gameId: currentGameId,
                                    winner: winner
                                });
                            })
                            .fail(function (error) {
                                clientLogToFile('Failed to announce victory', {
                                    gameId: currentGameId,
                                    winner: winner,
                                    error: error
                                });
                            });
                    }

                    return winner;

                }

                function determineWinningPlayerId(winningTeam) {
                    // If solo play, return current user's ID
                    if (isSoloPlay) {
                        return userId;
                    }

                    // For multiplayer games:
                    const isPlebsWinner = winningTeam === 'Plebs';
                    if (firstPlayerIsPlebs) {
                        // First player chose Plebs
                        return isPlebsWinner ? firstPlayerUserId : secondPlayerUserId;
                    } else {
                        // First player chose Aristoi
                        return isPlebsWinner ? secondPlayerUserId : firstPlayerUserId;
                    }
                }

                function showVictoryMessage(winner) {
                    // remove any existing victory message
                    const existingMessage = document.querySelector('.victory-message');
                    if (existingMessage) {
                        existingMessage.remove();
                    }

                    const messageDiv = document.createElement('div');
                    messageDiv.className = 'victory-message';

                    // Check if this is a draw
                    const isDraw = winner.startsWith('Draw');
                    const isResignation = winner.includes("Resigned");
                    const team = winner.replace(" Resigned", "");                                         // Remove "Resigned" if present
                    const victoryTeam = isResignation ? (team === "Plebs" ? "Aristoi" : "Plebs") : team;

                    console.log('isDraw        :' + isDraw)
                    console.log('isResignation :' + isResignation)
                    console.log('team          :' + team)
                    console.log('victoryTeam   :' + victoryTeam)

                    messageDiv.innerHTML = `
                        <h2>${isDraw ? 'Game Drawn!' : (isResignation ? `${team} Resigned!` : `${victoryTeam} Victory!`)}</h2>
                        ${isDraw ?
                            '<p>Neither side could achieve victory - both sides must sue for peace!</p>' :
                            `${isResignation ?
                                `<p>${team === 'Plebs' ?
                                    'How dreadfully terrible for the rabble! Better luck next time, peasant!' :
                                    'How dreadfully terrible for the nobility! Better luck next time, your former grace!'}</p>`
                                : ''
                            }
                            <p>${victoryTeam === 'Plebs' ?
                                'The Plebians have overthrown the tyrannical nobility! Hooray!' :
                                'The Aristoi have saved civilization and restored law and order!'}</p>`
                        }
                        <button onclick="resetGameToInitialState(); this.parentElement.remove();">New Game</button>
                    `;
                    document.body.appendChild(messageDiv);

                    // Update UI elements
                    const joinGameSection = document.getElementById('joinGameSection');
                    if (joinGameSection) {
                        joinGameSection.style.display = 'block';
                    }

                    const turnLight = document.querySelector('.turn-light');
                    if (turnLight) {
                        turnLight.className = 'turn-light waiting';
                    }

                    document.getElementById('turn-message').textContent = 'Waiting for game to start...';

                }

                function createNewGame() {
                    clientLogToFile("createNewGame called");
                    event.preventDefault();

                    const gameId = generateGameId();
                    currentGameId = gameId;
                    const isSoloPlay = document.getElementById('soloPlayCheck').checked;

                    // Get the selected victory mode
                    plebVictoryMode = document.getElementById('plebVictoryMode').value;

                    // Get the selected team immediately
                    isFirstPlayer = true;
                    const plebsFirst = document.querySelector('input[name="first-move"]:checked').value === 'plebs';
                    window.firstPlayerTurnValue = plebsFirst;

                    // Store the game ID in the session first
                    return new Promise((resolve, reject) => {
                        $.ajax({
                            url: 'Default.aspx/StoreGameId',
                            type: 'POST',
                            data: JSON.stringify({ gameId: gameId }),
                            contentType: 'application/json',
                            success: resolve,
                            error: reject
                        });
                    })
                        .then(() => {
                            // First, register the user connection so the userId is available
                            // We'll do this by calling joinGame first, which sets up the connection
                            return gameHub.server.joinGame(gameId, userId, isSoloPlay);
                        })
                        .then(success => {
                            if (success) {
                                // Now update first player selection after the connection is established
                                return gameHub.server.updateFirstPlayerSelection(gameId, plebsFirst);
                            } else {
                                throw new Error('Failed to join game');
                            }
                        })
                        .then(() => {
                            // updateFirstPlayerSelection doesn't return a value, so we continue
                            if (true) {
                                if (!isSoloPlay) {
                                    const waitingMessage = document.getElementById('waitingMessage');
                                    if (waitingMessage) {
                                        waitingMessage.style.display = 'block';
                                        waitingMessage.textContent = 'Waiting for opponent to join...';
                                    }
                                }

                                document.getElementById('currentGameId').textContent = gameId;

                                if (isSoloPlay) {
                                    startNewGame();
                                    // Add role display update for solo play
                                    const playerRole = document.getElementById('playerRole');
                                    if (playerRole) {
                                        playerRole.textContent = plebsFirst ? 'Plebs (Solo Play)' : 'Aristoi (Solo Play)';
                                    }
                                }

                                // Update UI elements
                                document.getElementById('joinGameSection').style.display = 'none';
                                gameStartTime = new Date();
                                updateGameStartUI();
                            } else {
                                console.error('Default.aspx - Failed to join game');
                                resetMultiplayerUI();
                                return Promise.reject('Failed to join game');
                            }
                        })
                        .catch(error => {
                            console.error('Default.aspx - Error creating game:', error);
                            document.getElementById('currentGameId').textContent = 'Error creating game';
                            if (waitingMessage) {
                                waitingMessage.style.display = 'none';
                            }
                        });

                    return false; // Prevent form submission
                }

                // Helper function to update UI after game start
                function updateGameStartUI() {
                    const startButton = document.getElementById('startNewGameBtn');
                    startButton.textContent = 'Resign';
                    startButton.className = 'resign-btn';
                    startButton.onclick = function (e) {
                        e.preventDefault();
                        handleResign();
                    };

                    const focusModeBtn = document.getElementById('btnFocusModeToggle');
                    if (focusModeBtn) {
                        focusModeBtn.style.display = 'block';
                    }

                    const soloPlayCheck = document.getElementById('soloPlayCheck');
                    if (soloPlayCheck) {
                        soloPlayCheck.disabled = true;
                    }
                }

                function handleResign() {

                    const isConfirmed = confirm("Are you sure you want to resign?");
                    if (!isConfirmed) return;

                    const resigningTeam = isPlebsTurn ? 'Plebs' : 'Aristoi';
                    const endGameAction = resigningTeam + " Resigned";

                    // Log the initial resign attempt
                    // clientLogToFile('Resign initiated:', {
                    //     resigningTeam: resigningTeam,
                    //     isSoloPlay: isSoloPlay,
                    //     isFirstPlayer: isFirstPlayer
                    // });

                    // For solo play, we only need the current userId
                    const endGameUserId = userId;

                    // Always announce victory to server, regardless of game mode
                    gameHub.server.announceVictory(currentGameId, endGameAction, userId)
                        .done(function () {
                            clientLogToFile('Resignation logged successfully', {
                                gameId: currentGameId,
                                action: endGameAction,
                                userId: userId
                            });
                        })
                        .fail(function (error) {
                            clientLogToFile('Failed to log resignation', {
                                error: error,
                                gameId: currentGameId,
                                action: endGameAction
                            });
                        })
                        .always(function () {
                            // Show victory message regardless of success/fail of logging
                            showVictoryMessage(endGameAction);
                        });
                }

                function resetGameToInitialState() {
                    // Reset game state
                    currentGameId = null;
                    selectedPiece = null;
                    isPlebsTurn = true;
                    promotedPawns.clear();
                    capturedPlebs = [];
                    capturedAristoi = [];
                    movesWithoutCapture = 0;  // Reset the counter for moves without capture for Draw condition

                    document.body.classList.remove('focus-mode');

                    // Reset focus mode button text
                    const focusModeButton = document.querySelector('.focus-mode-btn span');
                    if (focusModeButton) {
                        focusModeButton.textContent = 'Focus Mode';
                    }

                    // Reset chat interface if it exists
                    const chatInterface = document.getElementById('chat-interface');
                    if (chatInterface) {
                        chatInterface.className = 'chat-interface hidden';
                    }

                    // Reset the button
                    const button = document.getElementById('startNewGameBtn');
                    button.textContent = 'Start New Game';
                    button.className = '';
                    button.onclick = function (e) {
                        e.preventDefault();
                        createNewGame();
                    };

                    // Show the team selection options again
                    const radioOptions = document.querySelector('.radio-options');
                    if (radioOptions) {
                        radioOptions.style.display = 'flex';
                    }

                    // Show join game section
                    const joinGameSection = document.getElementById('joinGameSection');
                    if (joinGameSection) {
                        joinGameSection.style.display = 'block';
                    }

                    // Reset game ID display
                    document.getElementById('currentGameId').textContent = 'Not started';

                    // disable page transfer buttons
                    updateMenuButtonStates(false);  // keep this line

                    // Reset the board
                    initializeBoard();

                    // Reset turn status
                    document.getElementById('turn').textContent = 'Plebs (Pawns)';
                    console.log('Default.aspx - Inside resetGameToInitialState() - Reset turn status - call updateTurnStatus()')
                    updateTurnStatus();

                    // Re-enable solo play checkbox
                    const soloPlayCheck = document.getElementById('soloPlayCheck');
                    if (soloPlayCheck) {
                        soloPlayCheck.disabled = false;
                    }

                    // Reset turn light to waiting state
                    const turnLight = document.querySelector('.turn-light');
                    if (turnLight) {
                        turnLight.className = 'turn-light waiting';
                    }
                    document.getElementById('turn-message').textContent = 'Waiting for game to start...';

                    // Reset victory mode to default
                    const victoryModeSelect = document.getElementById('plebVictoryMode');
                    if (victoryModeSelect) {
                        victoryModeSelect.value = 'all';
                        updateGameRules();
                    }
                }

                // MODIFY THIS SECTION
                function startNewGame() {
                    // Get the victory mode first
                    const victoryMode = document.getElementById('plebVictoryMode').value;

                    // Clear any end-game state
                    const existingMessage = document.querySelector('.victory-message');
                    if (existingMessage) {
                        existingMessage.remove();
                    }

                    // Reset game state variables
                    resetGameState();

                    // Initialize the board
                    initializeBoard();

                    // disable page transfer
                    updateMenuButtonStates(true);  // keep this line

                    // Start new game with victory mode
                    gameHub.server.startNewGame(currentGameId)
                        .done(function () {
                            // Set victory mode after game starts
                            gameHub.server.setVictoryMode(currentGameId, victoryMode)
                                .then(() => {
                                    // For multiplayer games, keep waiting message until Player 2 joins
                                    if (!isSoloPlay) {
                                        document.getElementById('turn').textContent = 'Game not started';
                                        document.getElementById('turn-message').textContent = 'Waiting for game to start...';
                                    } else {
                                        // Solo play can proceed normally
                                        setInitialTurn();
                                        console.log('Default.aspx - Inside startNewGame() - SOLO PLAY can proceed normally - call updateTurnStatus()')
                                        updateTurnStatus();
                                    }

                                    // If in multiplayer, notify other players
                                    console.log('Default.aspx - Inside startNewGame() - If in multiplayer, notify other players - call notifyMultiplayerGameStart()')
                                    notifyMultiplayerGameStart();

                                    clientLogToFile('Game started with victory mode', {
                                        gameId: currentGameId,
                                        victoryMode: victoryMode
                                    });
                                });
                        })
                        .fail(function (error) {
                            clientLogToFile('Failed to start game', {
                                error: error,
                                gameId: currentGameId
                            });
                        });
                }

                function setInitialTurn() {
                    // Only set turns for solo play or when Player 2 has joined
                    console.log('Default.aspx - Inside setInitialTurn() - Only set turns for solo play or when Player 2 has joined')

                    if (isSoloPlay) {
                        console.log('Default.aspx - Inside setInitialTurn() - isSoloPlay = true')
                        isPlebsTurn = document.querySelector('input[name="first-move"]:checked').value === 'plebs';
                        document.getElementById('turn').textContent = isPlebsTurn ? 'Plebs (Pawns)' : 'Aristoi (Noble Pieces)';
                    }
                }

                // Helper functions for cleaner separation
                function resetGameState() {
                    selectedPiece = null;
                    currentSuperPawn = null;
                    promotedPawns.clear();
                    capturedPlebs = [];
                    capturedAristoi = [];
                }

                function resetMultiplayerUI() {
                    document.getElementById('currentGameId').textContent = 'Error creating game';
                    document.getElementById('waitingMessage').style.display = 'none';
                    document.getElementById('playerRole').textContent = 'Error setting up multiplayer';
                }

                function notifyMultiplayerGameStart() {
                    if (currentGameId) {
                        gameHub.server.startNewGame(currentGameId)
                            .done(() => clientLogToFile('New game state broadcasted'))
                            .fail(error => console.error('Default.aspx - Failed to broadcast new game:', error));
                    }
                }

                function generateGameId() {
                    return Math.random().toString(36).substring(2, 8).toUpperCase();
                }


                // Update the SignalR initialization code
                $(function () {
                    clientLogToFile('Initializing SignalR...');

                    console.log('Default.asxp -  Update the SignalR initialization code - $(function () - Initializing SignalR...')

                    // Initialize gameHub first
                    gameHub = $.connection.gameHub;
                    clientLogToFile('gameHub initialized');

                    // Add chat message handler before starting the connection
                    // Update the SignalR chat message handler
                    clientLogToFile('Setting up chat message handler');
                    gameHub.client.receiveChatMessage = function (senderId, message) {
                        // clientLogToFile('CHAT: Received message:', { senderId, message });

                        const chatMessages = document.getElementById('chat-messages');
                        if (!chatMessages) {
                            console.error('Default.aspx - Chat messages container not found');
                            return;
                        }

                        const messageDiv = document.createElement('div');
                        const isOwnMessage = senderId === $.connection.hub.id;

                        messageDiv.className = `chat-message ${isOwnMessage ? 'sent' : 'received'}`;
                        messageDiv.textContent = message;

                        chatMessages.appendChild(messageDiv);
                        chatMessages.scrollTop = chatMessages.scrollHeight;
                    };


                    // Make sure this handler is defined before the connection starts
                    clientLogToFile('Setting up player joined handler');

                    gameHub.client.playerJoined = function (gameState) {
                        // clientLogToFile('playerJoined handler', {
                        //     receivedState: gameState,
                        //     isFirstPlayer: isFirstPlayer,
                        //     currentGameId: currentGameId,
                        //     existingTurnState: {
                        //         isPlebsTurn: isPlebsTurn,
                        //         firstPlayerTurn: firstPlayerTurn
                        //     }
                        // });

                        // Store server's record of Player 1's choice globally
                        // clientLogToFile('Received gameState properties', gameState);
                        console.log('Default.asxp  - $(function () - gameHub.client.playerJoined -  isPlebsTurn = gameState.IsPlebsTurn; firstPlayerTurn = true;')

                        window.firstPlayerTurnValue = gameState.FirstPlayerIsPlebs;
                        isPlebsTurn = gameState.IsPlebsTurn;
                        firstPlayerTurn = true;  // Always start with Player 1's turn

                        // Debug state transition
                        // clientLogToFile('State transition in playerJoined:', JSON.stringify({
                        //     before: {
                        //         isPlebsTurn: isPlebsTurn,
                        //         firstPlayerTurn: firstPlayerTurn
                        //     },
                        //     action: 'Setting state from server: ' + JSON.stringify(gameState),
                        //     after: {
                        //         isPlebsTurn: isPlebsTurn,
                        //         firstPlayerTurn: firstPlayerTurn
                        //     }
                        // }, null, 2));


                        // clientLogToFile('After setting global firstPlayerTurnValue:', JSON.stringify({
                        //     windowFirstPlayerTurnValue: window.firstPlayerTurnValue,
                        //     isPlebsTurn: isPlebsTurn,
                        //     firstPlayerTurn: firstPlayerTurn,
                        //     stateSource: isFirstPlayer ? 'Player 1 Initial' : 'Player 2 Joining'
                        // }, null, 2));

                        const playerRoleElement = document.getElementById('playerRole');
                        const waitingMessage = document.getElementById('waitingMessage');
                        const soloPlayCheck = document.getElementById('soloPlayCheck'); // Add this line

                        if (playerRoleElement) {
                            // Set role based on player number and first player's choice
                            const role = isFirstPlayer ?
                                (window.firstPlayerTurnValue ? 'Plebs (First Player)' : 'Aristoi (First Player)') :
                                (window.firstPlayerTurnValue ? 'Aristoi (Second Player)' : 'Plebs (Second Player)');
                            playerRoleElement.textContent = role;
                        }

                        // Disable solo play checkbox when player 2 joins
                        if (soloPlayCheck) {
                            soloPlayCheck.disabled = true;
                            soloPlayCheck.checked = false; // Ensure it's unchecked
                        }

                        // Update UI elements
                        if (waitingMessage) {
                            waitingMessage.textContent = 'Player has joined the game!';
                            setTimeout(() => waitingMessage.style.display = 'none', 3000);
                        }

                        // clientLogToFile('Game state after join:', {
                        //     isFirstPlayer,
                        //     firstPlayerTurnValue,
                        //     isPlebsTurn,
                        //     firstPlayerTurn
                        // });

                        console.log('Default.asxp - $(function () - gameHub.client.playerJoined -  updateTurnStatus();')
                        updateTurnStatus();

                        // clientLogToFile('Game initialization complete', {
                        //     isFirstPlayer: isFirstPlayer,
                        //     currentGameId: currentGameId,
                        //     firstPlayerTurnValue: window.firstPlayerTurnValue,
                        //     role: isFirstPlayer ? 'Player 1' : 'Player 2'
                        // });

                        // Change the Start New Game button to Resign for joining player
                        const startButton = document.getElementById('startNewGameBtn');
                        startButton.textContent = 'Resign';
                        startButton.className = 'resign-btn';
                        startButton.onclick = function (e) {
                            e.preventDefault();
                            handleResign();
                        };


                    };

                    // Update the SignalR client handler
                    clientLogToFile('Setting up game state handler');
                    gameHub.client.updateGameState = function (moveData) {

                        // clientLogToFile('SignalR updateGameState handler triggered', {
                        //     isFirstPlayer,
                        //     //moveData,
                        //     currentState: {
                        //         isPlebsTurn,
                        //         firstPlayerTurn
                        //     }
                        // });

                        // Only update board state if not processing local move
                        if (!isProcessingLocalMove) {
                            // Check for pawn swap before any state updates
                            const boardHasSwappedPawns = moveData.board.some((row, rowIndex) =>
                                row.some((piece, colIndex) =>
                                    piece?.endsWith('pawn') &&
                                    board[rowIndex][colIndex]?.endsWith('pawn') &&
                                    piece !== board[rowIndex][colIndex]
                                )
                            );

                            // Add sound playback
                            if (moveData.moveType) {
                                playChessSound(moveData.moveType);
                            }

                            // Store the current board state before updating
                            const oldBoard = JSON.parse(JSON.stringify(board));  // Deep copy of current board

                            // Update board state
                            board = moveData.board;


                            // Find both source and destination by comparing boards
                            let sourceRow, sourceCol, destRow, destCol, targetPiece;
                            for (let row = 0; row < 8; row++) {
                                for (let col = 0; col < 8; col++) {
                                    if (oldBoard[row][col] !== board[row][col]) {
                                        if (oldBoard[row][col] !== '') {  // Found where piece moved from
                                            sourceRow = row;
                                            sourceCol = col;
                                            targetPiece = oldBoard[row][col];
                                        }
                                        if (board[row][col] !== '') {  // Found where piece moved to
                                            destRow = row;
                                            destCol = col;
                                        }
                                    }
                                }
                            }

                            // clientLogToFile('Move detection:', {
                            //     destRow,
                            //     destCol,
                            //     targetPiece,
                            //     oldBoardState: oldBoard[destRow][destCol],
                            //     newBoardState: board[destRow][destCol]
                            // });

                            currentSuperPawn = moveData.currentSuperPawn;
                            promotedPawns.clear();
                            if (moveData.promotedPawns) {
                                moveData.promotedPawns.forEach(pawn => promotedPawns.add(pawn));
                            }

                            // Update captured pieces arrays
                            capturedPlebs = moveData.capturedPlebs || [];
                            capturedAristoi = moveData.capturedAristoi || [];

                            // Update pawn states
                            pawnStates.clear();
                            if (moveData.pawnStates) {
                                // Handle both array format and dictionary format
                                if (Array.isArray(moveData.pawnStates)) {
                                    moveData.pawnStates.forEach(entry => {
                                        const [position, state] = entry;
                                        pawnStates.set(position, state);

                                        // Update visual state
                                        const [row, col] = position.split(',');
                                        const square = document.querySelector(`.square[data-row="${row}"][data-col="${col}"]`);
                                        if (square) {
                                            if (state === PAWN_STATES.RADICAL) {
                                                square.classList.add('radicalized-pawn');
                                            } else if (state === PAWN_STATES.SUPER) {
                                                square.classList.add('promoted-pawn');
                                            }
                                        }
                                    });
                                } else {
                                    Object.entries(moveData.pawnStates).forEach(([position, state]) => {
                                        pawnStates.set(position, state);

                                        // Update visual state
                                        const [row, col] = position.split(',');
                                        const square = document.querySelector(`.square[data-row="${row}"][data-col="${col}"]`);
                                        if (square) {
                                            if (state === PAWN_STATES.RADICAL) {
                                                square.classList.add('radicalized-pawn');
                                            } else if (state === PAWN_STATES.SUPER) {
                                                square.classList.add('promoted-pawn');
                                            }
                                        }
                                    });
                                }
                            }

                            // Update next state from nextState
                            isPlebsTurn = moveData.nextState.isPlebsTurn;
                            firstPlayerTurn = moveData.nextState.firstPlayerTurn;

                            renderBoard();
                            updatePrisons();
                            updateTurnStatus();

                            // Check for victory condition
                            const winner = checkVictory();
                            if (winner) {
                                showVictoryMessage(winner);
                            }

                            // Get the destination square element AFTER board render
                            // const destSquare = document.querySelector(`.square[data-row="${destRow}"][data-col="${destCol}"]`);


                            // Add the same sound logic we use for local moves
                            // playMoveEffects(fromRow, fromCol, toRow, toCol, piece, targetPiece);
                        } else {
                            clientLogToFile('Skipping board update for local move - no sounds played');
                        }

                    };

                    // Add this right after other gameHub.client handlers, before $.connection.hub.start()
                    gameHub.client.victoryAnnounced = function (winner) {
                        showVictoryMessage(winner);
                        clientLogToFile('Victory announced for:', winner);
                    };

                    // Start the SignalR connection
                    $.connection.hub.start()
                        .done(function () {
                            clientLogToFile('Successfully connected to SignalR hub');
                            clientLogToFile('Connection ID:', $.connection.hub.id);
                            $('#startNewGameBtn').prop('disabled', false);
                        })
                        .fail(function (error) {
                            console.error('Default.aspx - Failed to connect to SignalR hub:', error);
                            alert('Failed to connect to game server. Please refresh the page.');
                        });

                    // Get the hub
                    // gameHub = $.connection.gameHub;


                    // Update the sendChatMessage function with more logging
                    window.sendChatMessage = function () {
                        const input = document.getElementById('chat-input');
                        const message = input.value.trim();

                        // clientLogToFile('Attempting to send message:', {
                        //     message,
                        //     currentGameId,
                        //     hubId: $.connection.hub.id,
                        //     hubState: $.connection.hub.state,
                        //     timestamp: new Date().toISOString()
                        // });

                        if (message && currentGameId) {
                            // Send message through SignalR
                            gameHub.server.sendChatMessage(currentGameId, message)
                                .done(() => {
                                    clientLogToFile('Message sent successfully:', {
                                        message,
                                        currentGameId,
                                        timestamp: new Date().toISOString()
                                    });
                                    input.value = '';
                                })
                                .fail(error => {
                                    console.error('Default.aspx - Failed to send message:', {
                                        error,
                                        message,
                                        currentGameId,
                                        hubState: $.connection.hub.state,
                                        timestamp: new Date().toISOString()
                                    });
                                });
                        }
                    };

                    $.connection.hub.error(function (error) {
                        console.error('Default.aspx - SignalR error:', {
                            error,
                            timestamp: new Date().toISOString()
                        });
                    });

                    // Add enter key handler for chat input
                    document.getElementById('chat-input')?.addEventListener('keypress', function (e) {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                            sendChatMessage();
                        }
                    });

                    // Add this to the SignalR client setup section
                    gameHub.client.playerReconnected = function(userId) {
                        console.log('Player reconnected:', userId);
                        clientLogToFile('Player reconnected', { userId: userId });

                        // Update UI to show player reconnected
                        const statusElement = document.getElementById('gameStatus');
                        if (statusElement) {
                            statusElement.textContent = 'Player reconnected';
                            statusElement.className = 'status-reconnected';
                        }
                    };

                    // Add automatic reconnection handling
                    $.connection.hub.disconnected(function() {
                        console.log('Disconnected from game hub');

                        // Show reconnecting message
                        const statusElement = document.getElementById('gameStatus');
                        if (statusElement) {
                            statusElement.textContent = 'Disconnected - attempting to reconnect...';
                            statusElement.className = 'status-disconnected';
                        }

                        // Try to reconnect after 5 seconds
                        setTimeout(function() {
                            $.connection.hub.start()
                                .done(function() {
                                    console.log('Reconnected to game hub');

                                    // If we have a current game, try to rejoin
                                    if (currentGameId) {
                                        gameHub.server.joinGame(currentGameId, userId, isSoloPlay)
                                            .done(function(success) {
                                                console.log('Rejoined game:', success);

                                                // Update UI
                                                if (statusElement) {
                                                    statusElement.textContent = 'Reconnected';
                                                    statusElement.className = 'status-connected';
                                                }
                                            })
                                            .fail(function(error) {
                                                console.error('Failed to rejoin game:', error);
                                            });
                                    }
                                });
                        }, 5000);
                    });

                    // Add helper function to update game from state
                    function updateGameFromState(gameState) {
                        // Update board
                        board = gameState.Board;
                        isPlebsTurn = gameState.IsPlebsTurn;

                        // Update UI
                        renderBoard();
                        updateTurnStatus();

                        clientLogToFile('Game state updated from server', {
                            gameId: currentGameId,
                            isPlebsTurn: isPlebsTurn
                        });
                    }
                });

                // Update the button in HTML to have an ID and be initially disabled
                initializeBoard();

                //====================================================================================================================

                function joinGame() {
                    // clientLogToFile('joinGame function called');

                    // Add a guard against multiple calls
                    if (window.joiningInProgress) {
                        clientLogToFile('Join already in progress, ignoring duplicate call');
                        clientLogToFile("joinGame() - STEP 2 - Join already in progress, ignoring duplicate call")
                        return;
                    }
                    window.joiningInProgress = true;

                    const gameIdToJoin = document.getElementById('gameId').value.trim().toUpperCase();

                    if (!gameIdToJoin) {
                        alert('Please enter a game ID');
                        window.joiningInProgress = false;
                        return;
                    }

                    // clientLogToFile("joinGame() - STEP 3 - before ajax")  // This should appear!

                    // Create a Promise wrapper around the AJAX call
                    new Promise((resolve, reject) => {
                        $.ajax({
                            url: 'Default.aspx/StoreGameId',
                            type: 'POST',
                            data: JSON.stringify({ gameId: gameIdToJoin }),
                            contentType: 'application/json',
                            success: resolve,
                            error: reject
                        });
                    })
                        .then(() => {
                            clientLogToFile('Game ID stored in session:', gameIdToJoin);
                            currentGameId = gameIdToJoin;

                            // Process any pending logs now that we have a gameId
                            return processPendingLogs();
                        })
                        .then(() => {
                            // Now proceed with logging and joining
                            return clientLogToFile('Player 2 join attempt', {
                                gameIdToJoin: gameIdToJoin,
                                userId: userId,
                                hubId: $.connection.hub.id
                            });
                        })
                        .then(() => {
                            hideFirstMoverOptions();
                            return clientLogToFile("joinGame() - STEP 4 - isFirstPlayer = false;");
                        })
                        .then(() => {
                            isFirstPlayer = false;  // This player is Player 2
                            return clientLogToFile("joinGame() - STEP 5 - gameHub.server.joinGame(): gameIdToJoin: " + gameIdToJoin + " userId:" + userId);
                        })
                        .then(() => {
                            return gameHub.server.joinGame(gameIdToJoin, userId, isSoloPlay);
                        })
                        .then((success) => {
                            if (success) {

                                console.log("Default.aspx - joinGame() - STEP 6 - success = true");
                                document.getElementById('currentGameId').textContent = gameIdToJoin;
                                document.getElementById('waitingMessage').style.display = 'none';
                                document.getElementById('joinGameSection').style.display = 'none';

                                initializeBoard();
                                return clientLogToFile('Player 2 joined successfully')
                                    .then(() => {
                                        updateTurnStatus();
                                    });
                            } else {
                                console.log("Default.aspx - joinGame() - STEP 6 - success = false");

                                // Check if the game exists in the database but is full
                                return gameHub.server.checkGameExists(gameIdToJoin)
                                    .then(gameExists => {
                                        if (gameExists) {
                                            document.getElementById('currentGameId').textContent = 'Game is full - cannot join';
                                        } else {
                                            document.getElementById('currentGameId').textContent = 'Game does not exist';
                                        }

                                        // Clear game ID since join failed
                                        return new Promise((resolve, reject) => {
                                            $.ajax({
                                                url: 'Default.aspx/StoreGameId',
                                                type: 'POST',
                                                data: JSON.stringify({ gameId: null }),
                                                contentType: 'application/json',
                                                success: resolve,
                                                error: reject
                                            });
                                        });
                                    });
                            }
                        })
                        .catch((error) => {
                            clientLogToFile('Log error', {
                                error: error,
                                message: error.message
                            });

                            document.getElementById('currentGameId').textContent = 'Error joining game';
                            return clientLogToFile('Player 2 join failed', {
                                error: error,
                                gameId: gameIdToJoin
                            });
                        })
                        .finally(() => {
                            window.joiningInProgress = false;
                        });
                }

                // Add SignalR handler for victory announcement
                $(function () {
                    gameHub.client.victoryAnnounced = function (winner) {
                        showVictoryMessage(winner);
                        clientLogToFile('Victory announced for:', winner);
                    };
                });


                // Add this new function to handle solo play toggle
                function handleSoloPlayToggle(checkbox) {
                    clientLogToFile('handleSoloPlayToggle called');
                    clientLogToFile('checkbox checked:', checkbox.checked);

                    // Set isSoloPlay BEFORE any other operations
                    isSoloPlay = checkbox.checked;

                    const waitingMessage = document.getElementById('waitingMessage');
                    const joinGameSection = document.getElementById('joinGameSection');

                    clientLogToFile('joinGameSection found:', joinGameSection ? 'yes' : 'no');

                    if (checkbox.checked) {
                        // Hide multiplayer elements when solo play is enabled
                        if (waitingMessage) {
                            waitingMessage.style.display = 'none';
                            clientLogToFile('hiding waiting message');
                        }
                        if (joinGameSection) {
                            joinGameSection.style.display = 'none';
                            clientLogToFile('hiding join section');
                        }
                    } else {
                        // Show multiplayer elements when solo play is disabled
                        if (waitingMessage) {
                            waitingMessage.style.display = 'block';
                            clientLogToFile('showing waiting message');
                        }
                        if (joinGameSection) {
                            joinGameSection.style.display = 'block';
                            clientLogToFile('showing join section');
                        }
                    }
                }

                function updateTurnIndicator() {
                    // Handle solo play first
                    if (isSoloPlay) {
                        const turnLight = document.querySelector('.turn-light');
                        if (turnLight) {
                            turnLight.className = 'turn-light active';
                        }
                        return;  // Exit early for solo play
                    }

                    // Multiplayer logic
                    var isMyTurn = (isFirstPlayer && firstPlayerTurn) || (!isFirstPlayer && !firstPlayerTurn);
                    var currentTeam = isPlebsTurn ? 'Plebs' : 'Aristoi';
                    var myTeam = isFirstPlayer ?
                        (window.firstPlayerTurnValue ? 'Plebs' : 'Aristoi') :
                        (window.firstPlayerTurnValue ? 'Aristoi' : 'Plebs');

                    // Update both turn indicators
                    const turnSpan = document.getElementById('turn');
                    const turnMessage = document.getElementById('turn-message');
                    const turnLight = document.querySelector('.turn-light');

                    try {
                        // Update turn span
                        if (turnSpan) {
                            turnSpan.textContent = isPlebsTurn ? 'Plebs (Pawns)' : 'Aristoi (Noble Pieces)';
                        }

                        // Update turn message
                        if (turnMessage) {
                            turnMessage.textContent = isMyTurn ?
                                (myTeam === 'Plebs' ? "It's your turn, Rebel scum!" : "It's your turn, vile Tyrant!") :
                                "Please wait while your opponent makes their move.";
                        }

                        // Update turn light
                        if (turnLight) {
                            turnLight.className = 'turn-light ' + (isMyTurn ? 'active' : 'inactive');
                        }

                        // clientLogToFile('Turn indicators updated', {
                        //     currentTeam,
                        //     isMyTurn,
                        //     myTeam
                        // });
                    } catch (error) {
                        console.error('Default.aspx - Error updating turn indicators:', error);
                        clientLogToFile('Turn indicator update error', {
                            error: error.message,
                            state: {
                                isMyTurn,
                                currentTeam,
                                myTeam
                            }
                        });
                    }
                }


                function updateTurnMessage() {
                    // clientLogToFile('updateTurnMessage - Start', {
                    //     isFirstPlayer,
                    //     firstPlayerTurnValue: window.firstPlayerTurnValue,
                    //     isPlebsTurn,
                    //     firstPlayerTurn,
                    //     elements: {
                    //         turnMessage: document.getElementById('turn-message') ? 'exists' : 'missing',
                    //         turnDisplay: document.getElementById('turn') ? 'exists' : 'missing'
                    //     }
                    // });

                    const turnMessage = document.getElementById('turn-message');
                    const turnDisplay = document.getElementById('turn');


                    if (turnMessage) {
                        /* Variable Purposes:
                         * isFirstPlayer (boolean)
                         * - Indicates if this client is Player 1 or Player 2
                         * - True = Player 1, False = Player 2
                         *
                         * firstPlayerTurnValue (boolean) [poorly named - should be firstPlayerChosePlebs]
                         * - Stores what team Player 1 chose at game start
                         * - True = Player 1 chose Plebs, False = Player 1 chose Aristoi
                         *
                         * isPlebsTurn (boolean)
                         * - Indicates whose turn it currently is
                         * - True = Plebs' turn, False = Aristoi's turn
                         *
                         * firstPlayerTurn (boolean)
                         * - Indicates which player's turn it is
                         * - True = Player 1's turn, False = Player 2's turn
                         *
                         * isPlayerPlebs (boolean)
                         * - Calculated value to determine if current player is on Plebs team
                         * - True = This player is Plebs, False = This player is Aristoi
                         */
                        // Existing multiplayer logic
                        const isPlayerAristoi = isFirstPlayer ? !window.firstPlayerTurnValue : window.firstPlayerTurnValue;

                        // clientLogToFile('updateTurnMessage - Calculating message', {
                        //     isPlayerAristoi,
                        //     isPlayerTurn: isPlayerTurn(),
                        //     currentMessage: turnMessage.textContent
                        // });

                        if (isSoloPlay) {
                            // In solo mode, just show which side's turn it is
                            turnMessage.textContent = isPlebsTurn ?
                                "Plebs' Turn - Make your move!" :
                                "Aristoi's Turn - Make your move!";
                            turnDisplay.textContent = isPlebsTurn ?
                                'Plebs (Pawns)' :
                                'Aristoi (Noble Pieces)';
                            return;
                        }


                        turnMessage.textContent = isPlayerTurn() ?
                            (isPlayerAristoi ? "It's your turn, Tyrant!" : "It's your turn, rebel scum!") :
                            "Please wait while your opponent makes their move.";

                        // Move this inside the else block since it's multiplayer-specific
                        if (turnDisplay) {
                            // clientLogToFile('updateTurnMessage - Turn calculation', {
                            //     firstPlayerTurnValue: window.firstPlayerTurnValue,
                            //     firstPlayerTurn,
                            //     isPlebsTurn,
                            //     isFirstPlayer
                            // });

                            // Determine player's role based on Player 1's choice
                            const isCurrentPlayerPlebs = isFirstPlayer === window.firstPlayerTurnValue;
                            const playerRole = isCurrentPlayerPlebs ? 'Plebs (Pawns)' : 'Aristoi (Noble Pieces)';
                            const whoseTurn = firstPlayerTurn ? 'Player 1' : 'Player 2';

                            // clientLogToFile('updateTurnMessage - Display values', {
                            //     isCurrentPlayerPlebs,
                            //     playerRole,
                            //     whoseTurn,
                            //     currentDisplay: turnDisplay.textContent
                            // });

                            turnDisplay.textContent = `${playerRole} - ${whoseTurn}'s Turn`;
                        }

                    }

                    clientLogToFile('updateTurnMessage - Complete');
                }

                // New function to update both
                function updateTurnStatus() {

                    console.log('Default.aspx - INSIDE LOCAL updateTurnStatus()');
                    // clientLogToFile('Updating turn status', {
                    //     isPlebsTurn,
                    //     isFirstPlayer,
                    //     firstPlayerTurn,
                    //     isSoloPlay
                    // });

                    updateTurnMessage();
                    updateTurnIndicator();

                    selectedPiece = null;
                    clearHighlights();
                }

                function isPlayerTurn() {
                    // clientLogToFile('isPlayerTurn check', {
                    //     isFirstPlayer,
                    //     firstPlayerTurn,
                    //     isPlebsTurn,
                    //     firstPlayerTurnValue: window.firstPlayerTurnValue,
                    //     calculation: {
                    //         currentTeam: isPlebsTurn ? 'Plebs' : 'Aristoi',
                    //         playerTeam: isFirstPlayer ?
                    //             (window.firstPlayerTurnValue ? 'Plebs' : 'Aristoi') :
                    //             (window.firstPlayerTurnValue ? 'Aristoi' : 'Plebs')
                    //     }
                    // });

                    // If it's Player 1's turn (firstPlayerTurn is true), check if they can move
                    // If it's Player 2's turn (firstPlayerTurn is false), check if they can move
                    if (isSoloPlay) {
                        return true;  // Always allow moves in solo play
                    }
                    else {
                        return isFirstPlayer ? firstPlayerTurn : !firstPlayerTurn;
                    }
                }

                // Add this new function near the other utility functions
                function hideFirstMoverOptions() {
                    const radioOptions = document.querySelector('.radio-options');
                    if (radioOptions) {
                        radioOptions.style.display = 'none';
                    }
                }

                // Add a debounce function at the top of your script
                function debounce(func, wait) {
                    let timeout;
                    return function executedFunction(...args) {
                        const later = () => {
                            clearTimeout(timeout);
                            func(...args);
                        };
                        clearTimeout(timeout);
                        timeout = setTimeout(later, wait);
                    };
                }

                // Modify the LogToFile function
                const LogToFile = debounce(function (message, data) {
                    try {
                        // Match server-side expected format
                        const logEntry = {
                            message: message,
                            data: data || {},
                            userId: typeof userId !== 'undefined' ? userId : null,
                            gameId: typeof currentGameId !== 'undefined' ? currentGameId : null  // Add gameId
                        };

                        // Debug log the exact string being sent
                        const jsonString = JSON.stringify(logEntry);
                        // clientLogToFile('Sending JSON:', jsonString);

                        $.ajax({
                            url: 'LoggingHandler.ashx',
                            type: 'POST',
                            data: jsonString,
                            contentType: 'application/json',
                            success: function (response) {
                                clientLogToFile('Log success:', response);
                            },
                            error: function (xhr, status, error) {
                                console.error('Default.aspx - Log error:', {
                                    error: error
                                });
                            }
                        });
                    } catch (error) {
                        clientLogToFile('Default.aspx - Error in LogToFile', {
                            error: error.message,
                            stack: error.stack,
                            message: message,
                            data: data
                        });
                    }
                }, 100);

                // Add this function to clear the log at game start
                function clearGameLog() {
                    return new Promise((resolve, reject) => {
                        $.ajax({
                            type: 'POST',
                            url: 'LoggingHandler.ashx',
                            data: {
                                clearLog: 'true'
                            },
                            success: resolve,
                            error: reject
                        });
                    });
                }

                // Strategy Guide Controls
                const strategyGuide = document.getElementById('strategyGuide');
                const btnStrategyGuide = document.getElementById('btnStrategyGuide');
                const closeBtn = document.querySelector('.strategy-guide .close-btn');

                if (btnStrategyGuide) {
                    btnStrategyGuide.addEventListener('click', function (e) {
                        e.preventDefault();
                        e.stopPropagation();
                        if (strategyGuide) {
                            strategyGuide.style.display = 'block';
                            setTimeout(() => {
                                strategyGuide.style.opacity = '1';
                                strategyGuide.style.transition = 'opacity 0.3s ease-in-out';
                            }, 10);
                        }
                    });
                }

                if (closeBtn) {
                    closeBtn.addEventListener('click', function (e) {
                        e.preventDefault();
                        e.stopPropagation();
                        if (strategyGuide) {
                            strategyGuide.style.opacity = '0';
                            strategyGuide.style.transition = 'opacity 0.3s ease-in-out';
                            setTimeout(() => {
                                strategyGuide.style.display = 'none';
                            }, 300);
                        }
                    });
                }

                // Handle clicking outside - use capture phase
                document.addEventListener('click', function (e) {
                    if (strategyGuide &&
                        strategyGuide.style.display === 'block' &&
                        !strategyGuide.contains(e.target) &&
                        e.target !== btnStrategyGuide) {
                        closeBtn.click();
                    }
                }, true);

                // Add this near the top with other SignalR initialization
                $.connection.hub.stateChanged(function (change) {
                    const connectionMessage = document.getElementById('connection-message') || createConnectionMessage();

                    switch (change.newState) {
                        case $.signalR.connectionState.connecting:
                            connectionMessage.textContent = "Establishing communications...";
                            connectionMessage.style.display = 'block';
                            break;
                        case $.signalR.connectionState.connected:
                            connectionMessage.textContent = "Communications established!";
                            setTimeout(() => {
                                connectionMessage.style.display = 'none';
                            }, 2000);
                            break;
                        case $.signalR.connectionState.reconnecting:
                            connectionMessage.textContent = "Communications breakdown... code monkey working... please wait...";
                            connectionMessage.style.display = 'block';
                            break;
                        case $.signalR.connectionState.disconnected:
                            connectionMessage.textContent = "Connection lost! Try refreshing the page...";
                            connectionMessage.style.display = 'block';
                            break;
                    }
                });

                // Add error handling
                $.connection.hub.error(function (error) {
                    const connectionMessage = document.getElementById('connection-message') || createConnectionMessage();
                    connectionMessage.textContent = "Communications error! Code monkey scratching head... Try refreshing?";
                    connectionMessage.style.display = 'block';
                });

                // Helper function to create the message element
                function createConnectionMessage() {
                    const messageDiv = document.createElement('div');
                    messageDiv.id = 'connection-message';
                    messageDiv.style.cssText = `
                        position: fixed;
                        top: 20px;
                        left: 50%;

                        background-color: rgba(0, 0, 0, 0.8);
                        color: white;
                        padding: 10px 20px;
                        border-radius: 5px;
                        z-index: 1000;
                        font-size: 14px;
                        display: none;
                        animation: fadeIn 0.3s ease-in-out;
                    `;
                    document.body.appendChild(messageDiv);
                    return messageDiv;
                }

                // Add CSS animation - THIS IS FOR THE CHAT WINDOW (I PRESUME)
                const style = document.createElement('style');
                style.textContent = `
                    @keyframes fadeIn {
                        from { opacity: 0; transform: translate(-50%, -20px); }
                        to { opacity: 1; transform: translate(-50%, 0); }
                    }

                    #connection-message {
                        transition: opacity 0.3s ease-in-out;
                    }
                `;
                document.head.appendChild(style);

                // Add a toggle button to the header area
                function toggleFocusMode(e) {
                    e.preventDefault();
                    const container = document.querySelector('.container');
                    const chatInterface = document.getElementById('chat-interface');

                    // Toggle focus mode class on body
                    document.body.classList.toggle('focus-mode');
                    const isFocusMode = document.body.classList.contains('focus-mode');

                    // Handle chat interface in multiplayer games
                    if (!isSoloPlay && chatInterface) {
                        clientLogToFile('Chat visibility before:', {
                            isFocusMode,
                            isSoloPlay,
                            currentClasses: chatInterface.className
                        });

                        // Reset classes completely
                        chatInterface.className = 'chat-interface';

                        if (isFocusMode) {
                            // Add visible class immediately for focus mode
                            chatInterface.classList.add('visible');
                        } else {
                            // Add hidden class when exiting focus mode
                            chatInterface.classList.add('hidden');
                        }

                        // clientLogToFile('Chat visibility after:', {
                        //     isFocusMode,
                        //     currentClasses: chatInterface.className
                        // });
                    }

                    // Update button text
                    const button = document.querySelector('.focus-mode-btn span');
                    if (button) {
                        button.textContent = isFocusMode ? 'Exit Focus' : 'Focus Mode';
                    }
                }

                function clientLogToFile(message, data) {
                    return new Promise((resolve, reject) => {
                        try {
                            const logEntry = {
                                message: message || 'No message',
                                data: data,
                                gameId: typeof currentGameId !== 'undefined' ? currentGameId : null,
                                userId: typeof userId !== 'undefined' ? userId : null,
                                timestamp: new Date().toISOString()
                            };

                            //console.log('Default.aspx - Sending log entry:', logEntry); // Debug what we're sending

                            $.ajax({
                                url: 'LoggingHandler.ashx',
                                type: 'POST',
                                data: JSON.stringify(logEntry),
                                contentType: 'application/json',
                                success: function (response) {
                                    //console.log('Default.aspx - Log entry success:', response);
                                    resolve(response);
                                },
                                error: function (xhr, status, error) {
                                    console.error('Default.aspx - Log entry failed:', {
                                        error: error
                                    });
                                    reject(error);
                                }
                            });
                        } catch (ex) {
                            console.error('Default.aspx - Error preparing log entry:', ex);
                            reject(ex);
                        }
                    });
                }

                function processPendingLogs() {
                    if (!window.pendingLogs || window.pendingLogs.length === 0) {
                        return Promise.resolve();
                    }

                    clientLogToFile('Processing pending logs:', window.pendingLogs.length);

                    // Process logs sequentially
                    return window.pendingLogs.reduce((promise, log) => {
                        return promise.then(() => clientLogToFile(log.message, log.data));
                    }, Promise.resolve())
                        .then(() => {
                            window.pendingLogs = [];
                        })
                        .catch(error => {
                            console.error('Default.aspx - Error processing pending logs:', error);
                        });
                }

                function updateGameRules() {
                    const victoryMode = document.getElementById('plebVictoryMode').value;
                    const plebVictoryText = {
                        'all': 'capture all noble pieces                     (The Ultimate Challenge!)',
                        'half': 'capture more than half of the noble pieces  (They will rise again!)   ',
                        'royalty': 'capture all four Kings and Queens        (Off with their heads!)  '
                    }[victoryMode];

                    console.log(plebVictoryText);

                    document.querySelector('.PlebVictoryConditions').textContent =
                        `The Plebians win if they ${plebVictoryText}`;
                }

                document.getElementById('plebVictoryMode').addEventListener('change', function () {
                    updateGameRules();
                    // Log the change for debugging
                    console.log('Victory mode changed to: ' + this.value);
                });



                function updateMenuButtonStates(isGameActive) {
                    const leaderBoardBtn = document.getElementById('<%= btnLeaderBoard.ClientID %>');
                    const replayBtn = document.getElementById('<%= btnChessGameReplay.ClientID %>');

                    if (leaderBoardBtn) {
                        if (isGameActive) {
                            leaderBoardBtn.onclick = function(e) { e.preventDefault(); };
                            leaderBoardBtn.disabled = true;
                            leaderBoardBtn.className += ' aspNetDisabled';
                        } else {
                            leaderBoardBtn.onclick = null;
                            leaderBoardBtn.classList.remove('aspNetDisabled');
                        }
                    }

                    if (replayBtn) {
                        if (isGameActive) {
                            replayBtn.onclick = function(e) { e.preventDefault(); };
                            replayBtn.disabled = true;
                            replayBtn.className += ' aspNetDisabled';
                        } else {
                            replayBtn.onclick = null;
                            replayBtn.classList.remove('aspNetDisabled');
                        }
                    }

                    console.log('default.aspx - Inside updateMenuButtonStates() - isGameActive: ', isGameActive);
                }

            </script>
        </form>
    </div>
</body>
</html>
