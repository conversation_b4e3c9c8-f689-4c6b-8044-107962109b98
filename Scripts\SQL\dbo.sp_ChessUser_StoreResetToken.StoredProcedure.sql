USE [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]
GO
/****** Object:  StoredProcedure [dbo].[sp_ChessUser_StoreResetToken]    Script Date: 4/5/2025 1:17:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [dbo].[sp_ChessUser_StoreResetToken]
    @Email NVARCHAR(100),
    @ResetToken NVARCHAR(100),
    @ExpiryDate DATETIME
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Debug output
    PRINT 'Searching for email: ' + @Email
    
    -- Check if the user exists first
    IF EXISTS (SELECT 1 FROM dbo.ChessUsers WHERE Email = @Email)
    BEGIN
        PRINT 'User found, updating reset token'
        
        UPDATE dbo.ChessUsers
        SET ResetToken = @ResetToken,
            ResetTokenExpiry = @ExpiryDate
        WHERE Email = @Email
        
        DECLARE @RowCount INT = @@ROWCOUNT
        PRINT 'Rows updated: ' + CAST(@RowCount AS NVARCHAR(10))
        <PERSON><PERSON><PERSON><PERSON> @RowCount
    END
    ELSE
    BEGIN
        PRINT 'No user found with email: ' + @Email
        RETURN 0
    END
END
GO
