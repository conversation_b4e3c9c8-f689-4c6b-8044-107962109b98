Option Strict On
Option Explicit On
Imports System.Collections.Concurrent
Imports System.Threading.Tasks
Imports Microsoft.AspNet.SignalR.Hubs
Imports Newtonsoft.Json.Linq
Imports System.Data.SqlClient
Imports System.Configuration

Namespace PvAChess.Hubs
    Public Enum GameStatus
        AwaitingPlayers = 0
        Active = 1
        Abandoned = 2
        Completed = 3
    End Enum

    <HubName("gameHub")>
    Public Class GameHub
        Inherits Common.Hubs.SignalRCoreHub(Of IGameClient)
        Implements Common.Hubs.IGameHub

        ' Dictionary to store game states - Key is game ID, Value is game state
        Private Shared gameStates As New ConcurrentDictionary(Of String, GameState)
        ' Dictionary to track players in each game - Key is game ID, Value is tuple of player connection IDs
        Private Shared gameConnections As New ConcurrentDictionary(Of String, Tuple(Of String, String))
        ' Dictionary to track games and players
        Private Shared ActiveGames As New Dictionary(Of String, HashSet(Of String))
        ' Dictionary to store userId for each connection
        Private Shared Shadows userConnections As New ConcurrentDictionary(Of String, Integer)  ' ConnectionId -> UserId
        ' Add this with the other shared fields at the top of the class
        Private Shared currentGameId As String
        ' Add at class level
        Private Shared connectionLock As New Object

        Public Class GameState
            Public Property Board As String()()
            Public Property IsPlebsTurn As Boolean
            Public Property PromotedPawns As HashSet(Of String)
            Public Property CapturedPlebs As List(Of String)
            Public Property CapturedAristoi As List(Of String)
            Public Property CurrentSuperPawn As String
            Public Property FirstPlayerIsPlebs As Boolean
            Public Property FirstPlayerUserId As Integer
            Public Property SecondPlayerUserId As Integer
            Public Property PawnStates As Dictionary(Of String, String)  ' Key: "row,col", Value: state ("normal", "radical", "super")
            Public Property CurrentMoveNumber As Integer = 0  ' Initialize to 0, first move will be 1
            Public Property VictoryMode As String = "all"
            Public Property Status As GameStatus = GameStatus.AwaitingPlayers
            Public Property LastActivityTime As DateTime = DateTime.Now

            Public Sub New()
                PromotedPawns = New HashSet(Of String)()
                CapturedPlebs = New List(Of String)()
                CapturedAristoi = New List(Of String)()
                PawnStates = New Dictionary(Of String, String)()
                Status = GameStatus.AwaitingPlayers
                LastActivityTime = DateTime.Now
            End Sub
        End Class

        Private Class BoardPosition
            Public Property Row As Integer
            Public Property Col As Integer
        End Class

        Private Function IsUserAuthenticated() As Boolean
            Return Context.User.Identity.IsAuthenticated
        End Function

        ' Join a game room
        Public Function JoinGame(gameId As String, userId As Integer, isSoloPlay As Boolean) As Task(Of Boolean) _
            Implements Common.Hubs.IGameHub.JoinGame
            Try
                LogToFile("JoinGame - Starting with FULL DETAILS", New With {
                    .gameId = gameId,
                    .userId = userId,
                    .isSoloPlay = isSoloPlay,
                    .connectionId = Context.ConnectionId,
                    .gameExists = gameStates.ContainsKey(gameId),
                    .connectionsExist = gameConnections.ContainsKey(gameId)
                })

                ' Store the userId for this connection
                If Not userConnections.ContainsKey(Context.ConnectionId) Then
                    LogToFile("JoinGame - Adding to userConnections", New With {
                        .connectionId = Context.ConnectionId,
                        .userId = userId
                    })
                    userConnections.TryAdd(Context.ConnectionId, userId)
                End If

                ' Check if game exists in memory
                Dim gameState As GameState = Nothing
                Dim connections As Tuple(Of String, String) = Nothing

                If gameStates.TryGetValue(gameId, gameState) Then
                    ' Existing game in memory - LOG EVERYTHING
                    Dim tempConn As Tuple(Of String, String) = Nothing
                    LogToFile("JoinGame - Game exists in memory - DETAILED", New With {
                        .gameId = gameId,
                        .firstPlayerUserId = gameState.FirstPlayerUserId,
                        .secondPlayerUserId = gameState.SecondPlayerUserId,
                        .status = gameState.Status.ToString(),
                        .currentUserId = userId,
                        .isReturningPlayer = (userId = gameState.FirstPlayerUserId OrElse userId = gameState.SecondPlayerUserId),
                        .connectionExists = gameConnections.TryGetValue(gameId, tempConn),
                        .connection1 = If(tempConn IsNot Nothing AndAlso tempConn.Item1 IsNot Nothing, tempConn.Item1, "null"),
                        .connection2 = If(tempConn IsNot Nothing AndAlso tempConn.Item2 IsNot Nothing, tempConn.Item2, "null")
                    })

                    ' If this is a new game and firstPlayerUserId is not set, set it now
                    If gameState.FirstPlayerUserId = 0 Then
                        gameState.FirstPlayerUserId = userId
                        LogToFile("JoinGame - Setting FirstPlayerUserId", New With {
                            .gameId = gameId,
                            .firstPlayerUserId = userId
                        })
                    End If

                    ' FIRST check if this is a returning player, BEFORE checking connections
                    If userId = gameState.FirstPlayerUserId OrElse userId = gameState.SecondPlayerUserId Then
                        LogToFile("JoinGame - Returning player detected - DETAILED", New With {
                            .gameId = gameId,
                            .userId = userId,
                            .isFirstPlayer = (userId = gameState.FirstPlayerUserId),
                            .isSecondPlayer = (userId = gameState.SecondPlayerUserId)
                        })

                        SyncLock connectionLock
                            ' Get existing connections if any
                            If Not gameConnections.TryGetValue(gameId, connections) Then
                                ' No connections exist, create new one
                                connections = New Tuple(Of String, String)(Nothing, Nothing)
                            End If

                            ' Update appropriate connection slot
                            If userId = gameState.FirstPlayerUserId Then
                                connections = New Tuple(Of String, String)(Context.ConnectionId, connections.Item2)
                            Else
                                connections = New Tuple(Of String, String)(connections.Item1, Context.ConnectionId)
                            End If

                            ' Update or add connections
                            If gameConnections.ContainsKey(gameId) Then
                                gameConnections(gameId) = connections
                            Else
                                gameConnections.TryAdd(gameId, connections)
                            End If

                            ' Add to group
                            Groups.Add(Context.ConnectionId, gameId).Wait()

                            ' Reconstruct and send game state
                            Dim reconstructedState = ReconstructBoardState(gameId)

                            ' Update game state
                            gameState.Board = reconstructedState.Board
                            gameState.CapturedPlebs = reconstructedState.CapturedPlebs
                            gameState.CapturedAristoi = reconstructedState.CapturedAristoi
                            gameState.IsPlebsTurn = reconstructedState.IsPlebsTurn

                            ' Notify client about joining with the correct role information
                            LogToFile("JoinGame - Before sending PlayerJoined", New With {
                                .gameId = gameId,
                                .firstPlayerIsPlebs = gameState.FirstPlayerIsPlebs,
                                .isPlebsTurn = gameState.IsPlebsTurn,
                                .userId = userId,
                                .firstPlayerUserId = gameState.FirstPlayerUserId,
                                .isUserFirstPlayer = (userId = gameState.FirstPlayerUserId)
                            })

                            ' Add explicit role information
                            Dim isPlayerPlebs As Boolean = If(userId = gameState.FirstPlayerUserId,
                                                                  gameState.FirstPlayerIsPlebs,
                                                                  Not gameState.FirstPlayerIsPlebs)

                            LogToFile("JoinGame - Role calculation", New With {
                                .gameId = gameId,
                                .userId = userId,
                                .isFirstPlayer = (userId = gameState.FirstPlayerUserId),
                                .firstPlayerIsPlebs = gameState.FirstPlayerIsPlebs,
                                .calculatedPlayerRole = If(isPlayerPlebs, "Plebs", "Aristoi")
                            })

                            Clients.Caller.PlayerJoined(New With {
                                .FirstPlayerIsPlebs = gameState.FirstPlayerIsPlebs,
                                .IsPlebsTurn = gameState.IsPlebsTurn,
                                .FirstPlayerTurn = (gameState.IsPlebsTurn = gameState.FirstPlayerIsPlebs),
                                .IsFirstPlayer = (userId = gameState.FirstPlayerUserId),
                                .IsPlayerPlebs = isPlayerPlebs  ' Send explicit role information
                            })

                            Return Task.FromResult(True)
                        End SyncLock
                    End If

                    ' For non-returning players, we need to get connections
                    LogToFile("JoinGame - After rejoin check", New With {
                        .gameId = gameId,
                        .userId = userId,
                        .connectionsExist = gameConnections.ContainsKey(gameId),
                        .previousBlockExecuted = False
                    })

                    ' Get fresh connections information outside the previous SyncLock
                    SyncLock connectionLock
                        ' Check connections again after lock
                        If Not gameConnections.TryGetValue(gameId, connections) Then
                            LogToFile("JoinGame - No connections found for game after rejoin check", New With {
                                .gameId = gameId,
                                .userId = userId
                            })

                            ' This is a critical error - game exists in gameStates but not in gameConnections
                            ' We'll create connections to recover from this error state
                            connections = New Tuple(Of String, String)(Nothing, Nothing)
                            gameConnections.TryAdd(gameId, connections)
                        End If

                        ' Check if this player is the first player
                        If userId = gameState.FirstPlayerUserId Then
                            ' This player should be in connection slot 1
                            Dim newConnections = New Tuple(Of String, String)(Context.ConnectionId, connections.Item2)
                            Dim updateResult = gameConnections.TryUpdate(gameId, newConnections, connections)

                            LogToFile("JoinGame - First player connection", New With {
                                .gameId = gameId,
                                .updateResult = updateResult,
                                .userId = userId,
                                .oldConnection1 = connections.Item1,
                                .newConnection1 = Context.ConnectionId
                            })

                            Groups.Add(Context.ConnectionId, gameId).Wait()
                            Return Task.FromResult(True)
                        End If

                        ' Check if this player is already the second player
                        If userId = gameState.SecondPlayerUserId Then
                            ' This player should be in connection slot 2
                            Dim newConnections = New Tuple(Of String, String)(connections.Item1, Context.ConnectionId)
                            Dim updateResult = gameConnections.TryUpdate(gameId, newConnections, connections)

                            LogToFile("JoinGame - Second player reconnection", New With {
                                .gameId = gameId,
                                .updateResult = updateResult,
                                .userId = userId,
                                .oldConnection2 = connections.Item2,
                                .newConnection2 = Context.ConnectionId
                            })

                            Groups.Add(Context.ConnectionId, gameId).Wait()
                            Return Task.FromResult(True)
                        End If

                        ' Check if second player slot is available
                        If connections.Item2 Is Nothing Then
                            ' Add second player
                            gameState.SecondPlayerUserId = userId

                            ' Update connections with Player 2's info
                            Dim newConnections = New Tuple(Of String, String)(connections.Item1, Context.ConnectionId)
                            Dim updateResult = gameConnections.TryUpdate(gameId, newConnections, connections)

                            LogToFile("JoinGame - Second player joining", New With {
                                .gameId = gameId,
                                .updateResult = updateResult,
                                .userId = userId
                            })

                            Groups.Add(Context.ConnectionId, gameId).Wait()

                            ' Update game status
                            gameState.Status = GameStatus.Active

                            ' Update database with second player
                            Try
                                ChessGameLogger.DB_AddSecondPlayer(gameId, userId)
                            Catch ex As Exception
                                LogToFile("JoinGame - Error updating database with second player", New With {
                                    .gameId = gameId,
                                    .userId = userId,
                                    .error = ex.Message
                                })
                                ' Continue anyway - the game will work in memory
                            End Try

                            ' Notify both players
                            Clients.Group(gameId).PlayerJoined(New With {
                                .FirstPlayerIsPlebs = gameState.FirstPlayerIsPlebs,
                                .IsPlebsTurn = gameState.IsPlebsTurn,
                                .FirstPlayerTurn = True
                            })

                            LogToFile("JoinGame - Second player joined successfully", New With {
                                .gameId = gameId,
                                .userId = userId
                            })

                            Return Task.FromResult(True)
                        Else

                            ' Game already has two players
                            LogToFile("JoinGame - Game already has two players", New With {
                                .gameId = gameId,
                                .connection1 = connections.Item1,
                                .connection2 = connections.Item2,
                                .firstPlayerUserId = gameState.FirstPlayerUserId,
                                .secondPlayerUserId = gameState.SecondPlayerUserId,
                                .currentUserId = userId
                            })
                            Return Task.FromResult(False)
                        End If
                    End SyncLock
                Else
                    ' Game doesn't exist in memory, check if it exists in database
                    LogToFile("JoinGame - Game not in memory, checking database", New With {
                        .gameId = gameId,
                        .userId = userId
                    })

                    ' Try to load from database
                    gameState = ReconstructBoardState(gameId)

                    If gameState IsNot Nothing Then
                        ' Game found in database, add to memory
                        LogToFile("JoinGame - Found game in database, restoring", New With {
                            .gameId = gameId,
                            .firstPlayerUserId = gameState.FirstPlayerUserId,
                            .firstPlayerIsPlebs = gameState.FirstPlayerIsPlebs
                        })

                        ' Store in memory
                        gameStates.TryAdd(gameId, gameState)

                        ' Continue with join process for returning player
                        ' Create new connection for this player
                        SyncLock connectionLock
                            connections = New Tuple(Of String, String)(Nothing, Nothing)

                            ' Update appropriate connection slot
                            If userId = gameState.FirstPlayerUserId Then
                                connections = New Tuple(Of String, String)(Context.ConnectionId, Nothing)
                            Else
                                connections = New Tuple(Of String, String)(Nothing, Context.ConnectionId)
                            End If

                            ' Add connections
                            gameConnections.TryAdd(gameId, connections)

                            ' Add to group
                            Groups.Add(Context.ConnectionId, gameId).Wait()
                        End SyncLock
                    End If
                End If

                LogToFile("JoinGame - Connections check", New With {
                    .gameId = gameId,
                    .connectionsExists = gameConnections.ContainsKey(gameId),
                    .userId = userId
                })

            Catch ex As Exception
                LogToFile("JoinGame error", New With {
                    .error = ex.Message,
                    .stackTrace = ex.StackTrace
                })
                Return Task.FromResult(False)
            End Try
        End Function
        ' Make a move
        Public Function MakeMove(gameId As String, moveData As Object, moveType As String) As Task Implements Common.Hubs.IGameHub.MakeMove
            Try
                Dim state As GameState = Nothing
                If gameStates.TryGetValue(gameId, state) Then
                    ' Convert moveData to JObject if it isn't already
                    Dim moveDataObj = TryCast(moveData, JObject)
                    If moveDataObj Is Nothing Then
                        moveDataObj = JObject.FromObject(moveData)
                    End If

                    ' Update board state
                    state.Board = moveDataObj("board").ToObject(Of String()())
                    state.IsPlebsTurn = moveDataObj("nextState")("isPlebsTurn").ToObject(Of Boolean)

                    ' Update promoted pawns
                    state.PromotedPawns.Clear()
                    If moveDataObj("promotedPawns") IsNot Nothing Then
                        For Each pawn As JToken In moveDataObj("promotedPawns")
                            state.PromotedPawns.Add(pawn.ToString())
                        Next
                    End If

                    ' Update captured pieces - use the client's capture lists directly
                    ' since the client's isValidMove() already enforces the rules
                    state.CapturedPlebs = moveDataObj("capturedPlebs").ToObject(Of List(Of String))()
                    state.CapturedAristoi = moveDataObj("capturedAristoi").ToObject(Of List(Of String))()

                    ' Update pawn states
                    state.PawnStates.Clear()
                    If moveDataObj("pawnStates") IsNot Nothing Then
                        For Each pair As JArray In moveDataObj("pawnStates")
                            state.PawnStates.Add(pair(0).ToString(), pair(1).ToString())
                        Next
                    End If

                    ' Update current super pawn
                    state.CurrentSuperPawn = If(moveDataObj("currentSuperPawn")?.ToObject(Of String)(), Nothing)

                    ' Broadcast the updated state
                    Clients.Group(gameId).UpdateGameState(New With {
                        .moveType = moveType,
                        .board = state.Board,
                        .isPlebsTurn = state.IsPlebsTurn,
                        .promotedPawns = state.PromotedPawns,
                        .capturedPlebs = state.CapturedPlebs,
                        .capturedAristoi = state.CapturedAristoi,
                        .currentSuperPawn = state.CurrentSuperPawn,
                        .pawnStates = state.PawnStates,
                        .nextState = moveDataObj("nextState")
                    })

                    Return Task.CompletedTask
                End If

                Return Task.CompletedTask
            Catch ex As Exception
                LogToFile("Error in MakeMove", New With {
                    .error = ex.Message,
                    .stackTrace = ex.StackTrace,
                    .moveData = moveData
                }, gameId)
                Throw
            End Try
        End Function

        Private Function InitializeBoard() As String()()
            Return New String()() {
                New String() {"black-rook", "black-knight", "black-bishop", "black-queen", "black-king", "black-bishop", "black-knight", "black-rook"},
                New String() {"black-pawn", "black-pawn", "black-pawn", "black-pawn", "black-pawn", "black-pawn", "black-pawn", "black-pawn"},
                New String() {Nothing, Nothing, Nothing, Nothing, Nothing, Nothing, Nothing, Nothing},
                New String() {Nothing, Nothing, Nothing, Nothing, Nothing, Nothing, Nothing, Nothing},
                New String() {Nothing, Nothing, Nothing, Nothing, Nothing, Nothing, Nothing, Nothing},
                New String() {Nothing, Nothing, Nothing, Nothing, Nothing, Nothing, Nothing, Nothing},
                New String() {"white-pawn", "white-pawn", "white-pawn", "white-pawn", "white-pawn", "white-pawn", "white-pawn", "white-pawn"},
                New String() {"white-rook", "white-knight", "white-bishop", "white-queen", "white-king", "white-bishop", "white-knight", "white-rook"}
            }
        End Function

        Private Function CheckVictory(game As GameState) As String
            Dim remainingNobles = 0
            Dim remainingKingsQueens = 0
            Dim remainingPawns = False

            ' Count pieces
            For Each row In game.Board
                For Each piece In row
                    If piece IsNot Nothing Then
                        If piece.EndsWith("pawn") Then
                            remainingPawns = True
                        Else
                            remainingNobles += 1
                            If piece.EndsWith("king") OrElse piece.EndsWith("queen") Then
                                remainingKingsQueens += 1
                            End If
                        End If
                    End If
                Next
            Next

            ' Check victory based on mode
            Select Case game.VictoryMode.ToLower()
                Case "half"
                    If game.CapturedAristoi.Count > (remainingNobles + game.CapturedAristoi.Count) / 2 Then
                        Return "Plebs"
                    End If
                Case "royalty"
                    If remainingKingsQueens = 0 Then
                        Return "Plebs"
                    End If
                Case Else ' "all"
                    If remainingNobles = 0 Then
                        Return "Plebs"
                    End If
            End Select

            ' Check Aristoi victory (unchanged)
            If Not remainingPawns Then
                Return "Aristoi"
            End If

            Return Nothing
        End Function

        Public Function SendTestMessage(message As String) As Task
            System.Diagnostics.Debug.WriteLine("Hub received message: " & message)
            Return Clients.All.ReceiveMessage(" " & message)
        End Function

        Public Function StartNewGame(gameId As String) As Task Implements Common.Hubs.IGameHub.StartNewGame
            LogToFile("StartNewGame call to Clients.Group(gameId).initializeNewGame() ", New With {
                    .gameId = gameId
                })

            ' Default to "all" if no victory mode is specified
            If gameStates.ContainsKey(gameId) Then
                If String.IsNullOrEmpty(gameStates(gameId).VictoryMode) Then
                    gameStates(gameId).VictoryMode = "all"
                End If
            End If

            Return Clients.Group(gameId).InitializeNewGame()
        End Function

        Public Function SetVictoryMode(gameId As String, victoryMode As String) As Task
            If gameStates.ContainsKey(gameId) Then
                gameStates(gameId).VictoryMode = If(String.IsNullOrEmpty(victoryMode), "all", victoryMode)
                LogToFile("Victory mode set", New With {
                    .gameId = gameId,
                    .victoryMode = victoryMode
                })
            End If
            Return Task.CompletedTask
        End Function

        Public Function AnnounceVictory(gameId As String, endGameAction As String, endGameUserId As Integer) As Task Implements Common.Hubs.IGameHub.AnnounceVictory
            Try
                ' Log the game end before announcing
                LogGameEnd(gameId, endGameAction, endGameUserId)

                ' Then announce to clients using the original event name
                Return Clients.Group(gameId).VictoryAnnounced(endGameAction)
            Catch ex As Exception
                LogToFile("Error in AnnounceVictory", New With {
                    .gameId = gameId,
                    .endGameAction = endGameAction,
                    .endGameUserId = endGameUserId,
                    .error = ex.Message,
                    .stack = ex.StackTrace
                })
                Throw
            End Try
        End Function

        Public Sub LogMove(moveData As Object)
            Try
                Dim userId As Integer = -1
                userConnections.TryGetValue(Context.ConnectionId, userId)

                ' Get current game state to access move number
                Dim gameState As GameState = Nothing
                Dim moveDataObj = DirectCast(moveData, JObject)
                If gameStates.TryGetValue(moveDataObj.Value(Of String)("GameId"), gameState) Then
                    ' Increment move number
                    gameState.CurrentMoveNumber += 1

                    Dim move As New ChessMove With {
                        .GameId = moveDataObj.Value(Of String)("GameId"),
                        .PlayerRole = moveDataObj.Value(Of String)("PlayerRole"),
                        .FromRow = moveDataObj.Value(Of Integer)("FromRow"),
                        .FromCol = moveDataObj.Value(Of Integer)("FromCol"),
                        .ToRow = moveDataObj.Value(Of Integer)("ToRow"),
                        .ToCol = moveDataObj.Value(Of Integer)("ToCol"),
                        .PieceMoved = moveDataObj.Value(Of String)("PieceMoved"),
                        .PieceCaptured = moveDataObj.Value(Of String)("PieceCaptured"),
                        .WasPromotion = moveDataObj.Value(Of Boolean)("WasPromotion"),
                        .UserId = userId,
                        .MoveNumber = gameState.CurrentMoveNumber  ' Add the move number
                    }

                    ChessGameLogger.DB_LogMove(move)
                End If
            Catch ex As Exception
                LogToFile("Error in LogMove", New With {
                    .error = ex.Message,
                    .stackTrace = ex.StackTrace
                })
            End Try
        End Sub

        Public Function LogGameEnd(gameId As String, endGameAction As String, endGameUserId As Integer) As Task
            Try
                LogToFile("Game end", New With {
                    .gameId = gameId,
                    .endGameAction = endGameAction,
                    .endGameUserId = endGameUserId
                })

                ChessGameLogger.DB_EndGame(gameId, endGameAction, endGameUserId)
                Return Task.CompletedTask
            Catch ex As Exception
                LogToFile("Game end error", New With {
                    .error = ex.Message,
                    .gameId = gameId,
                    .endGameAction = endGameAction,
                    .endGameUserId = endGameUserId
                })
                Return Task.CompletedTask
            End Try
        End Function

        ' Check if a game exists in the database
        Public Function CheckGameExists(gameId As String) As Task(Of Boolean) Implements Common.Hubs.IGameHub.CheckGameExists
            Try
                LogToFile("CheckGameExists", New With {
                    .gameId = gameId
                })

                ' First check if game exists in memory
                If gameStates.ContainsKey(gameId) Then
                    Return Task.FromResult(True)
                End If

                ' Then check database
                Using conn As New SqlConnection(ConfigurationManager.ConnectionStrings("ChessGameConnection").ConnectionString)
                    Using cmd As New SqlCommand("SELECT COUNT(*) FROM ChessGames WHERE GameId = @GameId", conn)
                        cmd.Parameters.AddWithValue("@GameId", gameId)

                        conn.Open()
                        Dim count As Integer = Convert.ToInt32(cmd.ExecuteScalar())

                        Return Task.FromResult(count > 0)
                    End Using
                End Using
            Catch ex As Exception
                LogToFile("CheckGameExists error", New With {
                    .error = ex.Message,
                    .gameId = gameId
                })
                Return Task.FromResult(False)
            End Try
        End Function

        Public Function LogGameMove(moveLogData As Object) As Task Implements Common.Hubs.IGameHub.LogGameMove
            Try
                Dim jObject = DirectCast(moveLogData, JObject)
                Dim userId As Integer
                If Not Integer.TryParse(jObject.Value(Of String)("userId"), userId) Then
                    userId = -1
                End If
                Dim gameId = jObject.Value(Of String)("gameId")

                ' Get current game state to access move number
                Dim gameState As GameState = gameStates.GetOrAdd(gameId, Function(key)
                                                                             Dim newState = New GameState()
                                                                             newState.CurrentMoveNumber = 0
                                                                             Return newState
                                                                         End Function)
                ' Increment move number
                gameState.CurrentMoveNumber += 1

                Dim move As New ChessMove With {
                    .GameId = gameId,
                    .PlayerRole = jObject.Value(Of String)("playerRole"),
                    .FromRow = jObject.Value(Of Integer)("fromRow"),
                    .FromCol = jObject.Value(Of Integer)("fromCol"),
                    .ToRow = jObject.Value(Of Integer)("toRow"),
                    .ToCol = jObject.Value(Of Integer)("toCol"),
                    .PieceMoved = jObject.Value(Of String)("pieceMoved"),
                    .PieceCaptured = jObject.Value(Of String)("pieceCaptured"),
                    .UserId = userId,
                    .MoveNumber = gameState.CurrentMoveNumber
                }

                ChessGameLogger.DB_LogMove(move)

                Return Task.CompletedTask

            Catch ex As Exception
                LogToFile("Error in LogGameMove", New With {
                    .error = ex.Message,
                    .stackTrace = ex.StackTrace
                })
                Return Task.CompletedTask
            End Try
        End Function

        Public Function UpdateFirstPlayerSelection(gameId As String, isPlebs As Boolean, userId As Integer) As Task Implements Common.Hubs.IGameHub.UpdateFirstPlayerSelection
            Try
                LogToFile("UpdateFirstPlayerSelection - Starting", New With {
                    .gameId = gameId,
                    .isPlebs = isPlebs
                })

                ' Get or create game state
                Dim gameState As GameState = Nothing
                If Not gameStates.TryGetValue(gameId, gameState) Then
                    gameState = New GameState()
                    gameStates.TryAdd(gameId, gameState)

                    ' Use the userId parameter directly
                    LogToFile("UpdateFirstPlayerSelection - Using userId parameter", New With {
                        .gameId = gameId,
                        .userId = userId
                    })

                    ' Set the first player userId
                    gameState.FirstPlayerUserId = userId

                    ' Create a connection entry for the first player
                    Dim connections = New Tuple(Of String, String)(Context.ConnectionId, Nothing)
                    gameConnections.TryAdd(gameId, connections)

                    ' Add database record for new game
                    Dim success As Boolean = False
                    Try
                        success = ChessGameLogger.DB_StartNewGame(gameId, False, userId)
                    Catch ex As Exception
                        LogToFile("UpdateFirstPlayerSelection - Database error", New With {
                            .gameId = gameId,
                            .userId = userId,
                            .error = ex.Message
                        })
                        ' Continue anyway - the game will work in memory
                    End Try

                    LogToFile("UpdateFirstPlayerSelection - Created new game", New With {
                        .gameId = gameId,
                        .firstPlayerUserId = userId,
                        .connectionId = Context.ConnectionId,
                        .dbSuccess = success
                    })
                End If

                ' Update state
                gameState.FirstPlayerIsPlebs = isPlebs
                gameState.IsPlebsTurn = isPlebs

                LogToFile("UpdateFirstPlayerSelection - State updated", New With {
                    .gameId = gameId,
                    .firstPlayerIsPlebs = gameState.FirstPlayerIsPlebs,
                    .isPlebsTurn = gameState.IsPlebsTurn,
                    .firstPlayerUserId = gameState.FirstPlayerUserId
                })

                Return Task.CompletedTask
            Catch ex As Exception
                LogToFile("UpdateFirstPlayerSelection - Error", New With {
                    .error = ex.Message,
                    .stackTrace = ex.StackTrace,
                    .gameId = gameId
                })
                Return Task.CompletedTask
            End Try
        End Function

        Private Function GetUserIdFromConnectionId(connectionId As String) As Integer
            Dim userId As Integer = -1
            If userConnections.TryGetValue(connectionId, userId) Then
                Return userId
            End If
            Return -1
        End Function

        Private Function IsFirstPlayer(gameId As String, connectionId As String) As Boolean
            Dim connections As Tuple(Of String, String) = Nothing
            If gameConnections.TryGetValue(gameId, connections) Then
                Return connections.Item1 = connectionId
            End If
            Return False
        End Function

        Public Function SendChatMessage(gameId As String, message As String) As Task Implements Common.Hubs.IGameHub.SendChatMessage
            Try
                LogToFile("SendChatMessage - Starting", New With {
                    .gameId = gameId,
                    .sender = Context.ConnectionId,
                    .message = message
                })

                ' First ensure sender is in the group using base class method
                MyBase.JoinGroup(gameId)

                ' Send to group using original event name
                Return Clients.Group(gameId).ReceiveChatMessage(Context.ConnectionId, message)
            Catch ex As Exception
                LogToFile("SendChatMessage - Error", New With {
                    .error = ex.Message,
                    .stackTrace = ex.StackTrace
                })
                Throw
            End Try
        End Function

        Private Sub LogToFile(message As String, Optional data As Object = Nothing, Optional GameId As String = "")
            ' Create wrapper that includes ConnectionId
            Dim contextExists = (Context IsNot Nothing)
            Dim connectionIdValue = If(Context?.ConnectionId, "context-null")

            Dim logData As New With {
                .originalData = data,
                .connectionId = connectionIdValue,
                .hasContext = contextExists
            }

            ' Pass to existing logging system
            LoggingHandler.LogToFile(message, logData, GameId)
        End Sub

        Public Overrides Function OnReconnected() As Task
            Try
                Dim userId As Integer = -1
                If userConnections.TryGetValue(Context.ConnectionId, userId) Then
                    LogToFile("OnReconnected - User found", New With {
                        .userId = userId,
                        .connectionId = Context.ConnectionId
                    })

                    ' Find games this user was part of
                    For Each kvp In gameStates
                        Dim gameId = kvp.Key
                        Dim gameState = kvp.Value

                        ' Check if user is part of this game
                        If gameState.FirstPlayerUserId = userId OrElse gameState.SecondPlayerUserId = userId Then
                            ' Re-add to group
                            Groups.Add(Context.ConnectionId, gameId).Wait()

                            ' Update connection info
                            Dim connections As Tuple(Of String, String) = Nothing
                            If gameConnections.TryGetValue(gameId, connections) Then
                                Dim newConnections As Tuple(Of String, String)

                                If gameState.FirstPlayerUserId = userId Then
                                    ' First player reconnecting
                                    newConnections = New Tuple(Of String, String)(Context.ConnectionId, connections.Item2)
                                Else
                                    ' Second player reconnecting
                                    newConnections = New Tuple(Of String, String)(connections.Item1, Context.ConnectionId)
                                End If

                                ' Update connections
                                gameConnections.TryUpdate(gameId, newConnections, connections)

                                ' Update game status
                                If gameState.Status = GameStatus.Abandoned Then
                                    gameState.Status = GameStatus.Active
                                End If

                                ' Notify other player
                                Clients.Group(gameId).playerReconnected(userId)
                            End If
                        End If
                    Next
                End If

                Return MyBase.OnReconnected()
            Catch ex As Exception
                LogToFile("OnReconnected error", New With {
                    .error = ex.Message,
                    .stack = ex.StackTrace
                })
                Return MyBase.OnReconnected()
            End Try
        End Function

        Public Overrides Function OnDisconnected(stopCalled As Boolean) As Task
            Try
                ' Find games this player was in
                Dim gamesToUpdate = gameConnections.Where(Function(g) _
                    g.Value.Item1 = Context.ConnectionId OrElse
                    g.Value.Item2 = Context.ConnectionId).Select(Function(g) g.Key).ToList()

                LogToFile("OnDisconnected - Found games", New With {
                    .connectionId = Context.ConnectionId,
                    .gameCount = gamesToUpdate.Count
                })

                For Each gameId In gamesToUpdate
                    Dim state As GameState = Nothing
                    Dim connections As Tuple(Of String, String) = Nothing

                    ' Mark game status
                    If gameStates.TryGetValue(gameId, state) Then
                        state.Status = GameStatus.Abandoned
                        state.LastActivityTime = DateTime.Now

                        LogToFile("OnDisconnected - Updated game status", New With {
                            .gameId = gameId,
                            .status = state.Status.ToString()
                        })
                    End If

                    ' Update connections
                    If gameConnections.TryGetValue(gameId, connections) Then
                        Dim newConnections As Tuple(Of String, String) = Nothing

                        If connections.Item1 = Context.ConnectionId Then
                            newConnections = New Tuple(Of String, String)(Nothing, connections.Item2)
                        Else
                            newConnections = New Tuple(Of String, String)(connections.Item1, Nothing)
                        End If

                        ' Update the connections
                        If gameConnections.TryUpdate(gameId, newConnections, connections) Then
                            LogToFile("OnDisconnected - Updated connections", New With {
                                .gameId = gameId
                            })
                        End If

                        ' Notify other player if they're still connected
                        Dim otherConnectionId = If(connections.Item1 = Context.ConnectionId,
                                                 connections.Item2, connections.Item1)
                        If otherConnectionId IsNot Nothing Then
                            Try
                                Clients.Client(otherConnectionId).PlayerDisconnected()
                                LogToFile("OnDisconnected - Notified other player", New With {
                                    .gameId = gameId,
                                    .otherConnectionId = otherConnectionId
                                })
                            Catch ex As Exception
                                LogToFile("OnDisconnected - Failed to notify other player", New With {
                                    .error = ex.Message
                                })
                            End Try
                        End If
                    End If
                Next

                ' Remove user connection
                Dim userId As Integer
                If userConnections.TryRemove(Context.ConnectionId, userId) Then
                    LogToFile("OnDisconnected - Removed user connection", New With {
                        .connectionId = Context.ConnectionId,
                        .userId = userId
                    })
                End If

                Return MyBase.OnDisconnected(stopCalled)
            Catch ex As Exception
                LogToFile("OnDisconnected error", New With {
                    .error = ex.Message,
                    .stackTrace = ex.StackTrace
                })
                Return MyBase.OnDisconnected(stopCalled)
            End Try
        End Function

        Private Function ReconstructBoardState(gameId As String) As GameState
            Dim state As New GameState()

            Try
                ' Initialize default values for the game state
                state.FirstPlayerUserId = 0
                state.FirstPlayerIsPlebs = True
                state.IsPlebsTurn = True
                state.Status = GameStatus.AwaitingPlayers
                state.PromotedPawns = New HashSet(Of String)()
                state.PawnStates = New Dictionary(Of String, String)()

                ' Initialize the board state
                state.Board = New String(7)() {}
                For i As Integer = 0 To 7
                    state.Board(i) = New String(7) {}
                Next

                state.CapturedPlebs = New List(Of String)()
                state.CapturedAristoi = New List(Of String)()

                ' Try to get game data from database
                Using conn As New SqlConnection(ConfigurationManager.ConnectionStrings("ChessGameConnection").ConnectionString)
                    ' First get the basic game state
                    Using cmd As New SqlCommand("sp_ChessGame_GetGameState", conn)
                        cmd.CommandType = CommandType.StoredProcedure
                        cmd.Parameters.AddWithValue("@GameId", gameId)

                        Try
                            conn.Open()
                            Using reader As SqlDataReader = cmd.ExecuteReader()
                                If Not reader.Read() Then
                                    LogToFile("ReconstructBoardState - No game found", New With {.gameId = gameId})
                                    Return state ' Return initialized state instead of null
                                End If

                                ' Use correct column names from the database
                                state.FirstPlayerUserId = If(reader.IsDBNull(reader.GetOrdinal("FirstPlayerUserId")), 0,
                                                           reader.GetInt32(reader.GetOrdinal("FirstPlayerUserId")))

                                ' Check if FirstPlayerIsPlebs column exists
                                Try
                                    state.FirstPlayerIsPlebs = If(reader.IsDBNull(reader.GetOrdinal("FirstPlayerIsPlebs")), True,
                                                               reader.GetBoolean(reader.GetOrdinal("FirstPlayerIsPlebs")))
                                Catch ex As Exception
                                    ' Column doesn't exist, use default value
                                    state.FirstPlayerIsPlebs = True
                                    LogToFile("FirstPlayerIsPlebs column not found, using default", New With {.gameId = gameId})
                                End Try

                                ' Get SecondPlayerUserId if available
                                Try
                                    state.SecondPlayerUserId = If(reader.IsDBNull(reader.GetOrdinal("SecondPlayerUserId")), 0,
                                                               reader.GetInt32(reader.GetOrdinal("SecondPlayerUserId")))
                                Catch ex As Exception
                                    state.SecondPlayerUserId = 0
                                End Try

                                ' Set game status based on players
                                If state.SecondPlayerUserId > 0 Then
                                    state.Status = GameStatus.Active
                                End If
                            End Using
                        Catch ex As Exception
                            LogToFile("Error reading game state from database", New With {
                                .gameId = gameId,
                                .error = ex.Message
                            })
                            ' Continue with default state
                        End Try
                    End Using
                End Using

                Return state

            Catch ex As Exception
                LogToFile("ReconstructBoardState error", New With {
                    .gameId = gameId,
                    .error = ex.Message,
                    .stackTrace = ex.StackTrace
                })

                ' Create a minimal valid state instead of returning null
                Dim fallbackState As New GameState()
                fallbackState.FirstPlayerUserId = 0
                fallbackState.FirstPlayerIsPlebs = True
                fallbackState.IsPlebsTurn = True
                fallbackState.Status = GameStatus.AwaitingPlayers
                fallbackState.PromotedPawns = New HashSet(Of String)()
                fallbackState.PawnStates = New Dictionary(Of String, String)()

                ' Initialize the board state
                fallbackState.Board = New String(7)() {}
                For i As Integer = 0 To 7
                    fallbackState.Board(i) = New String(7) {}
                Next

                fallbackState.CapturedPlebs = New List(Of String)()
                fallbackState.CapturedAristoi = New List(Of String)()

                Return fallbackState
            End Try
        End Function

        Private Sub InitializeStartingPositions(finalPositions As Dictionary(Of String, Object))
            ' Initialize Plebs (black pieces)
            finalPositions("black-rook-1") = New BoardPosition With {.Row = 0, .Col = 0}
            finalPositions("black-knight-1") = New BoardPosition With {.Row = 0, .Col = 1}
            finalPositions("black-bishop-1") = New BoardPosition With {.Row = 0, .Col = 2}
            finalPositions("black-queen") = New BoardPosition With {.Row = 0, .Col = 3}
            finalPositions("black-king") = New BoardPosition With {.Row = 0, .Col = 4}
            finalPositions("black-bishop-2") = New BoardPosition With {.Row = 0, .Col = 5}
            finalPositions("black-knight-2") = New BoardPosition With {.Row = 0, .Col = 6}
            finalPositions("black-rook-2") = New BoardPosition With {.Row = 0, .Col = 7}

            For i As Integer = 0 To 7
                finalPositions("black-pawn-" & i) = New BoardPosition With {.Row = 1, .Col = i}
            Next

            ' Initialize Aristoi (white pieces)
            For i As Integer = 0 To 7
                finalPositions("white-pawn-" & i) = New BoardPosition With {.Row = 6, .Col = i}
            Next

            finalPositions("white-rook-1") = New BoardPosition With {.Row = 7, .Col = 0}
            finalPositions("white-knight-1") = New BoardPosition With {.Row = 7, .Col = 1}
            finalPositions("white-bishop-1") = New BoardPosition With {.Row = 7, .Col = 2}
            finalPositions("white-queen") = New BoardPosition With {.Row = 7, .Col = 3}
            finalPositions("white-king") = New BoardPosition With {.Row = 7, .Col = 4}
            finalPositions("white-bishop-2") = New BoardPosition With {.Row = 7, .Col = 5}
            finalPositions("white-knight-2") = New BoardPosition With {.Row = 7, .Col = 6}
            finalPositions("white-rook-2") = New BoardPosition With {.Row = 7, .Col = 7}
        End Sub

        Private Sub SetupFinalBoardState(state As GameState, finalPositions As Dictionary(Of String, Object))
            ' Initialize empty board
            state.Board = New String(7)() {}
            For i As Integer = 0 To 7
                state.Board(i) = New String(7) {}
            Next

            ' Initialize capture lists
            state.CapturedPlebs = New List(Of String)()
            state.CapturedAristoi = New List(Of String)()

            ' Place pieces in their final positions
            For Each kvp In finalPositions
                Dim piece As String = kvp.Key
                Dim position As Object = kvp.Value

                Select Case position.ToString()
                    Case "PlebsPrison"
                        state.CapturedPlebs.Add(piece)
                    Case "AristoiPrison"
                        state.CapturedAristoi.Add(piece)
                    Case Else
                        ' Place on board
                        Dim pos = TryCast(position, BoardPosition)
                        If pos IsNot Nothing Then
                            state.Board(pos.Row)(pos.Col) = piece
                        End If
                End Select
            Next
        End Sub

    End Class

End Namespace
