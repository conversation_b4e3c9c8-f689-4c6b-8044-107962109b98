﻿Imports System.Data.SqlClient
Imports System.Configuration
Imports System.Data

Namespace PvAChess
    Partial Class LeaderBoard
        Inherits System.Web.UI.Page

        Private Sub LeaderBoard_Load(sender As Object, e As EventArgs) Handles Me.Load
            If Not IsPostBack Then
                LoadOverallStats()
            End If
        End Sub

        Protected Sub btnOverall_Click(ByVal sender As Object, ByVal e As System.EventArgs)
            mvLeaderboard.ActiveViewIndex = 0
            LoadOverallStats()
            UpdateButtonStyles(DirectCast(sender, Button))
        End Sub

        Protected Sub btnAggressive_Click(ByVal sender As Object, ByVal e As System.EventArgs)
            mvLeaderboard.ActiveViewIndex = 1
            LoadAggressiveStats()
            UpdateButtonStyles(DirectCast(sender, Button))
        End Sub

        Protected Sub btnRecent_Click(ByVal sender As Object, ByVal e As System.EventArgs)
            mvLeaderboard.ActiveViewIndex = 2
            LoadRecentStats()
            UpdateButtonStyles(DirectCast(sender, Button))
        End Sub

        Private Sub UpdateButtonStyles(activeButton As Button)
            btnOverall.CssClass = "tab-button"
            btnAggressive.CssClass = "tab-button"
            btnRecent.CssClass = "tab-button"
            activeButton.CssClass = "tab-button active"
        End Sub

        Private Sub LoadOverallStats()
            Using conn As New SqlConnection(ConfigurationManager.ConnectionStrings("ChessGameConnection").ConnectionString)
                Using cmd As New SqlCommand("sp_ChessGame_GetOverallStats", conn)
                    cmd.CommandType = CommandType.StoredProcedure

                    Dim dt As New DataTable()
                    Using adapter As New SqlDataAdapter(cmd)
                        adapter.Fill(dt)
                    End Using

                    gvOverall.DataSource = dt
                    gvOverall.DataBind()
                End Using
            End Using
        End Sub

        Private Sub LoadAggressiveStats()
            Using conn As New SqlConnection(ConfigurationManager.ConnectionStrings("ChessGameConnection").ConnectionString)
                Using cmd As New SqlCommand("sp_ChessGame_GetAggressiveStats", conn)
                    cmd.CommandType = CommandType.StoredProcedure

                    Dim dt As New DataTable()
                    Using adapter As New SqlDataAdapter(cmd)
                        adapter.Fill(dt)
                    End Using

                    gvAggressive.DataSource = dt
                    gvAggressive.DataBind()
                End Using
            End Using
        End Sub

        Private Sub LoadRecentStats()
            Using conn As New SqlConnection(ConfigurationManager.ConnectionStrings("ChessGameConnection").ConnectionString)
                Using cmd As New SqlCommand("sp_ChessGame_GetRecentStats", conn)
                    cmd.CommandType = CommandType.StoredProcedure

                    Dim dt As New DataTable()
                    Using adapter As New SqlDataAdapter(cmd)
                        adapter.Fill(dt)
                    End Using

                    gvRecent.DataSource = dt
                    gvRecent.DataBind()
                End Using
            End Using
        End Sub

        Public Function GetMedalOrNumber(rank As Integer) As String
            Select Case rank
                Case 1
                    Return "<span class='medal gold'>🥇</span>"
                Case 2
                    Return "<span class='medal silver'>🥈</span>"
                Case 3
                    Return "<span class='medal bronze'>🥉</span>"
                Case Else
                    Return rank.ToString()
            End Select
        End Function
    End Class
End Namespace