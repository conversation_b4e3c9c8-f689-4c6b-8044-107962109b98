class ChessGameLogic {
    // State properties
    constructor() {
        this.board = null;
        this.isPlebsTurn = true;
        this.promotedPawns = new Set();
        this.capturedPlebs = [];
        this.capturedAristoi = [];
        this.currentSuperPawn = null;
        this.pawnStates = new Map(); // normal/radical/super states
    }

    // Board Setup
    initializeBoard()
    resetGame()

    // Core Move Validation
    isValidMove(fromRow, fromCol, toRow, toCol)
    makeMove(fromRow, fromCol, toRow, toCol)
    
    // Piece-Specific Movement Rules
    isPawnMove(fromRow, fromCol, toRow, toCol)
    isKnightMove(fromRow, fromCol, toRow, toCol)
    isBishopMove(fromRow, fromCol, toRow, toCol)
    isRookMove(fromRow, fromCol, toRow, toCol)
    isQueenMove(fromRow, fromCol, toRow, toCol)
    isKingMove(fromRow, fromCol, toRow, toCol)

    // Special Pawn Rules
    canPawnPromote(row, col)
    handlePawnPromotion(row, col, piece)
    isRadicalPawnMove(fromRow, fromCol, toRow, toCol)
    isSuperPawnMove(fromRow, fromCol, toRow, toCol)
    
    // Path Checking
    isPathClear(fromRow, fromCol, toRow, toCol)
    getDiagonalPath(fromRow, fromCol, toRow, toCol)
    getStraightPath(fromRow, fromCol, toRow, toCol)

    // Game State Checks
    isGameOver()
    checkVictory()
    isPlebsPiece(piece)
    isAristoiPiece(piece)
    
    // Board State Helpers
    getPieceAt(row, col)
    isPieceAtPosition(row, col)
    isValidPosition(row, col)
    
    // Pawn State Management
    getPawnState(row, col)
    setPawnState(row, col, state)
    promoteToRadical(row, col)
    promoteToSuper(row, col)

    // Capture Management
    handleCapture(row, col, capturedPiece)
    addToCapturedPieces(piece)

    // State Getters
    getCurrentState()
    getBoardState()
    getCapturedPieces()
    getPromotedPawns()
} 