2025-03-04 01:17:05.186443 - Game[3SVVRX] - Conn[no-connection]: Player 2 join attempt
	{
  "gameIdToJoin": "3SVVRX",
  "userId": 26,
  "hubId": "9367e100-20cc-42e3-aba9-c83bf7026b17"
}
2025-03-04 01:17:05.193543 - Game[3SVVRX] - Conn[no-connection]: joinGame() - STEP 4 - isFirstPlayer = false;
2025-03-04 01:17:05.200652 - Game[3SVVRX] - Conn[no-connection]: joinGame() - STEP 5 - gameHub.server.joinGame(): gameIdToJoin: 3SVVRX userId:26
2025-03-04 01:19:05.209611 - Game[3SVVRX] - Conn[no-connection]: Player 2 join attempt
	{
  "gameIdToJoin": "3SVVRX",
  "userId": 26,
  "hubId": "85018013-9b74-4724-81fd-485e3d3a5c30"
}
2025-03-04 01:19:05.217117 - Game[3SVVRX] - Conn[no-connection]: joinGame() - STEP 4 - isFirstPlayer = false;
2025-03-04 01:19:05.223733 - Game[3SVVRX] - Conn[no-connection]: joinGame() - STEP 5 - gameHub.server.joinGame(): gameIdToJoin: 3SVVRX userId:26
2025-03-04 01:19:31.081236 - Game[3SVVRX] - Conn[no-connection]: Player 2 join failed
	{
  "error": {
    "source": "Exception"
  },
  "gameId": "3SVVRX"
}
2025-03-04 01:19:31.089239 - Game[3SVVRX] - Conn[no-connection]: Log error
	{
  "error": {
    "source": "Exception"
  },
  "message": "Connection started reconnecting before invocation result was received."
}
2025-03-04 04:22:36.092798 - Game[3SVVRX] - Conn[no-connection]: Player 2 join attempt
	{
  "gameIdToJoin": "3SVVRX",
  "userId": 26,
  "hubId": "2e5e60ca-15cb-4d99-abff-d5f6ff8ac5a8"
}
2025-03-04 04:22:36.100026 - Game[3SVVRX] - Conn[no-connection]: joinGame() - STEP 4 - isFirstPlayer = false;
2025-03-04 04:22:36.106532 - Game[3SVVRX] - Conn[no-connection]: joinGame() - STEP 5 - gameHub.server.joinGame(): gameIdToJoin: 3SVVRX userId:26
2025-03-04 04:23:01.018768 - Game[3SVVRX] - Conn[no-connection]: Player 2 join failed
	{
  "error": {
    "source": "Exception"
  },
  "gameId": "3SVVRX"
}
2025-03-04 04:23:01.522856 - Game[3SVVRX] - Conn[no-connection]: Log error
	{
  "error": {
    "source": "Exception"
  },
  "message": "Object reference not set to an instance of an object."
}
2025-03-04 05:04:46.029033 - Game[3SVVRX] - Conn[no-connection]: Player 2 join attempt
	{
  "gameIdToJoin": "3SVVRX",
  "userId": 26,
  "hubId": "aea0079d-e3d3-43cb-ac51-940cd547ccfc"
}
2025-03-04 05:04:46.036540 - Game[3SVVRX] - Conn[no-connection]: joinGame() - STEP 4 - isFirstPlayer = false;
2025-03-04 05:04:46.043993 - Game[3SVVRX] - Conn[no-connection]: joinGame() - STEP 5 - gameHub.server.joinGame(): gameIdToJoin: 3SVVRX userId:26
2025-03-04 05:05:30.260072 - Game[3SVVRX] - Conn[no-connection]: Log error
	{
  "error": {
    "source": "Exception"
  },
  "message": "Connection started reconnecting before invocation result was received."
}
2025-03-04 05:05:30.674734 - Game[3SVVRX] - Conn[no-connection]: Player 2 join failed
	{
  "error": {
    "source": "Exception"
  },
  "gameId": "3SVVRX"
}
