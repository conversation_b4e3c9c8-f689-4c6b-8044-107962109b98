USE [<PERSON>th<PERSON><PERSON><PERSON><PERSON>]
GO
/****** Object:  Table [dbo].[ChessGames]    Script Date: 4/5/2025 1:17:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ChessGames](
	[GameId] [nvarchar](50) NOT NULL,
	[StartTime] [datetime] NOT NULL,
	[EndTime] [datetime] NULL,
	[EndGameAction] [nvarchar](50) NULL,
	[IsSoloPlay] [bit] NOT NULL,
	[FirstPlayerUserId] [int] NOT NULL,
	[SecondPlayerUserId] [int] NULL,
	[EndGameUserID] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[GameId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
ALTER TABLE [dbo].[ChessGames]  WITH NOCHECK ADD  CONSTRAINT [FK_ChessGames_FirstPlayerUser] FOREIGN KEY([FirstPlayerUserId])
REFERENCES [dbo].[ChessUsers] ([UserId])
GO
ALTER TABLE [dbo].[ChessGames] CHECK CONSTRAINT [FK_ChessGames_FirstPlayerUser]
GO
ALTER TABLE [dbo].[ChessGames]  WITH NOCHECK ADD  CONSTRAINT [FK_ChessGames_SecondPlayerUser] FOREIGN KEY([SecondPlayerUserId])
REFERENCES [dbo].[ChessUsers] ([UserId])
GO
ALTER TABLE [dbo].[ChessGames] CHECK CONSTRAINT [FK_ChessGames_SecondPlayerUser]
GO
