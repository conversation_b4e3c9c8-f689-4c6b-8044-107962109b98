body {
    margin: 0;
    padding: 0;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background-image: url('../images/Cover2.jpg');
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    font-family: Arial, sans-serif;
    background-color: rgba(0, 0, 0, 0.95);
}

.auth-container {
    background-color: rgba(255, 255, 255, 0.95);
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
    max-width: 400px;
}

.form-group {
    margin-bottom: 15px;
}

.form-control {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}

.btn-primary {
    width: 100%;
    padding: 10px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.error {
    color: #dc3545;
    font-size: 0.875em;
    margin-top: 5px;
}

.message {
    font-size: small;
    display: block;
    margin-top: 10px;
    text-align: center;
    font-size: 16px;
}
.message.success {
    background-color: #d4edda;
    color: #155724;
}

.message.error {
    background-color: #f8d7da;
    color: #721c24;
}

.auth-links {
    margin-top: 15px;
    text-align: center;
}

.password-container {
    position: relative;
    display: flex;
    align-items: center;
}

.password-container .form-control {
    padding-right: 40px; /* Make room for the button */
}

.password-toggle {
    position: absolute;
    right: 10px;
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.password-toggle:hover {
    color: #333;
}

.password-toggle:focus {
    outline: none;
}

/* Ensure the password field maintains its style */
.password-container .form-control {
    flex: 1;
    margin-right: 0;
} 