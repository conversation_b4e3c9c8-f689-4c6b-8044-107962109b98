SELECT 
    g.GameId,
    g.StartTime,
    g.EndTime,
    g.<PERSON><PERSON><PERSON>A<PERSON>,
    g.<PERSON>GameUserID,
    g.FirstPlayerUserId,
    g.SecondPlayerUserId,
    p.DisplayName,
    p.UserId
FROM dbo.ChessGames g
JOIN dbo.ChessUsers p ON (p.UserId = g.FirstPlayerUserId OR p.UserId = g.SecondPlayerUserId)
WHERE g.StartTime >= DATEADD(day, -30, GETDATE())
    AND g.EndTime IS NOT NULL
    AND p.DisplayName NOT LIKE '%Test%'
ORDER BY g.StartTime DESC;

EXEC sp_ChessGame_CleanupGameData

SELECT * FROM ChessMoves where GameID = (select top 1 GameID From ChessGames order by starttime desc)

SELECT * FROM ChessGames ORDER BY StartTime DESC
SELECT * FROM ChessMoves WHERE GameId = 'M8W64S'

SELECT * FROM ChessGames WHERE GameID NOT IN (Select GameID from ChessM<PERSON>s)
SELECT * FROM ChessGames WHERE isSoloPlay = 1  

SELECT * FROM ChessGames cm
  FULL OUTER JOIN ChessGames cg
    ON cm.GameID = cg.GameID

