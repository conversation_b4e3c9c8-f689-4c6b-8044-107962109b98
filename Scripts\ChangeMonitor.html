<!DOCTYPE html>
<html>
<head>
    <title>Change Monitor</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .version-selector {
            width: 300px;
            margin: 20px 0;
            padding: 5px;
        }
        .file-comparison {
            margin-bottom: 30px;
        }
        .file-path {
            font-weight: bold;
            margin-bottom: 10px;
            padding: 5px;
            background-color: #f0f0f0;
        }
        .comparison-container {
            display: flex;
            gap: 20px;
            margin-top: 10px;
        }
        .file-content {
            width: 48%;
            font-family: "Courier New", monospace;
            font-size: 12px;
            white-space: pre-wrap;
            border: 1px solid #ccc;
            padding: 10px;
            max-height: 500px;
            overflow-y: auto;
        }
        .changed-line {
            color: red;
        }
        hr {
            margin: 20px 0;
            border: none;
            border-top: 1px solid #ccc;
        }
        .version-label {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .project-select {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f0f0f0;
            border-radius: 5px;
        }
        .project-select input[type="file"] {
            margin: 10px 0;
        }
        #mainContent {
            display: none;  /* Initially hidden until project is selected */
        }
        .file-content .unchanged {
            color: #000;
        }
        .file-content .deleted {
            color: #ff0000;
            background-color: #ffe6e6;
        }
        .file-content .added {
            color: #008000;
            background-color: #e6ffe6;
        }
        .file-content .modified {
            color: #0000ff;
            background-color: #e6e6ff;
        }
        .file-content .spacer {
            color: #ccc;
            background-color: #f8f8f8;
        }
        .action-buttons {
            margin-bottom: 15px;
        }
        .snapshot-button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .snapshot-button:hover {
            background-color: #45a049;
        }
        .workflow-instructions {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #4CAF50;
        }
        .workflow-instructions h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .workflow-instructions ol {
            margin: 0;
            padding-left: 20px;
        }
        .workflow-instructions li {
            margin-bottom: 8px;
            line-height: 1.4;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            position: relative;
            background-color: #fefefe;
            margin: 15% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 600px;
            border-radius: 5px;
        }

        .config-group {
            margin-bottom: 15px;
        }

        .config-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .config-group input[type="text"] {
            width: 100%;
            padding: 8px;
            margin-bottom: 5px;
        }

        .config-group textarea {
            width: 100%;
            height: 100px;
            padding: 8px;
        }

        .button-group {
            margin-top: 20px;
            text-align: right;
        }

        .button-group button {
            margin-left: 10px;
            padding: 8px 15px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
        }

        .save-button {
            background-color: #4CAF50;
            color: white;
        }

        .cancel-button {
            background-color: #f44336;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>Change Monitor</h2>

        <div class="project-select">
            <h3>Select Project Folder</h3>
            <p>Please select your project folder from Desktop/ChangeMonitor/Projects/</p>
            <input type="file" id="projectSelect" webkitdirectory directory>
            <div id="selectedPath"></div>
        </div>
        
        <div id="mainContent">
            <div class="action-buttons">
                <button id="createSnapshotBtn" class="snapshot-button">Create & Compare Snapshot</button>
                <button id="configureProjectBtn" class="snapshot-button">Configure Project</button>
            </div>
            <div class="version-control">
                <select class="version-selector" id="versionSelector">
                    <option value="">Select version to compare...</option>
                </select>
                <button id="refreshButton" class="refresh-button">↻ Refresh</button>
            </div>
            <hr>
            <div id="comparisonContainer">
                <!-- Comparison results will be inserted here -->
            </div>
        </div>
    </div>

    <!-- Add Modal Dialog -->
    <div id="configModal" class="modal">
        <div class="modal-content">
            <h3>Project Configuration</h3>
            <div class="config-group">
                <label for="fileTypes">File Types (comma-separated)</label>
                <input type="text" id="fileTypes" placeholder=".vb,.aspx,.config,.css,.master">
                <small>Example: .vb,.aspx,.config,.css,.master</small>
            </div>
            <div class="config-group">
                <label for="excludedFolders">Excluded Folders (one per line)</label>
                <textarea id="excludedFolders" placeholder="bin&#10;obj&#10;packages&#10;node_modules"></textarea>
            </div>
            <div class="config-group">
                <label for="sourcePath">Source Path</label>
                <input type="text" id="sourcePath" placeholder="C:\inetpub\wwwroot">
            </div>
            <div class="button-group">
                <button class="cancel-button" onclick="closeConfigModal()">Cancel</button>
                <button class="save-button" onclick="saveConfiguration()">Save Configuration</button>
            </div>
        </div>
    </div>

    <script>
        let projectPath = '';

        document.getElementById('projectSelect').addEventListener('change', async function(e) {
            const files = e.target.files;
            if (files.length > 0) {
                // Get the project path from the first file
                const path = files[0].webkitRelativePath;
                const pathParts = path.split('/');
                projectPath = pathParts[0]; // This is the project folder name
                
                // Look for CM_ folders in the selected directory
                const cmFolders = new Set();
                for (const file of files) {
                    const parts = file.webkitRelativePath.split('/');
                    if (parts.length > 1 && parts[1].startsWith('CM_')) {
                        cmFolders.add(parts[1]);
                    }
                }
                
                if (cmFolders.size === 0) {
                    alert('No CM_ folders found in the selected directory. Please select a project folder containing CM_XXXXX folders.');
                    document.getElementById('projectSelect').value = '';
                    return;
                }

                document.getElementById('selectedPath').textContent = 'Selected Project: ' + projectPath;
                document.getElementById('mainContent').style.display = 'block';
                
                // Update version selector
                const selector = document.getElementById('versionSelector');
                selector.innerHTML = '<option value="">Select version to compare...</option>';
                
                // Sort folders in reverse order (newest first)
                const versions = Array.from(cmFolders).sort().reverse();
                versions.forEach(version => {
                    const option = document.createElement('option');
                    option.value = version;
                    option.textContent = version;
                    selector.appendChild(option);
                });

                // Select latest version by default
                if (versions.length > 0) {
                    selector.value = versions[0];
                    await compareVersions(versions[0]);
                }
            }
        });

        // Handle version selection change
        document.getElementById('versionSelector').addEventListener('change', async function(e) {
            const selectedVersion = e.target.value;
            if (selectedVersion) {
                await compareVersions(selectedVersion);
            }
        });

        // Compare selected version with previous
        async function compareVersions(currentVersion) {
            const container = document.getElementById('comparisonContainer');
            container.innerHTML = '';

            try {
                const versionNum = parseInt(currentVersion.substring(3));
                const prevVersion = versionNum > 0 ? `CM_${String(versionNum - 1).padStart(5, '0')}` : null;

                // Get all files from the selected version
                const files = document.getElementById('projectSelect').files;
                const versionFiles = new Map();

                // Collect all files in the current CM folder
                for (const file of files) {
                    const pathParts = file.webkitRelativePath.split('/');
                    if (pathParts[1] === currentVersion) {
                        // Remove the project and CM_XXXXX parts to get the relative path
                        const relativePath = pathParts.slice(2).join('/');
                        if (relativePath) {
                            versionFiles.set(relativePath, file);
                        }
                    }
                }

                // Process each file
                for (const [relativePath, file] of versionFiles) {
                    const currentContent = await file.text();
                    let prevContent = '';
                    
                    if (prevVersion) {
                        // Look for the same file in the previous version
                        for (const f of files) {
                            const parts = f.webkitRelativePath.split('/');
                            if (parts[1] === prevVersion && parts.slice(2).join('/') === relativePath) {
                                prevContent = await f.text();
                                break;
                            }
                        }
                    }

                    displayComparison(relativePath, prevContent, currentContent, container);
                }
            } catch (error) {
                console.error('Error comparing versions:', error);
                container.innerHTML = `<div class="error">Error comparing versions: ${error.message}</div>`;
            }
        }

        // Display file comparison
        function displayComparison(filePath, prevContent, currentContent, container) {
            // Calculate diff first
            const diffResult = calculateDiff(prevContent.split('\n'), currentContent.split('\n'));
            
            // Check if there are any changes
            const hasChanges = diffResult.some(change => change.type !== 'unchanged');
            
            // Only proceed with display if there are changes
            if (!hasChanges) {
                return; // Skip files with no changes
            }

            const comparisonDiv = document.createElement('div');
            comparisonDiv.className = 'file-comparison';

            const pathDiv = document.createElement('div');
            pathDiv.className = 'file-path';
            pathDiv.textContent = filePath;

            const contentDiv = document.createElement('div');
            contentDiv.className = 'comparison-container';

            const prevDiv = document.createElement('div');
            prevDiv.className = 'file-content';
            const currentDiv = document.createElement('div');
            currentDiv.className = 'file-content';

            // Display the diff
            diffResult.forEach(change => {
                if (change.type === 'unchanged') {
                    // Add unchanged line to both sides
                    addLine(prevDiv, change.line, 'unchanged');
                    addLine(currentDiv, change.line, 'unchanged');
                } else if (change.type === 'deleted') {
                    // Add deleted line to left side only
                    addLine(prevDiv, change.line, 'deleted');
                    addLine(currentDiv, '', 'spacer');
                } else if (change.type === 'added') {
                    // Add added line to right side only
                    addLine(prevDiv, '', 'spacer');
                    addLine(currentDiv, change.line, 'added');
                } else if (change.type === 'modified') {
                    // Show both versions in blue to indicate modification
                    addLine(prevDiv, change.oldLine, 'modified');
                    addLine(currentDiv, change.newLine, 'modified');
                }
            });

            contentDiv.appendChild(prevDiv);
            contentDiv.appendChild(currentDiv);

            comparisonDiv.appendChild(pathDiv);
            comparisonDiv.appendChild(contentDiv);
            container.appendChild(comparisonDiv);
            container.appendChild(document.createElement('hr'));
        }

        function calculateDiff(oldLines, newLines) {
            const diff = [];
            let i = 0, j = 0;
            
            while (i < oldLines.length || j < newLines.length) {
                if (i >= oldLines.length) {
                    // Rest of new lines are additions
                    diff.push({ type: 'added', line: newLines[j] });
                    j++;
                } else if (j >= newLines.length) {
                    // Rest of old lines are deletions
                    diff.push({ type: 'deleted', line: oldLines[i] });
                    i++;
                } else if (oldLines[i] === newLines[j]) {
                    // Lines are the same
                    diff.push({ type: 'unchanged', line: oldLines[i] });
                    i++;
                    j++;
                } else {
                    // Lines are different - treat as modification
                    diff.push({
                        type: 'modified',
                        oldLine: oldLines[i],
                        newLine: newLines[j]
                    });
                    i++;
                    j++;
                }
            }
            
            return diff;
        }

        function addLine(container, content, type) {
            const line = document.createElement('div');
            line.textContent = content || ' ';
            line.className = type;
            container.appendChild(line);
        }

        // Add this function to check for File System Access API support
        function supportsFileSystemAccess() {
            return 'showDirectoryPicker' in window;
        }

        // Update the refresh button handler
        document.getElementById('refreshButton').addEventListener('click', async function() {
            if (supportsFileSystemAccess()) {
                try {
                    // Modern approach using File System Access API
                    const dirHandle = await window.showDirectoryPicker({
                        mode: 'read'
                    });
                    
                    // Get all entries in the directory
                    const cmFolders = new Set();
                    for await (const entry of dirHandle.values()) {
                        if (entry.kind === 'directory' && entry.name.startsWith('CM_')) {
                            cmFolders.add(entry.name);
                        }
                    }

                    // Update version selector
                    const selector = document.getElementById('versionSelector');
                    const currentSelection = selector.value;
                    selector.innerHTML = '<option value="">Select version to compare...</option>';
                    
                    // Sort folders in reverse order (newest first)
                    const versions = Array.from(cmFolders).sort().reverse();
                    versions.forEach(version => {
                        const option = document.createElement('option');
                        option.value = version;
                        option.textContent = version;
                        selector.appendChild(option);
                    });

                    // If there's a new version, select it and show comparison
                    if (versions.length > 0) {
                        if (versions[0] !== currentSelection) {
                            selector.value = versions[0];
                            await compareVersions(versions[0]);
                        } else {
                            selector.value = currentSelection;
                        }
                    }

                } catch (error) {
                    console.error('Error accessing filesystem:', error);
                    fallbackRefresh();
                }
            } else {
                fallbackRefresh();
            }
        });

        // Fallback refresh method
        function fallbackRefresh() {
            const projectSelect = document.getElementById('projectSelect');
            
            // Clear current selection
            projectSelect.value = '';
            
            // Create and dispatch a click event
            const clickEvent = new MouseEvent('click', {
                view: window,
                bubbles: true,
                cancelable: true
            });
            projectSelect.dispatchEvent(clickEvent);
        }

        // Add the combined action handler
        document.getElementById('createSnapshotBtn').addEventListener('click', async function() {
            alert('Please run CreateSnapshot.bat in the Scripts folder, then click OK to refresh the comparison.');
            
            // After user confirms they've run the batch file, refresh automatically
            if (supportsFileSystemAccess()) {
                try {
                    const dirHandle = await window.showDirectoryPicker({
                        mode: 'read'
                    });
                    
                    // Get all entries in the directory
                    const cmFolders = new Set();
                    for await (const entry of dirHandle.values()) {
                        if (entry.kind === 'directory' && entry.name.startsWith('CM_')) {
                            cmFolders.add(entry.name);
                        }
                    }

                    // Update version selector
                    const selector = document.getElementById('versionSelector');
                    const currentSelection = selector.value;
                    selector.innerHTML = '<option value="">Select version to compare...</option>';
                    
                    // Sort folders in reverse order (newest first)
                    const versions = Array.from(cmFolders).sort().reverse();
                    versions.forEach(version => {
                        const option = document.createElement('option');
                        option.value = version;
                        option.textContent = version;
                        selector.appendChild(option);
                    });

                    // If there's a new version, select it and show comparison
                    if (versions.length > 0) {
                        if (versions[0] !== currentSelection) {
                            selector.value = versions[0];
                            await compareVersions(versions[0]);
                        } else {
                            selector.value = currentSelection;
                        }
                    }

                } catch (error) {
                    console.error('Error accessing filesystem:', error);
                    fallbackRefresh();
                }
            } else {
                fallbackRefresh();
            }
        });

        // Configuration management
        let projectConfig = {
            fileTypes: ['.vb', '.aspx', '.config', '.css', '.master'],
            excludedFolders: ['bin', 'obj', 'packages', 'node_modules'],
            sourcePath: 'C:\\inetpub\\wwwroot'
        };

        // Load configuration from localStorage if available
        function loadConfiguration() {
            const savedConfig = localStorage.getItem(`projectConfig_${projectPath}`);
            if (savedConfig) {
                projectConfig = JSON.parse(savedConfig);
                updateConfigurationFields();
            }
        }

        // Update modal fields with current configuration
        function updateConfigurationFields() {
            document.getElementById('fileTypes').value = projectConfig.fileTypes.join(',');
            document.getElementById('excludedFolders').value = projectConfig.excludedFolders.join('\n');
            document.getElementById('sourcePath').value = projectConfig.sourcePath;
        }

        // Save configuration
        function saveConfiguration() {
            projectConfig.fileTypes = document.getElementById('fileTypes').value
                .split(',')
                .map(t => t.trim())
                .filter(t => t);
            
            projectConfig.excludedFolders = document.getElementById('excludedFolders').value
                .split('\n')
                .map(f => f.trim())
                .filter(f => f);
            
            projectConfig.sourcePath = document.getElementById('sourcePath').value.trim();

            // Save to localStorage
            localStorage.setItem(`projectConfig_${projectPath}`, JSON.stringify(projectConfig));
            
            closeConfigModal();
        }

        // Modal management
        document.getElementById('configureProjectBtn').addEventListener('click', function() {
            document.getElementById('configModal').style.display = 'block';
            updateConfigurationFields();
        });

        function closeConfigModal() {
            document.getElementById('configModal').style.display = 'none';
        }

        // Close modal if clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('configModal');
            if (event.target === modal) {
                closeConfigModal();
            }
        }

        // Update project selection handler to load configuration
        document.getElementById('projectSelect').addEventListener('change', async function(e) {
            // ... existing project select code ...
            
            // Add this after setting projectPath
            if (projectPath) {
                loadConfiguration();
            }
        });
    </script>
</body>
</html> 