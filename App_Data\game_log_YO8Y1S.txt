2025-04-05 17:54:13.086333 - Game[YO8Y1S] - Conn[no-connection]: updateTurnMessage - Complete
2025-04-05 17:54:17.724288 - Game[YO8Y1S] - Conn[no-connection]: Player 2 join attempt
	{
  "gameIdToJoin": "YO8Y1S",
  "userId": 1,
  "hubId": "f8767c5d-ecb9-4520-812f-3a004b9b479f"
}
2025-04-05 17:54:17.730289 - Game[YO8Y1S] - Conn[no-connection]: joinGame() - STEP 4 - isFirstPlayer = false;
2025-04-05 17:54:17.736290 - Game[YO8Y1S] - Conn[no-connection]: joinGame() - STEP 5 - gameHub.server.joinGame(): gameIdToJoin: YO8Y1S userId:1
2025-04-05 17:54:17.864049 - Game[YO8Y1S] - Conn[no-connection]: Player 2 joined successfully
2025-04-05 17:54:17.866050 - Game[YO8Y1S] - Conn[no-connection]: updateTurnMessage - Complete
2025-04-05 17:54:17.870646 - Game[YO8Y1S] - Conn[no-connection]: updateTurnMessage - Complete
2025-04-05 17:54:18.374173 - Game[YO8Y1S] - Conn[no-connection]: Prison displays updated
2025-04-05 17:54:18.889634 - Game[YO8Y1S] - Conn[no-connection]: updateTurnMessage - Complete
