2025-04-05 17:24:02.291006 - Game[5I3WHB] - Conn[no-connection]: updateTurnMessage - Complete
2025-04-05 17:24:06.184407 - Game[5I3WHB] - Conn[no-connection]: Player 2 join attempt
	{
  "gameIdToJoin": "5I3WHB",
  "userId": 1,
  "hubId": "b4e4df7b-6ca7-4ff2-b2f8-a2f36c0f8394"
}
2025-04-05 17:24:06.189911 - Game[5I3WHB] - Conn[no-connection]: joinGame() - STEP 4 - isFirstPlayer = false;
2025-04-05 17:24:06.198009 - Game[5I3WHB] - Conn[no-connection]: joinGame() - STEP 5 - gameHub.server.joinGame(): gameIdToJoin: 5I3WHB userId:1
2025-04-05 17:24:06.361278 - Game[5I3WHB] - Conn[no-connection]: updateTurnMessage - Complete
2025-04-05 17:24:06.364280 - Game[5I3WHB] - Conn[no-connection]: updateTurnMessage - Complete
2025-04-05 17:24:06.815107 - Game[5I3WHB] - Conn[no-connection]: Prison displays updated
2025-04-05 17:24:07.329050 - Game[5I3WHB] - Conn[no-connection]: Player 2 joined successfully
2025-04-05 17:24:07.337053 - Game[5I3WHB] - Conn[no-connection]: updateTurnMessage - Complete
