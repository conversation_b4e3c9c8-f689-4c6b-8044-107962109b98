USE [<PERSON>th<PERSON><PERSON><PERSON><PERSON>]
GO
/****** Object:  StoredProcedure [dbo].[sp_ChessGame_GetHistory]    Script Date: 4/5/2025 1:17:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- Create stored procedure for getting game history

CREATE PROCEDURE [dbo].[sp_ChessGame_GetHistory]
    @Limit int = 10
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT TOP (@Limit)
        g.GameId,
        g.StartTime,
        g.EndTime,
        g.EndGameAction ,
        g.IsSoloPlay,
        COUNT(m.MoveId) as TotalMoves
    FROM ChessGames g
    LEFT JOIN ChessMoves m ON g.GameId = m.GameId
    GROUP BY 
        g.GameId,
        g.StartTime,
        g.EndTime,
        g.EndGameAction,
        g.IsSoloPlay
    ORDER BY g.StartTime DESC
END
GO
