Option Strict On
Option Explicit On

Imports System.Threading.Tasks

Namespace PvAChess.Common.Hubs
    Public Interface IGameHub
        Inherits ISignalRCoreHub

        Function JoinGame(gameId As String, userId As Integer, isSoloPlay As Boolean) As Task(Of Boolean)
        Function MakeMove(gameId As String, moveData As Object, moveType As String) As Task
        Function StartNewGame(gameId As String) As Task
        Function AnnounceVictory(gameId As String, endGameAction As String, endGameUserId As Integer) As Task
        Function SendChatMessage(gameId As String, message As String) As Task
        Function UpdateFirstPlayerSelection(gameId As String, isPlebs As Boolean) As Task
        Function LogGameMove(moveLogData As Object) As Task
        Function CheckGameExists(gameId As String) As Task(Of Boolean)
    End Interface
End Namespace