Imports System.Security.Cryptography
Imports System.Text
Namespace PvAChess
    Public Class CommonCode
        Public Function HashPassword(password As String) As String
            Using sha512 As SHA512 = SHA512.Create()
                Dim passwordBytes As Byte() = Encoding.UTF8.GetBytes(password)
                Dim hashBytes As Byte() = sha512.ComputeHash(passwordBytes)
                Return BitConverter.ToString(hashBytes).Replace("-", "")
            End Using
        End Function
    End Class

End Namespace
