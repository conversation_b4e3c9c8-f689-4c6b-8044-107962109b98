<!DOCTYPE html>
<html>
<head>
    <title>CSS Sorter</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        .instructions {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f0f0f0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="instructions">
            <h2>CSS Sorter</h2>
            <p>1. Click "Choose File" to select your CSS file</p>
            <p>2. The sorted file will automatically download as 'OriginalFileName_new.css'</p>
        </div>
        <input type="file" id="cssFile" accept=".css">
    </div>

    <script>
    document.getElementById('cssFile').addEventListener('change', async function(e) {
    const file = e.target.files[0];
    const text = await file.text();
    
    // Get original filename and create new filename
    const originalName = file.name;
    const newFileName = originalName.replace('.css', '_new.css');

    // First, extract and preserve comments
    const comments = [];
    let cssTextWithoutComments = text.replace(/\/\*[\s\S]*?\*\//g, match => {
        comments.push(match);
        return '';
    });

    // Extract and preserve @media queries and @keyframes
    const specialBlocks = [];
    const specialBlockRegex = /@(?:media|keyframes)[^{]+\{(?:[^{}]*\{[^{}]*\})*[^{}]*\}/g;
    cssTextWithoutComments = cssTextWithoutComments.replace(specialBlockRegex, match => {
        specialBlocks.push(match);
        return '';
    });

    // Process remaining CSS rules
    const cssRules = {};
    const ruleRegex = /([^{]+){([^}]+)}/g;
    let match;
    let priority = 0;

    while ((match = ruleRegex.exec(cssTextWithoutComments)) !== null) {
        const selector = match[1].trim();
        const properties = match[2].trim();
        
        if (!cssRules[selector]) {
            cssRules[selector] = {};
        }

        properties.split(';').forEach(prop => {
            const [key, value] = prop.split(':').map(s => s.trim());
            if (key && value) {
                if (!cssRules[selector][key] || cssRules[selector][key].priority < priority) {
                    cssRules[selector][key] = {
                        value: value,
                        priority: priority
                    };
                }
            }
        });
        priority++;
    }

    // Build consolidated CSS
    let consolidatedCSS = '/* Consolidated CSS File */\n\n';
    
    // Add root variables first if they exist
    if (cssRules[':root']) {
        consolidatedCSS += ':root {\n';
        const props = cssRules[':root'];
        const sortedProps = Object.keys(props).sort();
        sortedProps.forEach(prop => {
            consolidatedCSS += `    ${prop}: ${props[prop].value};\n`;
        });
        consolidatedCSS += '}\n\n';
        delete cssRules[':root'];
    }

    // Add preserved special blocks (media queries and keyframes)
    specialBlocks.forEach(block => {
        consolidatedCSS += block + '\n\n';
    });

    // Add remaining regular CSS rules
    const sortedSelectors = Object.keys(cssRules).sort();
    sortedSelectors.forEach(selector => {
        const props = cssRules[selector];
        const sortedProps = Object.keys(props).sort();
        
        if (sortedProps.length > 0) {
            consolidatedCSS += `${selector} {\n`;
            sortedProps.forEach(prop => {
                consolidatedCSS += `    ${prop}: ${props[prop].value};\n`;
            });
            consolidatedCSS += '}\n\n';
        }
    });

    // Create download link
    const blob = new Blob([consolidatedCSS], { type: 'text/css' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = newFileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
});
    </script>
</body>
</html>