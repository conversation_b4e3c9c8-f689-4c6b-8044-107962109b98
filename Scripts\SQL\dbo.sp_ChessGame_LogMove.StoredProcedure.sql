USE [Elth<PERSON><PERSON>hes<PERSON>]
GO
/****** Object:  StoredProcedure [dbo].[sp_ChessGame_LogMove]    Script Date: 4/5/2025 1:17:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[sp_ChessGame_LogMove]
    @GameId nvarchar(50),
    @MoveNumber int,
    @PlayerRole nvarchar(10),
    @FromRow int,
    @FromCol int,
    @ToRow int,
    @ToCol int,
    @PieceMoved nvarchar(20),
    @PieceCaptured nvarchar(20),
    @WasPromotion bit,
    @UserId int
AS
BEGIN
    SET NOCOUNT ON;
    
    INSERT INTO ChessMoves (
        GameId, MoveNumber, PlayerRole, FromRow, FromCol, ToRow, ToCol, 
        PieceMoved, PieceCaptured, WasPromotion, Timestamp, UserId)
    VALUES (
        @GameId, @MoveNumber, @PlayerRole, @FromRow, @FromCol, @ToRow, @ToCol, 
        @PieceMoved, @PieceCaptured, @Was<PERSON>romotion, GETUTCDATE(), @UserId)
END
GO
