﻿Imports System.Data
Imports System.Data.SqlClient
Imports System.Configuration
Imports System.Web.Security
Imports System.IO
Imports System.Web.Services
Imports System.Web.Script.Services

<System.Web.Script.Services.ScriptService>
Public Class _Default
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs)
        ' Add debug logging
        System.Diagnostics.Debug.WriteLine("Default.aspx Page_Load - Current Path: " & Request.Path)
        System.Diagnostics.Debug.WriteLine("Default.aspx Page_Load - Session UserId: " & If(Session("UserId") Is Nothing, "Nothing", Session("UserId").ToString()))
        System.Diagnostics.Debug.WriteLine("Default.aspx Page_Load - User.Identity.IsAuthenticated: " & User.Identity.IsAuthenticated)

        If User.Identity.IsAuthenticated Then

            If User IsNot Nothing AndAlso User.Identity IsNot Nothing AndAlso Not String.IsNullOrEmpty(User.Identity.Name) Then
                lblUserDisplay.Text = String.Format("Welcome, {0}", User.Identity.Name)
            Else
                lblUserDisplay.Text = "Welcome, Guest"
            End If

            If Session("UserId") Is Nothing Then
                ' Try to recover UserId from the authenticated user
                Dim username As String = User.Identity.Name
                System.Diagnostics.Debug.WriteLine("Default.aspx - Attempting to recover UserId for user: " & username)

                Try
                    Using conn As New SqlConnection(ConfigurationManager.ConnectionStrings("ChessGameConnection").ConnectionString)
                        conn.Open()
                        Using cmd As New SqlCommand("SELECT UserId FROM ChessUsers WHERE Username = @Username", conn)
                            cmd.Parameters.AddWithValue("@Username", username)
                            Dim result = cmd.ExecuteScalar()
                            If result IsNot Nothing Then
                                Session("UserId") = Convert.ToInt32(result)
                                System.Diagnostics.Debug.WriteLine("Default.aspx - Recovered UserId: " & Session("UserId"))
                            Else
                                System.Diagnostics.Debug.WriteLine("Default.aspx - Could not recover UserId, redirecting to Login")
                                FormsAuthentication.SignOut()
                                Response.Redirect("~/Login.aspx", False)
                                Context.ApplicationInstance.CompleteRequest()
                                Return
                            End If
                        End Using
                    End Using
                Catch ex As Exception
                    System.Diagnostics.Debug.WriteLine("Default.aspx - Error recovering UserId: " & ex.Message)
                    FormsAuthentication.SignOut()
                    Response.Redirect("~/Login.aspx", False)
                    Context.ApplicationInstance.CompleteRequest()
                    Return
                End Try
            End If

            ' Only register the script if we have a valid UserId
            If Session("UserId") IsNot Nothing Then
                Dim script As String = "var userId = " & Session("UserId").ToString() & ";"
                ClientScript.RegisterStartupScript(Me.GetType(), "UserId", script, True)
            End If
        Else
            System.Diagnostics.Debug.WriteLine("Default.aspx - User not authenticated, redirecting to Login.aspx")
            Response.Redirect("~/Login.aspx", False)
            Context.ApplicationInstance.CompleteRequest()
            Return
        End If
    End Sub


    <WebMethod>
    <Script.Services.ScriptMethod(ResponseFormat:=Script.Services.ResponseFormat.Json)>
    Public Shared Function GetCurrentGameId() As String
        System.Diagnostics.Debug.WriteLine("GetCurrentGameId() called")
        If HttpContext.Current.Session IsNot Nothing Then
            Dim gameId As Object = HttpContext.Current.Session("CurrentGameId")
            System.Diagnostics.Debug.WriteLine("Session GameId: " & If(gameId Is Nothing, "Nothing", gameId.ToString()))
            If gameId IsNot Nothing Then
                Return gameId.ToString()
            End If
        Else
            System.Diagnostics.Debug.WriteLine("Session is Nothing")
        End If
        System.Diagnostics.Debug.WriteLine("Returning Nothing")
        Return Nothing
    End Function

    <WebMethod>
    <Script.Services.ScriptMethod(ResponseFormat:=Script.Services.ResponseFormat.Json)>
    Public Shared Sub StoreGameId(gameId As String)
        System.Diagnostics.Debug.WriteLine("Default.aspx - StoreGameId(gameId As String): " & gameId)
        If HttpContext.Current.Session IsNot Nothing Then
            HttpContext.Current.Session("CurrentGameId") = gameId
            System.Diagnostics.Debug.WriteLine("Stored GameId in session: " & gameId)
        End If
    End Sub

    Private Sub btnLeaderBoard_Click(sender As Object, e As EventArgs) Handles btnLeaderBoard.Click
        Response.Redirect("LeaderBoard.aspx", False)
    End Sub

    Private Sub btnLogout_Click(sender As Object, e As EventArgs) Handles btnLogout.Click
        Try
            LogToFile("Starting logout process", New With {
                .sessionId = Session.SessionID,
                .isAuthenticated = User.Identity.IsAuthenticated,
                .userId = If(Session("UserId") IsNot Nothing, Session("UserId").ToString(), "None")
            })

            FormsAuthentication.SignOut()
            LogToFile("Forms authentication signed out")

            Session.Clear()
            LogToFile("Session cleared")

            Session.Abandon()
            LogToFile("Session abandoned")

            LogToFile("About to redirect to Login.aspx")
            Response.Redirect("~/Login.aspx", False)
            Context.ApplicationInstance.CompleteRequest()
        Catch ex As IOException
            LogToFile("IOException during logout", New With {
                .error = ex.Message,
                .stackTrace = ex.StackTrace
            })
            Throw
        Catch ex As Exception
            LogToFile("Error in logout", New With {
                .error = ex.Message,
                .stackTrace = ex.StackTrace
            })
            Throw
        End Try
    End Sub

    Private Sub btnChessGameReplay_Click(sender As Object, e As EventArgs) Handles btnChessGameReplay.Click
        Response.Redirect("ChessGameReplay.aspx", False)
    End Sub
End Class