USE [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]
GO
/****** Object:  StoredProcedure [dbo].[sp_ChessGame_GetMoves]    Script Date: 4/5/2025 1:17:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE   PROCEDURE [dbo].[sp_ChessGame_GetMoves]
    @GameId NVARCHAR(50)
AS
BEGIN
    SET NOCOUNT ON;

/*
-----------------------------------------
USAGE:

   EXEC sp_ChessGame_GetMoves '8CBB3W'
-----------------------------------------
SUPPORT QUERIES
----------------

   SELECT * FROM ChessGames
-----------------------------------------

*/

    -- Select moves with user information
    SELECT 
        g.GameId,
        g.<PERSON>,
        m.<PERSON>,
        u.DisplayName AS PlayerName,
        m.<PERSON>,
        m.<PERSON>,
        m.FromCol,
        m.ToRow,
        m.ToCol,
        m.PieceMoved,
        m.PieceCaptured,
        m.WasPromotion,
        m.Timestamp
    FROM ChessMoves m
    INNER JOIN ChessGames g ON m.GameId = g.GameId
    INNER JOIN ChessUsers u ON m.UserId = u.UserId
    WHERE 
        @GameId = '0' -- Show all games if GameId = 0
        OR m.GameId = @GameId
    ORDER BY 
        g.StartTime ASC,
        m.GameId ASC,
        m.MoveNumber ASC;
END
GO
