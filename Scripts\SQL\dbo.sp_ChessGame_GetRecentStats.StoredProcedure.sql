USE [Elth<PERSON><PERSON><PERSON><PERSON>]
GO
/****** Object:  StoredProcedure [dbo].[sp_ChessGame_GetRecentStats]    Script Date: 4/5/2025 1:17:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [dbo].[sp_ChessGame_GetRecentStats]
AS
BEGIN
    SET NOCOUNT ON;

    SELECT 
        p.DisplayName,
        COUNT(DISTINCT g.GameId) as RecentGames,
        COUNT(DISTINCT CASE 
            WHEN g.EndGameAction LIKE '%Victory!' AND g.EndGameUserID = p.UserId THEN g.GameId
        END) as RecentWins,
        CAST(CAST(COUNT(DISTINCT CASE 
            WHEN g.EndGameAction LIKE '%Victory!' AND g.EndGameUserID = p.UserId THEN g.GameId
        END) AS FLOAT) / 
            NULLIF(CAST(COUNT(DISTINCT g.GameId) AS FLOAT), 0) * 100 AS DECIMAL(5,2)) as WinPercentage
    FROM dbo.ChessUsers p
    JOIN dbo.ChessGames g ON (p.UserId = g.FirstPlayerUserId OR p.UserId = g.SecondPlayerUserId)
    WHERE g.StartTime >= DATEADD(day, -30, GETDATE())
        AND g.EndTime IS NOT NULL
        AND g.EndGameAction != 'Abandoned'
        AND g.IsSoloPlay = 0  -- Only include multiplayer games
        AND p.DisplayName NOT LIKE '%Test%'
    GROUP BY p.DisplayName, p.UserId
    HAVING COUNT(DISTINCT g.GameId) > 0
    ORDER BY RecentGames DESC, RecentWins DESC;
END
GO
