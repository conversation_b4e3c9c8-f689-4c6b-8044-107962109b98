USE [ElthosChess]
GO
/****** Object:  StoredProcedure [dbo].[sp_ChessGame_GetGameList]    Script Date: 4/5/2025 1:17:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
 CREATE PROCEDURE [dbo].[sp_ChessGame_GetGameList]
    @StartDate datetime,
    @EndDate datetime,
    @PlayerName nvarchar(100) = NULL,
    @SoloGames bit = 0
AS

/*
=========================================================================
USAGE:

  EXEC sp_ChessGame_GetGameList '1/1/2025', '1/30/2025', '', 0  -- For multiplayer games
  EXEC sp_ChessGame_GetGameList '1/1/2024', '1/30/2026', '', 1  -- For solo games

=========================================================================
select * from ChessGames 
*/

BEGIN
    SET NOCOUNT ON;
 
 
    SELECT DISTINCT 
        g.gameId,
        g.StartTime,
        p1.DisplayName as Plebs,
        CASE 
            WHEN g.IsSoloPlay = 1 THEN 'Solo'
            ELSE p2.DisplayName 
        END as Aristoi,
        g.EndGameAction
    FROM ChessGames g
    INNER JOIN ChessUsers p1 ON g.FirstPlayerUserId = p1.UserID
    LEFT JOIN ChessUsers p2 ON g.SecondPlayerUserId = p2.UserID
    WHERE 
        g.StartTime BETWEEN @StartDate AND @EndDate
        AND (@PlayerName IS NULL 
             OR @PlayerName = '' 
             OR p1.DisplayName = @PlayerName 
             OR (@SoloGames = 0 AND p2.DisplayName = @PlayerName))
        AND (g.EndGameAction LIKE '%Victory!%'  -- Match Victory! with exclamation mark
		 OR g.EndGameAction LIKE '%Draw%'
		 OR g.EndGameAction LIKE '%Resign%'
		 )      -- Include Draws
        AND g.IsSoloPlay = @SoloGames           -- Use IsSoloPlay column directly
    ORDER BY g.StartTime DESC

END

GO
