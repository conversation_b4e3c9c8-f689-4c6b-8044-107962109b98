Option Strict On
Option Explicit On

Imports Microsoft.AspNet.SignalR
Imports Microsoft.AspNet.SignalR.Hubs
Imports System.Web.Routing
Imports Owin

Namespace PvAChess.Common.Services
    Public Class SignalRService
        Implements ISignalRService

        Public Function GetClients(Of T As Hub)() As IHubConnectionContext(Of Object) _
            Implements ISignalRService.GetClients
            Return GlobalHost.ConnectionManager.GetHubContext(Of T)().Clients
        End Function

        Public Sub RegisterHub(Of T As Hub)(route As String) _
            Implements ISignalRService.RegisterHub
            ' Note: This method is now a no-op as hub registration should be done in Startup.vb
            ' See http://go.microsoft.com/fwlink/?LinkId=320578
        End Sub

        Public Function GetConnectionInfo(connectionId As String) As PvAChess.Common.Models.ConnectionInfo _
            Implements ISignalRService.GetConnectionInfo
            If String.IsNullOrEmpty(connectionId) Then
                Return Nothing
            End If

            ' Try to get connection info from SignalRCoreHub's static Connections dictionary
            Dim connInfo As PvAChess.Common.Models.ConnectionInfo = Nothing
            ' Access the Connections dictionary through the base hub type
            If PvAChess.Common.Hubs.SignalRCoreHub(Of PvAChess.Hubs.IGameClient).Connections.TryGetValue(connectionId, connInfo) Then
                Return connInfo
            End If

            Return Nothing
        End Function
    End Class
End Namespace