﻿<%@ Page Language="VB" AutoEventWireup="true" CodeFile="Register.aspx.vb" Inherits="Register" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">


<head runat="server">
    <title>Chess Game - Register</title>
    <link href="Styles/auth.css" rel="stylesheet" />
    <link href="~/Styles/auth.css" rel="stylesheet" type="text/css" runat="server" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
</head>
<body>
    <form id="form1" runat="server" defaultbutton="btnRegister">
        <div class="auth-container">   
            <h2>Create Account</h2>
            
            <div class="form-group">
                <asp:Label runat="server" AssociatedControlID="txtUsername">Username:</asp:Label>
                <asp:TextBox ID="txtUsername" runat="server" CssClass="form-control"></asp:TextBox>
                <asp:RequiredFieldValidator runat="server" ControlToValidate="txtUsername" 
                    ErrorMessage="Username is required" CssClass="error" Display="Dynamic" 
                    ValidationGroup="RegisterGroup" />
            </div>

            <div class="form-group">
                <asp:Label runat="server" AssociatedControlID="txtEmail">Email:</asp:Label>
                <asp:TextBox ID="txtEmail" runat="server" TextMode="Email" CssClass="form-control"></asp:TextBox>
                <asp:RequiredFieldValidator runat="server" ControlToValidate="txtEmail" 
                    ErrorMessage="Email is required" CssClass="error" Display="Dynamic" 
                    ValidationGroup="RegisterGroup" />
                <asp:RegularExpressionValidator runat="server" ControlToValidate="txtEmail"
                    ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*"
                    ErrorMessage="Invalid email format" CssClass="error" Display="Dynamic" 
                    ValidationGroup="RegisterGroup" />
            </div>

            <div class="form-group">
                <asp:Label runat="server" AssociatedControlID="txtDisplayName">Display Name (Optional):</asp:Label>
                <asp:TextBox ID="txtDisplayName" runat="server" CssClass="form-control"></asp:TextBox>
            </div>

            <div class="form-group">
                <asp:Label runat="server" AssociatedControlID="txtPassword">Password:</asp:Label>
                <div class="password-container">
                    <asp:TextBox ID="txtPassword" runat="server" TextMode="Password" CssClass="form-control"></asp:TextBox>
                    <button type="button" class="password-toggle" onclick="togglePassword('txtPassword', this); return false;">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
                <asp:RequiredFieldValidator runat="server" ControlToValidate="txtPassword" 
                    ErrorMessage="Password is required" CssClass="error" Display="Dynamic" />
            </div>

            <div class="form-group">
                <asp:Label runat="server" AssociatedControlID="txtConfirmPassword">Confirm Password:</asp:Label>
                <div class="password-container">
                    <asp:TextBox ID="txtConfirmPassword" runat="server" TextMode="Password" CssClass="form-control"></asp:TextBox>
                    <button type="button" class="password-toggle" onclick="togglePassword('txtConfirmPassword', this); return false;">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
                <asp:CompareValidator runat="server" ControlToCompare="txtPassword" 
                    ControlToValidate="txtConfirmPassword" ErrorMessage="Passwords do not match" 
                    CssClass="error" Display="Dynamic" />
            </div>

            <asp:Button ID="btnRegister" runat="server" Text="Register" 
                OnClick="btnRegister_Click" CssClass="btn-primary" />
            
            <asp:Label ID="lblMessage" runat="server" CssClass="message"></asp:Label>
            
            <div class="auth-links">
                Already have an account? <a href="Login.aspx">Login here</a>
            </div>
        </div>
    </form>

    <script type="text/javascript">
        function togglePassword(controlId, button) {
            var passwordInput = document.getElementById(controlId);
            var icon = button.querySelector('i');
            
            if (passwordInput.type === "password") {
                passwordInput.type = "text";
                icon.classList.remove("fa-eye");
                icon.classList.add("fa-eye-slash");
            } else {
                passwordInput.type = "password";
                icon.classList.remove("fa-eye-slash");
                icon.classList.add("fa-eye");
            }
        }
    </script>
</body>
</html>
