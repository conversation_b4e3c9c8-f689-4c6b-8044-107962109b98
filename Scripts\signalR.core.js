class SignalRCoreClient {
    constructor(hubName) {
        this.connection = $.hubConnection();
        this.proxy = this.connection.createHubProxy(hubName);
        this.handlers = new Map();
    }

    connect() {
        return this.connection.start()
            .done(() => this.log('Connected'))
            .fail(err => this.error('Connection failed:', err));
    }

    registerHandler(eventName, handler) {
        this.proxy.on(eventName, handler);
        this.handlers.set(eventName, handler);
    }

    invoke(methodName, ...args) {
        return this.proxy.invoke(methodName, ...args)
            .fail(err => this.error(`Invoke ${methodName} failed:`, err));
    }

    log(...args) { /* Unified logging */ }
    error(...args) { /* Error handling */ }
} 