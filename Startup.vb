Option Strict Off
Option Explicit On

Imports Microsoft.Owin
Imports Owin
Imports Microsoft.AspNet.SignalR

' Add friendly name to match web.config
<Assembly: OwinStartupAttribute("PvAChess.Startup", GetType(PvAChess.Startup))>

Namespace PvAChess
    Public Class Startup
        Public Sub Configuration(app As IAppBuilder)
            Try
                ' Create hub configuration
                Dim hubConfiguration = New HubConfiguration With {
                    .EnableDetailedErrors = True,
                    .EnableJavaScriptProxies = True
                }

                ' Configure SignalR
                GlobalHost.Configuration.DefaultMessageBufferSize = 32

                ' Register hub
                GlobalHost.DependencyResolver.Register(GetType(Hubs.GameHub), Function() New Hubs.GameHub())

                ' Map SignalR with configuration
                app.MapSignalR(hubConfiguration)

                ' Log success
                System.Diagnostics.Debug.WriteLine("SignalR Configured Successfully")

            Catch ex As Exception
                System.Diagnostics.Debug.WriteLine("SignalR Configuration Error: " & ex.ToString())
                Throw
            End Try
        End Sub
    End Class
End Namespace