﻿Imports System.Data.SqlClient
Imports System.Web.UI.WebControls
Imports System.Diagnostics
Imports System.Web.Security

Partial Public Class Register
    Inherits System.Web.UI.Page

    Private _isProcessing As Boolean = False

    Protected Sub Page_Init(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Init
        Debug.WriteLine("Page_Init called - IsPostBack: " & IsPostBack.ToString())
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Debug.WriteLine("Page_Load called - IsPostBack: " & IsPostBack.ToString())
        Debug.WriteLine("Form submission type: " & Request.Form("__EVENTTARGET"))
        Debug.WriteLine("Form submission argument: " & Request.Form("__EVENTARGUMENT"))

        ' Add this line to see all form variables
        For Each key As String In Request.Form.AllKeys
            Debug.WriteLine("Form key: " & key & " = " & Request.Form(key))
        Next

        ' Prevent direct URL access if already authenticated
        If User.Identity.IsAuthenticated Then
            Response.Redirect("~/Default.aspx")
        End If
    End Sub

    Protected Sub Page_PreRender(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.PreRender
        Debug.WriteLine("Page_PreRender called - IsPostBack: " & IsPostBack.ToString())
    End Sub

    Protected Sub btnRegister_Click(sender As Object, e As EventArgs)
        Debug.WriteLine("Register button clicked - IsPostBack: " & IsPostBack.ToString())
        Debug.WriteLine("Register button clicked - IsValid: " & Page.IsValid.ToString())


        Try
            If Page.IsValid Then
                Dim common As New PvAChess.CommonCode()

                Dim hashedPassword As String = common.HashPassword(txtPassword.Text)

                If ChessUserService.CreateUser(txtUsername.Text, txtEmail.Text, hashedPassword, txtDisplayName.Text) Then
                    Debug.WriteLine("User created successfully, setting auth cookie and redirecting")
                    FormsAuthentication.SetAuthCookie(txtUsername.Text, False)
                    Response.Redirect("~/Default.aspx", True)  ' Force immediate redirect
                    Context.ApplicationInstance.CompleteRequest()
                    Return
                End If

                Debug.WriteLine("User creation failed")
                lblMessage.Text = "Registration failed. Username or email might already be in use."
                lblMessage.CssClass = "error"
            End If
        Catch ex As Exception
            Debug.WriteLine("Error in registration: " & ex.ToString())
            lblMessage.Text = "An error occurred during registration. Please try again."
            lblMessage.CssClass = "error"
        End Try
    End Sub
End Class