Imports System.Data.SqlClient
Imports System.Configuration
Imports System.Data
Imports PvAChess

Public Class ChessGameLogger
    Private Shared ReadOnly ConnectionString As String = ConfigurationManager.ConnectionStrings("ChessGameConnection").ConnectionString

    Public Shared Function DB_StartNewGame(gameId As String, isSoloPlay As Boolean, firstPlayerUserId As Integer) As Boolean
        Try
            LogToFile("StartNewGame has been called..." & gameId & " " & isSoloPlay & " " & firstPlayerUserId)

            Using conn As New SqlConnection(ConnectionString)
                conn.Open()

                ' Use the existing stored procedure
                Using cmd As New SqlCommand("sp_ChessGame_Start", conn)
                    cmd.CommandType = CommandType.StoredProcedure

                    cmd.Parameters.AddWithValue("@GameId", gameId)
                    cmd.Parameters.AddWithValue("@IsSoloPlay", isSoloPlay)
                    cmd.Parameters.AddWithValue("@FirstPlayerUserId", firstPlayerUserId)

                    Dim rowsAffected = cmd.ExecuteNonQuery()
                    LogToFile("StartNewGame database update", New With {
                        .gameId = gameId,
                        .isSoloPlay = isSoloPlay,
                        .firstPlayerUserId = firstPlayerUserId,
                        .rowsAffected = rowsAffected
                    })
                    Return True
                End Using
            End Using
        Catch ex As Exception
            LogToFile("StartNewGame error", New With {.error = ex.Message})
            System.Diagnostics.Debug.WriteLine("Error starting game: " & ex.Message)
            Return False
        End Try
    End Function

    Public Shared Function DB_ValidateGamePlayer(userId As Integer, gameId As String) As Boolean
        Try
            Using conn As New SqlConnection(ConnectionString)
                Using cmd As New SqlCommand("sp_ChessGame_ValidateGamePlayer", conn)
                    cmd.CommandType = CommandType.StoredProcedure
                    cmd.Parameters.AddWithValue("@UserID", userId)
                    cmd.Parameters.AddWithValue("@GameID", gameId)

                    conn.Open()
                    Using reader = cmd.ExecuteReader()
                        Return reader.HasRows
                    End Using
                End Using
            End Using
        Catch ex As Exception
            LogToFile("DB_ValidateGamePlayer", New With {.error = ex.Message})
            Return False
        End Try
    End Function

    Public Shared Sub DB_LogMove(move As ChessMove)
        Try
            Using conn As New SqlConnection(ConnectionString)
                conn.Open()
                Using cmd As New SqlCommand("sp_ChessGame_LogMove", conn)
                    cmd.CommandType = CommandType.StoredProcedure

                    cmd.Parameters.AddWithValue("@GameId", move.GameId)
                    cmd.Parameters.AddWithValue("@MoveNumber", move.MoveNumber)
                    cmd.Parameters.AddWithValue("@PlayerRole", move.PlayerRole)
                    cmd.Parameters.AddWithValue("@FromRow", move.FromRow)
                    cmd.Parameters.AddWithValue("@FromCol", move.FromCol)
                    cmd.Parameters.AddWithValue("@ToRow", move.ToRow)
                    cmd.Parameters.AddWithValue("@ToCol", move.ToCol)
                    cmd.Parameters.AddWithValue("@PieceMoved", move.PieceMoved)
                    cmd.Parameters.AddWithValue("@PieceCaptured", If(String.IsNullOrEmpty(move.PieceCaptured), DBNull.Value, move.PieceCaptured))
                    cmd.Parameters.AddWithValue("@WasPromotion", move.WasPromotion)
                    cmd.Parameters.AddWithValue("@UserId", move.UserId)

                    Dim rowsAffected = cmd.ExecuteNonQuery()
                    LogToFile("LogMove database update", New With {
                        .gameId = move.GameId,
                        .moveNumber = move.MoveNumber,
                        .playerRole = move.PlayerRole,
                        .rowsAffected = rowsAffected
                    })
                End Using
            End Using
        Catch ex As Exception
            LogToFile("LogMove error", New With {.error = ex.Message})
            System.Diagnostics.Debug.WriteLine("Error logging move: " & ex.Message)
        End Try
    End Sub

    Public Shared Sub DB_AddSecondPlayer(gameId As String, secondPlayerUserId As Integer)
        Try
            LogToFile("DB_AddSecondPlayer starting", New With {
                .gameId = gameId,
                .secondPlayerUserId = secondPlayerUserId
            })

            Using conn As New SqlConnection(ConnectionString)
                conn.Open()

                ' Use the existing stored procedure
                Using cmd As New SqlCommand("sp_ChessGame_AddSecondPlayer", conn)
                    cmd.CommandType = CommandType.StoredProcedure

                    cmd.Parameters.AddWithValue("@GameId", gameId)
                    cmd.Parameters.AddWithValue("@SecondPlayerUserId", secondPlayerUserId)

                    Dim rowsAffected = cmd.ExecuteNonQuery()
                    LogToFile("AddSecondPlayer database update", New With {
                        .gameId = gameId,
                        .secondPlayerUserId = secondPlayerUserId,
                        .rowsAffected = rowsAffected
                    })

                    If rowsAffected = 0 Then
                        ' If no rows were updated, the game might not exist yet
                        ' Try to create the game first
                        Using cmdGame As New SqlCommand("sp_ChessGame_Start", conn)
                            cmdGame.CommandType = CommandType.StoredProcedure
                            cmdGame.Parameters.AddWithValue("@GameId", gameId)
                            cmdGame.Parameters.AddWithValue("@IsSoloPlay", False)
                            cmdGame.Parameters.AddWithValue("@FirstPlayerUserId", secondPlayerUserId) ' Use second player as first player

                            Dim gameRowsAffected = cmdGame.ExecuteNonQuery()
                            LogToFile("Created missing game", New With {
                                .gameId = gameId,
                                .rowsAffected = gameRowsAffected
                            })

                            ' Now try to add the second player again
                            rowsAffected = cmd.ExecuteNonQuery()
                            LogToFile("AddSecondPlayer retry", New With {
                                .gameId = gameId,
                                .secondPlayerUserId = secondPlayerUserId,
                                .rowsAffected = rowsAffected
                            })
                        End Using
                    End If
                End Using
            End Using
        Catch ex As Exception
            LogToFile("AddSecondPlayer error", New With {
                .gameId = gameId,
                .secondPlayerUserId = secondPlayerUserId,
                .error = ex.Message,
                .stackTrace = ex.StackTrace
            })
            System.Diagnostics.Debug.WriteLine("Error adding second player: " & ex.Message)
            Throw ' Re-throw to ensure the error is properly handled upstream
        End Try
    End Sub

    Public Shared Sub DB_EndGame(gameId As String, EndGameAction As String, EndGameUserId As Integer)
        Try
            Using conn As New SqlConnection(ConnectionString)
                conn.Open()
                Using cmd As New SqlCommand("sp_ChessGame_End", conn)
                    cmd.CommandType = CommandType.StoredProcedure

                    cmd.Parameters.AddWithValue("@GameId", gameId)
                    cmd.Parameters.AddWithValue("@EndGameAction", EndGameAction)
                    cmd.Parameters.AddWithValue("@EndGameUserId", EndGameUserId)

                    Dim rowsAffected = cmd.ExecuteNonQuery()
                    LogToFile("EndGame database update", New With {
                        .gameId = gameId,
                        .EndGameAction = EndGameAction,
                        .EndGameUserId = EndGameUserId,
                        .rowsAffected = rowsAffected
                    })
                End Using
            End Using
        Catch ex As Exception
            LogToFile("EndGame error", New With {.error = ex.Message})
            System.Diagnostics.Debug.WriteLine("Error ending game: " & ex.Message)
        End Try
    End Sub

    Public Shared Function DB_GetGameHistory(Optional limit As Integer = 10) As DataTable
        Try
            Using conn As New SqlConnection(ConnectionString)
                conn.Open()
                Using cmd As New SqlCommand("sp_ChessGame_GetHistory", conn)
                    cmd.CommandType = CommandType.StoredProcedure
                    cmd.Parameters.AddWithValue("@Limit", limit)

                    Dim dt As New DataTable()
                    Using adapter As New SqlDataAdapter(cmd)
                        adapter.Fill(dt)
                    End Using
                    Return dt
                End Using
            End Using
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Error getting game history: " & ex.Message)
            Return Nothing
        End Try
    End Function

    Public Shared Function DB_GetUserId(username As String) As Integer
        Try
            Using conn As New SqlConnection(ConnectionString)
                conn.Open()
                Using cmd As New SqlCommand("sp_ChessUser_GetUserId", conn)
                    cmd.CommandType = CommandType.StoredProcedure
                    cmd.Parameters.AddWithValue("@Username", username)

                    Dim result = cmd.ExecuteScalar()
                    If result IsNot Nothing Then
                        Return Convert.ToInt32(result)
                    End If
                End Using
            End Using
            Return -1  ' Return -1 if user not found
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Error getting user ID: " & ex.Message)
            Return -1
        End Try
    End Function
End Class