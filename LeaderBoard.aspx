﻿<%@ Page Language="VB" AutoEventWireup="false" CodeFile="LeaderBoard.aspx.vb" Inherits="PvAChess.LeaderBoard" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Plebs vs Aristoi - Leader Board</title>
    <link href="Styles/PvAChess.css" rel="stylesheet" />
    <link href="Styles/LeaderBoard.css" rel="stylesheet" />
</head>
<body>
    <form id="form1" runat="server">
        <div class="container">
            <h1 class="game-title">Plebs vs Aristoi - Leader Board</h1>

            <div class="info-section_lb">
                <asp:HyperLink runat="server" NavigateUrl="~/Default.aspx" CssClass="lnkbtn">Return to Game</asp:HyperLink>
            </div>
            <div class="tab-container">
                <asp:Button runat="server" ID="btnOverall" Text="Overall Stats" CssClass="tab-button active" OnClick="btnOverall_Click" />
                <asp:Button runat="server" ID="btnAggressive" Text="Most Aggressive" CssClass="tab-button" OnClick="btnAggressive_Click" />
                <asp:Button runat="server" ID="btnRecent" Text="Recent Activity" CssClass="tab-button" OnClick="btnRecent_Click" />            
            </div>

            <asp:MultiView runat="server" ID="mvLeaderboard" ActiveViewIndex="0">
                <asp:View runat="server" ID="vwOverall">
                    <div class="leaderboard-section">
                        <h2 class="leaderboard-title">Overall Player Rankings</h2>
                        <asp:GridView runat="server" ID="gvOverall" CssClass="stat-table" AutoGenerateColumns="false">
                            <Columns>
                                <asp:TemplateField HeaderText="Rank">
                                    <ItemTemplate>
                                        <%# If(Container.DataItemIndex < 3, 
                                            New String() {"🥇", "🥈", "🥉"}(Container.DataItemIndex), 
                                            (Container.DataItemIndex + 1).ToString()) %>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:BoundField DataField="DisplayName" HeaderText="Player" />
                                <asp:BoundField DataField="TotalGames" HeaderText="Games" />
                                <asp:BoundField DataField="GamesWon" HeaderText="Wins" />
                                <asp:BoundField DataField="WinPercentage" HeaderText="Win %" DataFormatString="{0:N1}%" />
                                <asp:BoundField DataField="MultiplayerGames" HeaderText="MP Games" />
                                <asp:BoundField DataField="SoloGames" HeaderText="Solo Games" />
                            </Columns>
                        </asp:GridView>
                    </div>
                </asp:View>

                <asp:View runat="server" ID="vwAggressive">
                    <div class="leaderboard-section">
                        <h2 class="leaderboard-title">Most Aggressive Players</h2>
                        <asp:GridView runat="server" ID="gvAggressive" CssClass="stat-table" AutoGenerateColumns="false">
                            <Columns>
                                <asp:TemplateField HeaderText="Rank">
                                    <ItemTemplate>
                                        <%# If(Container.DataItemIndex < 3, 
                                            New String() {"🥇", "🥈", "🥉"}(Container.DataItemIndex), 
                                            (Container.DataItemIndex + 1).ToString()) %>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:BoundField DataField="DisplayName" HeaderText="Player" />
                                <asp:BoundField DataField="TotalCaptures" HeaderText="Captures" />
                                <asp:BoundField DataField="GamesPlayed" HeaderText="Games" />
                                <asp:BoundField DataField="CapturesPerGame" HeaderText="Captures/Game" DataFormatString="{0:N1}" />
                            </Columns>
                        </asp:GridView>
                    </div>
                </asp:View>

                <asp:View runat="server" ID="vwRecent">
                    <div class="leaderboard-section">
                        <h2 class="leaderboard-title">Recent Activity (Last 30 Days)</h2>
                        <asp:GridView runat="server" ID="gvRecent" CssClass="stat-table" AutoGenerateColumns="false">
                            <Columns>
                                <asp:TemplateField HeaderText="Rank">
                                    <ItemTemplate>
                                        <%# If(Container.DataItemIndex < 3, 
                                            New String() {"🥇", "🥈", "🥉"}(Container.DataItemIndex), 
                                            (Container.DataItemIndex + 1).ToString()) %>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:BoundField DataField="DisplayName" HeaderText="Player" />
                                <asp:BoundField DataField="RecentGames" HeaderText="Games" />
                                <asp:BoundField DataField="RecentWins" HeaderText="Wins" />
                            </Columns>
                        </asp:GridView>
                    </div>
                </asp:View>
            </asp:MultiView>

        </div>
    </form>
</body>
</html>