Public Class ChessMove
    Public Property GameId As String
    Public Property MoveNumber As Integer
    Public Property PlayerRole As String
    Public Property FromRow As Integer
    Public Property FromCol As Integer
    Public Property ToRow As Integer
    Public Property ToCol As Integer
    Public Property PieceMoved As String
    Public Property PieceCaptured As String
    Public Property WasPromotion As Boolean
    Public Property UserId As Integer
End Class

Public Class ChessGame
    Public Property GameId As String
    Public Property StartTime As DateTime
    Public Property EndTime As DateTime
    Public Property Winner As String
    Public Property FirstPlayerSessionId As String
    Public Property SecondPlayerSessionId As String
    Public Property IsSoloPlay As Boolean
    Public Property Moves As List(Of ChessMove)
    
    Public Sub New()
        Moves = New List(Of ChessMove)
    End Sub
End Class 