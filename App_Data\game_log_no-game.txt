2025-03-04 01:16:55.382419 - Game[no-game] - Conn[f1a5ad55-d8c5-4caf-b187-4d32d1629e0f]: OnDisconnected - Found games
	{
  "connectionId": "f1a5ad55-d8c5-4caf-b187-4d32d1629e0f",
  "gameCount": 0
}
2025-03-04 01:16:56.790322 - Game[no-game] - Conn[5d67b91e-b0f0-46df-85e1-c45e38e55174]: OnDisconnected - Found games
	{
  "connectionId": "5d67b91e-b0f0-46df-85e1-c45e38e55174",
  "gameCount": 0
}
2025-03-04 01:17:05.212752 - Game[no-game] - Conn[9367e100-20cc-42e3-aba9-c83bf7026b17]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "3SVVRX",
  "userId": 26,
  "isSoloPlay": false,
  "connectionId": "9367e100-20cc-42e3-aba9-c83bf7026b17",
  "gameExists": false,
  "connectionsExist": false
}
2025-03-04 01:17:05.214752 - Game[no-game] - Conn[9367e100-20cc-42e3-aba9-c83bf7026b17]: JoinGame - Adding to userConnections
	{
  "connectionId": "9367e100-20cc-42e3-aba9-c83bf7026b17",
  "userId": 26
}
2025-03-04 01:17:05.216752 - Game[no-game] - Conn[9367e100-20cc-42e3-aba9-c83bf7026b17]: JoinGame - Creating new game
	{
  "gameId": "3SVVRX",
  "userId": 26
}
2025-03-04 01:17:05.221390 - Game[no-game] - Conn[no-connection]: StartNewGame has been called...3SVVRX False 26
2025-03-04 01:17:05.275999 - Game[no-game] - Conn[no-connection]: StartNewGame error
	{
  "error": "Violation of PRIMARY KEY constraint 'PK__ChessGam__2AB897FDA104119F'. Cannot insert duplicate key in object 'dbo.ChessGames'. The duplicate key value is (3SVVRX).\r\nThe statement has been terminated."
}
2025-03-04 01:17:05.313590 - Game[no-game] - Conn[9367e100-20cc-42e3-aba9-c83bf7026b17]: JoinGame - Failed to log game to database
	{
  "gameId": "3SVVRX"
}
2025-03-04 01:19:05.236380 - Game[no-game] - Conn[85018013-9b74-4724-81fd-485e3d3a5c30]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "3SVVRX",
  "userId": 26,
  "isSoloPlay": false,
  "connectionId": "85018013-9b74-4724-81fd-485e3d3a5c30",
  "gameExists": false,
  "connectionsExist": false
}
2025-03-04 01:19:05.237985 - Game[no-game] - Conn[85018013-9b74-4724-81fd-485e3d3a5c30]: JoinGame - Adding to userConnections
	{
  "connectionId": "85018013-9b74-4724-81fd-485e3d3a5c30",
  "userId": 26
}
2025-03-04 01:19:05.239988 - Game[no-game] - Conn[85018013-9b74-4724-81fd-485e3d3a5c30]: JoinGame - Game not in memory, checking database
	{
  "gameId": "3SVVRX",
  "userId": 26
}
2025-03-04 01:19:05.338932 - Game[no-game] - Conn[85018013-9b74-4724-81fd-485e3d3a5c30]: ReconstructBoardState error
	{
  "gameId": "3SVVRX",
  "error": "UserId"
}
2025-03-04 01:19:05.340935 - Game[no-game] - Conn[85018013-9b74-4724-81fd-485e3d3a5c30]: JoinGame - Found game in database, restoring
	{
  "gameId": "3SVVRX",
  "firstPlayerUserId": 0,
  "firstPlayerIsPlebs": false
}
2025-03-04 01:19:05.346439 - Game[no-game] - Conn[85018013-9b74-4724-81fd-485e3d3a5c30]: JoinGame - Connections check
	{
  "gameId": "3SVVRX",
  "connectionsExists": true,
  "userId": 26
}
2025-03-04 01:19:47.173170 - Game[no-game] - Conn[ca088546-af98-424c-846d-d2076286aca8]: OnDisconnected - Found games
	{
  "connectionId": "ca088546-af98-424c-846d-d2076286aca8",
  "gameCount": 0
}
2025-03-04 01:19:47.206388 - Game[no-game] - Conn[85018013-9b74-4724-81fd-485e3d3a5c30]: OnReconnected - User found
	{
  "userId": 26,
  "connectionId": "85018013-9b74-4724-81fd-485e3d3a5c30"
}
2025-03-04 01:20:07.692190 - Game[no-game] - Conn[9367e100-20cc-42e3-aba9-c83bf7026b17]: OnDisconnected - Found games
	{
  "connectionId": "9367e100-20cc-42e3-aba9-c83bf7026b17",
  "gameCount": 0
}
2025-03-04 01:20:19.553770 - Game[no-game] - Conn[092cfb39-761e-4331-b90f-99a69ae00d8c]: OnDisconnected - Found games
	{
  "connectionId": "092cfb39-761e-4331-b90f-99a69ae00d8c",
  "gameCount": 0
}
2025-03-04 01:20:19.699402 - Game[no-game] - Conn[85018013-9b74-4724-81fd-485e3d3a5c30]: OnDisconnected - Found games
	{
  "connectionId": "85018013-9b74-4724-81fd-485e3d3a5c30",
  "gameCount": 1
}
2025-03-04 01:20:19.731516 - Game[no-game] - Conn[85018013-9b74-4724-81fd-485e3d3a5c30]: OnReconnected - User found
	{
  "userId": 26,
  "connectionId": "85018013-9b74-4724-81fd-485e3d3a5c30"
}
2025-03-04 01:20:19.795135 - Game[no-game] - Conn[85018013-9b74-4724-81fd-485e3d3a5c30]: OnDisconnected - Updated game status
	{
  "gameId": "3SVVRX",
  "status": "Abandoned"
}
2025-03-04 01:20:19.977239 - Game[no-game] - Conn[85018013-9b74-4724-81fd-485e3d3a5c30]: OnDisconnected - Updated connections
	{
  "gameId": "3SVVRX"
}
2025-03-04 01:20:20.010750 - Game[no-game] - Conn[85018013-9b74-4724-81fd-485e3d3a5c30]: OnDisconnected - Removed user connection
	{
  "connectionId": "85018013-9b74-4724-81fd-485e3d3a5c30",
  "userId": 26
}
2025-03-04 01:20:20.075140 - Game[no-game] - Conn[db225452-4b56-4b06-a723-ebd2c5b33113]: OnDisconnected - Found games
	{
  "connectionId": "db225452-4b56-4b06-a723-ebd2c5b33113",
  "gameCount": 0
}
2025-03-04 04:22:29.374787 - Game[no-game] - Conn[85018013-9b74-4724-81fd-485e3d3a5c30]: OnDisconnected - Found games
	{
  "connectionId": "85018013-9b74-4724-81fd-485e3d3a5c30",
  "gameCount": 0
}
2025-03-04 04:22:30.331319 - Game[no-game] - Conn[9367e100-20cc-42e3-aba9-c83bf7026b17]: OnDisconnected - Found games
	{
  "connectionId": "9367e100-20cc-42e3-aba9-c83bf7026b17",
  "gameCount": 0
}
2025-03-04 04:22:31.376984 - Game[no-game] - Conn[092cfb39-761e-4331-b90f-99a69ae00d8c]: OnDisconnected - Found games
	{
  "connectionId": "092cfb39-761e-4331-b90f-99a69ae00d8c",
  "gameCount": 0
}
2025-03-04 04:22:36.117136 - Game[no-game] - Conn[2e5e60ca-15cb-4d99-abff-d5f6ff8ac5a8]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "3SVVRX",
  "userId": 26,
  "isSoloPlay": false,
  "connectionId": "2e5e60ca-15cb-4d99-abff-d5f6ff8ac5a8",
  "gameExists": false,
  "connectionsExist": false
}
2025-03-04 04:22:36.119727 - Game[no-game] - Conn[2e5e60ca-15cb-4d99-abff-d5f6ff8ac5a8]: JoinGame - Adding to userConnections
	{
  "connectionId": "2e5e60ca-15cb-4d99-abff-d5f6ff8ac5a8",
  "userId": 26
}
2025-03-04 04:22:36.120729 - Game[no-game] - Conn[2e5e60ca-15cb-4d99-abff-d5f6ff8ac5a8]: JoinGame - Game not in memory, checking database
	{
  "gameId": "3SVVRX",
  "userId": 26
}
2025-03-04 04:22:36.281839 - Game[no-game] - Conn[2e5e60ca-15cb-4d99-abff-d5f6ff8ac5a8]: ReconstructBoardState error
	{
  "gameId": "3SVVRX",
  "error": "UserId"
}
2025-03-04 04:22:36.283839 - Game[no-game] - Conn[2e5e60ca-15cb-4d99-abff-d5f6ff8ac5a8]: JoinGame - Found game in database, restoring
	{
  "gameId": "3SVVRX",
  "firstPlayerUserId": 0,
  "firstPlayerIsPlebs": false
}
2025-03-04 04:22:36.287951 - Game[no-game] - Conn[2e5e60ca-15cb-4d99-abff-d5f6ff8ac5a8]: JoinGame - Connections check
	{
  "gameId": "3SVVRX",
  "connectionsExists": true,
  "userId": 26
}
2025-03-04 05:04:46.057589 - Game[no-game] - Conn[aea0079d-e3d3-43cb-ac51-940cd547ccfc]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "3SVVRX",
  "userId": 26,
  "isSoloPlay": false,
  "connectionId": "aea0079d-e3d3-43cb-ac51-940cd547ccfc",
  "gameExists": false,
  "connectionsExist": false
}
2025-03-04 05:04:46.059690 - Game[no-game] - Conn[aea0079d-e3d3-43cb-ac51-940cd547ccfc]: JoinGame - Adding to userConnections
	{
  "connectionId": "aea0079d-e3d3-43cb-ac51-940cd547ccfc",
  "userId": 26
}
2025-03-04 05:04:46.060690 - Game[no-game] - Conn[aea0079d-e3d3-43cb-ac51-940cd547ccfc]: JoinGame - Game not in memory, checking database
	{
  "gameId": "3SVVRX",
  "userId": 26
}
2025-03-04 05:04:46.123805 - Game[no-game] - Conn[aea0079d-e3d3-43cb-ac51-940cd547ccfc]: ReconstructBoardState error
	{
  "gameId": "3SVVRX",
  "error": "Could not find stored procedure 'sp_ChessGame_GetGameState'.",
  "stackTrace": "   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)\r\n   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)\r\n   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)\r\n   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()\r\n   at System.Data.SqlClient.SqlDataReader.get_MetaData()\r\n   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)\r\n   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)\r\n   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)\r\n   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)\r\n   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)\r\n   at System.Data.SqlClient.SqlCommand.ExecuteReader()\r\n   at PvAChess.PvAChess.Hubs.GameHub.ReconstructBoardState(String gameId) in C:\\inetpub\\wwwroot\\PvAChess\\Hubs\\GameHub.vb:line 823"
}
2025-03-04 05:04:46.125805 - Game[no-game] - Conn[aea0079d-e3d3-43cb-ac51-940cd547ccfc]: JoinGame - Connections check
	{
  "gameId": "3SVVRX",
  "connectionsExists": false,
  "userId": 26
}
2025-03-04 05:05:30.272660 - Game[no-game] - Conn[aea0079d-e3d3-43cb-ac51-940cd547ccfc]: OnReconnected - User found
	{
  "userId": 26,
  "connectionId": "aea0079d-e3d3-43cb-ac51-940cd547ccfc"
}
2025-03-06 06:49:58.174460 - Game[no-game] - Conn[df3b7b54-c1d4-44a0-b813-7f9ce3981f3f]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "3SVVRX",
  "userId": 26,
  "isSoloPlay": false,
  "connectionId": "df3b7b54-c1d4-44a0-b813-7f9ce3981f3f",
  "gameExists": false,
  "connectionsExist": false
}
2025-03-06 06:49:58.176631 - Game[no-game] - Conn[df3b7b54-c1d4-44a0-b813-7f9ce3981f3f]: JoinGame - Adding to userConnections
	{
  "connectionId": "df3b7b54-c1d4-44a0-b813-7f9ce3981f3f",
  "userId": 26
}
2025-03-06 06:49:58.178631 - Game[no-game] - Conn[df3b7b54-c1d4-44a0-b813-7f9ce3981f3f]: JoinGame - Game not in memory, checking database
	{
  "gameId": "3SVVRX",
  "userId": 26
}
2025-04-05 12:45:13.215096 - Game[no-game] - Conn[cf7eb3cd-7736-43b0-918b-503e7bbced16]: UpdateFirstPlayerSelection - Starting
	{
  "gameId": "DB8GRM",
  "isPlebs": true
}
2025-04-05 12:45:13.217096 - Game[no-game] - Conn[cf7eb3cd-7736-43b0-918b-503e7bbced16]: UpdateFirstPlayerSelection - State updated
	{
  "gameId": "DB8GRM",
  "firstPlayerIsPlebs": true,
  "isPlebsTurn": true
}
2025-04-05 12:45:13.227602 - Game[no-game] - Conn[cf7eb3cd-7736-43b0-918b-503e7bbced16]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "DB8GRM",
  "userId": 26,
  "isSoloPlay": false,
  "connectionId": "cf7eb3cd-7736-43b0-918b-503e7bbced16",
  "gameExists": true,
  "connectionsExist": false
}
2025-04-05 12:45:13.228601 - Game[no-game] - Conn[cf7eb3cd-7736-43b0-918b-503e7bbced16]: JoinGame - Adding to userConnections
	{
  "connectionId": "cf7eb3cd-7736-43b0-918b-503e7bbced16",
  "userId": 26
}
2025-04-05 12:45:13.230601 - Game[no-game] - Conn[cf7eb3cd-7736-43b0-918b-503e7bbced16]: JoinGame - Game exists in memory - DETAILED
	{
  "gameId": "DB8GRM",
  "firstPlayerUserId": 0,
  "secondPlayerUserId": 0,
  "status": "AwaitingPlayers",
  "currentUserId": 26,
  "isReturningPlayer": false,
  "connectionExists": false,
  "connection1": "null",
  "connection2": "null"
}
2025-04-05 12:45:13.232601 - Game[no-game] - Conn[cf7eb3cd-7736-43b0-918b-503e7bbced16]: JoinGame - After rejoin check
	{
  "gameId": "DB8GRM",
  "userId": 26,
  "connectionsExist": false,
  "previousBlockExecuted": false
}
2025-04-05 12:45:13.234602 - Game[no-game] - Conn[cf7eb3cd-7736-43b0-918b-503e7bbced16]: JoinGame - No connections found for game after rejoin check
	{
  "gameId": "DB8GRM",
  "userId": 26
}
2025-04-05 12:45:13.235601 - Game[no-game] - Conn[cf7eb3cd-7736-43b0-918b-503e7bbced16]: JoinGame - Second player joining
	{
  "gameId": "DB8GRM",
  "updateResult": true,
  "userId": 26
}
2025-04-05 12:45:13.279109 - Game[no-game] - Conn[cf7eb3cd-7736-43b0-918b-503e7bbced16]: JoinGame - Second player joined successfully
	{
  "gameId": "DB8GRM",
  "userId": 26
}
2025-04-05 12:45:18.777314 - Game[no-game] - Conn[1f96e88b-2f30-46a2-b5e5-8c1ca6113c50]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "DB8GRM",
  "userId": 1,
  "isSoloPlay": false,
  "connectionId": "1f96e88b-2f30-46a2-b5e5-8c1ca6113c50",
  "gameExists": true,
  "connectionsExist": true
}
2025-04-05 12:45:18.778315 - Game[no-game] - Conn[1f96e88b-2f30-46a2-b5e5-8c1ca6113c50]: JoinGame - Adding to userConnections
	{
  "connectionId": "1f96e88b-2f30-46a2-b5e5-8c1ca6113c50",
  "userId": 1
}
2025-04-05 12:45:18.780314 - Game[no-game] - Conn[1f96e88b-2f30-46a2-b5e5-8c1ca6113c50]: JoinGame - Game exists in memory - DETAILED
	{
  "gameId": "DB8GRM",
  "firstPlayerUserId": 0,
  "secondPlayerUserId": 26,
  "status": "Active",
  "currentUserId": 1,
  "isReturningPlayer": false,
  "connectionExists": true,
  "connection1": "null",
  "connection2": "cf7eb3cd-7736-43b0-918b-503e7bbced16"
}
2025-04-05 12:45:18.782314 - Game[no-game] - Conn[1f96e88b-2f30-46a2-b5e5-8c1ca6113c50]: JoinGame - After rejoin check
	{
  "gameId": "DB8GRM",
  "userId": 1,
  "connectionsExist": true,
  "previousBlockExecuted": false
}
2025-04-05 12:45:18.784314 - Game[no-game] - Conn[1f96e88b-2f30-46a2-b5e5-8c1ca6113c50]: JoinGame - Game already has two players
	{
  "gameId": "DB8GRM",
  "connection1": null,
  "connection2": "cf7eb3cd-7736-43b0-918b-503e7bbced16"
}
2025-04-05 12:45:57.406274 - Game[no-game] - Conn[1f96e88b-2f30-46a2-b5e5-8c1ca6113c50]: OnDisconnected - Found games
	{
  "connectionId": "1f96e88b-2f30-46a2-b5e5-8c1ca6113c50",
  "gameCount": 0
}
2025-04-05 12:45:57.408274 - Game[no-game] - Conn[1f96e88b-2f30-46a2-b5e5-8c1ca6113c50]: OnDisconnected - Removed user connection
	{
  "connectionId": "1f96e88b-2f30-46a2-b5e5-8c1ca6113c50",
  "userId": 1
}
2025-04-05 13:57:37.836190 - Game[no-game] - Conn[6866117b-bf22-451e-817d-540e53898c4b]: UpdateFirstPlayerSelection - Starting
	{
  "gameId": "DWYXLH",
  "isPlebs": true
}
2025-04-05 13:57:37.838430 - Game[no-game] - Conn[6866117b-bf22-451e-817d-540e53898c4b]: UpdateFirstPlayerSelection - State updated
	{
  "gameId": "DWYXLH",
  "firstPlayerIsPlebs": true,
  "isPlebsTurn": true
}
2025-04-05 13:57:37.850709 - Game[no-game] - Conn[6866117b-bf22-451e-817d-540e53898c4b]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "DWYXLH",
  "userId": 26,
  "isSoloPlay": false,
  "connectionId": "6866117b-bf22-451e-817d-540e53898c4b",
  "gameExists": true,
  "connectionsExist": false
}
2025-04-05 13:57:37.852709 - Game[no-game] - Conn[6866117b-bf22-451e-817d-540e53898c4b]: JoinGame - Adding to userConnections
	{
  "connectionId": "6866117b-bf22-451e-817d-540e53898c4b",
  "userId": 26
}
2025-04-05 13:57:37.854709 - Game[no-game] - Conn[6866117b-bf22-451e-817d-540e53898c4b]: JoinGame - Game exists in memory - DETAILED
	{
  "gameId": "DWYXLH",
  "firstPlayerUserId": 0,
  "secondPlayerUserId": 0,
  "status": "AwaitingPlayers",
  "currentUserId": 26,
  "isReturningPlayer": false,
  "connectionExists": false,
  "connection1": "null",
  "connection2": "null"
}
2025-04-05 13:57:37.856710 - Game[no-game] - Conn[6866117b-bf22-451e-817d-540e53898c4b]: JoinGame - After rejoin check
	{
  "gameId": "DWYXLH",
  "userId": 26,
  "connectionsExist": false,
  "previousBlockExecuted": false
}
2025-04-05 13:57:37.857808 - Game[no-game] - Conn[6866117b-bf22-451e-817d-540e53898c4b]: JoinGame - No connections found for game after rejoin check
	{
  "gameId": "DWYXLH",
  "userId": 26
}
2025-04-05 13:57:37.859810 - Game[no-game] - Conn[6866117b-bf22-451e-817d-540e53898c4b]: JoinGame - Second player joining
	{
  "gameId": "DWYXLH",
  "updateResult": true,
  "userId": 26
}
2025-04-05 13:57:37.902227 - Game[no-game] - Conn[6866117b-bf22-451e-817d-540e53898c4b]: JoinGame - Second player joined successfully
	{
  "gameId": "DWYXLH",
  "userId": 26
}
2025-04-05 13:57:45.055490 - Game[no-game] - Conn[4bacc7ab-ec05-4315-8ff2-6cbafdb08405]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "DWYXLH",
  "userId": 1,
  "isSoloPlay": false,
  "connectionId": "4bacc7ab-ec05-4315-8ff2-6cbafdb08405",
  "gameExists": true,
  "connectionsExist": true
}
2025-04-05 13:57:45.057490 - Game[no-game] - Conn[4bacc7ab-ec05-4315-8ff2-6cbafdb08405]: JoinGame - Adding to userConnections
	{
  "connectionId": "4bacc7ab-ec05-4315-8ff2-6cbafdb08405",
  "userId": 1
}
2025-04-05 13:57:45.060490 - Game[no-game] - Conn[4bacc7ab-ec05-4315-8ff2-6cbafdb08405]: JoinGame - Game exists in memory - DETAILED
	{
  "gameId": "DWYXLH",
  "firstPlayerUserId": 0,
  "secondPlayerUserId": 26,
  "status": "Active",
  "currentUserId": 1,
  "isReturningPlayer": false,
  "connectionExists": true,
  "connection1": "null",
  "connection2": "6866117b-bf22-451e-817d-540e53898c4b"
}
2025-04-05 13:57:45.061490 - Game[no-game] - Conn[4bacc7ab-ec05-4315-8ff2-6cbafdb08405]: JoinGame - After rejoin check
	{
  "gameId": "DWYXLH",
  "userId": 1,
  "connectionsExist": true,
  "previousBlockExecuted": false
}
2025-04-05 13:57:45.063490 - Game[no-game] - Conn[4bacc7ab-ec05-4315-8ff2-6cbafdb08405]: JoinGame - Game already has two players
	{
  "gameId": "DWYXLH",
  "connection1": null,
  "connection2": "6866117b-bf22-451e-817d-540e53898c4b"
}
2025-04-05 13:57:45.068595 - Game[no-game] - Conn[4bacc7ab-ec05-4315-8ff2-6cbafdb08405]: CheckGameExists
	{
  "gameId": "DWYXLH"
}
2025-04-05 14:10:57.533500 - Game[no-game] - Conn[4bacc7ab-ec05-4315-8ff2-6cbafdb08405]: OnDisconnected - Found games
	{
  "connectionId": "4bacc7ab-ec05-4315-8ff2-6cbafdb08405",
  "gameCount": 0
}
2025-04-05 14:11:03.652117 - Game[no-game] - Conn[6866117b-bf22-451e-817d-540e53898c4b]: OnDisconnected - Found games
	{
  "connectionId": "6866117b-bf22-451e-817d-540e53898c4b",
  "gameCount": 0
}
2025-04-05 14:11:09.295342 - Game[no-game] - Conn[dd9d38ad-60ce-47a5-a140-dcb9f089918c]: UpdateFirstPlayerSelection - Starting
	{
  "gameId": "HOJFJC",
  "isPlebs": true
}
2025-04-05 14:11:09.297342 - Game[no-game] - Conn[dd9d38ad-60ce-47a5-a140-dcb9f089918c]: UpdateFirstPlayerSelection - Created new game
	{
  "gameId": "HOJFJC",
  "firstPlayerUserId": 0,
  "connectionId": "dd9d38ad-60ce-47a5-a140-dcb9f089918c"
}
2025-04-05 14:11:09.299342 - Game[no-game] - Conn[dd9d38ad-60ce-47a5-a140-dcb9f089918c]: UpdateFirstPlayerSelection - State updated
	{
  "gameId": "HOJFJC",
  "firstPlayerIsPlebs": true,
  "isPlebsTurn": true,
  "firstPlayerUserId": 0
}
2025-04-05 14:11:09.309847 - Game[no-game] - Conn[dd9d38ad-60ce-47a5-a140-dcb9f089918c]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "HOJFJC",
  "userId": 26,
  "isSoloPlay": false,
  "connectionId": "dd9d38ad-60ce-47a5-a140-dcb9f089918c",
  "gameExists": true,
  "connectionsExist": true
}
2025-04-05 14:11:09.311847 - Game[no-game] - Conn[dd9d38ad-60ce-47a5-a140-dcb9f089918c]: JoinGame - Adding to userConnections
	{
  "connectionId": "dd9d38ad-60ce-47a5-a140-dcb9f089918c",
  "userId": 26
}
2025-04-05 14:11:09.314351 - Game[no-game] - Conn[dd9d38ad-60ce-47a5-a140-dcb9f089918c]: JoinGame - Game exists in memory - DETAILED
	{
  "gameId": "HOJFJC",
  "firstPlayerUserId": 0,
  "secondPlayerUserId": 0,
  "status": "AwaitingPlayers",
  "currentUserId": 26,
  "isReturningPlayer": false,
  "connectionExists": true,
  "connection1": "dd9d38ad-60ce-47a5-a140-dcb9f089918c",
  "connection2": "null"
}
2025-04-05 14:11:09.316351 - Game[no-game] - Conn[dd9d38ad-60ce-47a5-a140-dcb9f089918c]: JoinGame - After rejoin check
	{
  "gameId": "HOJFJC",
  "userId": 26,
  "connectionsExist": true,
  "previousBlockExecuted": false
}
2025-04-05 14:11:09.317856 - Game[no-game] - Conn[dd9d38ad-60ce-47a5-a140-dcb9f089918c]: JoinGame - Second player joining
	{
  "gameId": "HOJFJC",
  "updateResult": true,
  "userId": 26
}
2025-04-05 14:11:09.357362 - Game[no-game] - Conn[dd9d38ad-60ce-47a5-a140-dcb9f089918c]: JoinGame - Second player joined successfully
	{
  "gameId": "HOJFJC",
  "userId": 26
}
2025-04-05 14:11:14.242242 - Game[no-game] - Conn[ed5aca56-0c6c-410f-b2e6-19e04d9422b2]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "HOJFJC",
  "userId": 1,
  "isSoloPlay": false,
  "connectionId": "ed5aca56-0c6c-410f-b2e6-19e04d9422b2",
  "gameExists": true,
  "connectionsExist": true
}
2025-04-05 14:11:14.243242 - Game[no-game] - Conn[ed5aca56-0c6c-410f-b2e6-19e04d9422b2]: JoinGame - Adding to userConnections
	{
  "connectionId": "ed5aca56-0c6c-410f-b2e6-19e04d9422b2",
  "userId": 1
}
2025-04-05 14:11:14.245242 - Game[no-game] - Conn[ed5aca56-0c6c-410f-b2e6-19e04d9422b2]: JoinGame - Game exists in memory - DETAILED
	{
  "gameId": "HOJFJC",
  "firstPlayerUserId": 0,
  "secondPlayerUserId": 26,
  "status": "Active",
  "currentUserId": 1,
  "isReturningPlayer": false,
  "connectionExists": true,
  "connection1": "dd9d38ad-60ce-47a5-a140-dcb9f089918c",
  "connection2": "dd9d38ad-60ce-47a5-a140-dcb9f089918c"
}
2025-04-05 14:11:14.247242 - Game[no-game] - Conn[ed5aca56-0c6c-410f-b2e6-19e04d9422b2]: JoinGame - After rejoin check
	{
  "gameId": "HOJFJC",
  "userId": 1,
  "connectionsExist": true,
  "previousBlockExecuted": false
}
2025-04-05 14:11:14.249415 - Game[no-game] - Conn[ed5aca56-0c6c-410f-b2e6-19e04d9422b2]: JoinGame - Game already has two players
	{
  "gameId": "HOJFJC",
  "connection1": "dd9d38ad-60ce-47a5-a140-dcb9f089918c",
  "connection2": "dd9d38ad-60ce-47a5-a140-dcb9f089918c",
  "firstPlayerUserId": 0,
  "secondPlayerUserId": 26,
  "currentUserId": 1
}
2025-04-05 14:11:14.253917 - Game[no-game] - Conn[ed5aca56-0c6c-410f-b2e6-19e04d9422b2]: CheckGameExists
	{
  "gameId": "HOJFJC"
}
2025-04-05 17:04:47.047645 - Game[no-game] - Conn[5c314e5a-eafe-4dd2-90ac-4635bd62c92a]: UpdateFirstPlayerSelection - Starting
	{
  "gameId": "ON3344",
  "isPlebs": true
}
2025-04-05 17:04:47.049228 - Game[no-game] - Conn[5c314e5a-eafe-4dd2-90ac-4635bd62c92a]: UpdateFirstPlayerSelection - Created new game
	{
  "gameId": "ON3344",
  "firstPlayerUserId": 0,
  "connectionId": "5c314e5a-eafe-4dd2-90ac-4635bd62c92a"
}
2025-04-05 17:04:47.051231 - Game[no-game] - Conn[5c314e5a-eafe-4dd2-90ac-4635bd62c92a]: UpdateFirstPlayerSelection - State updated
	{
  "gameId": "ON3344",
  "firstPlayerIsPlebs": true,
  "isPlebsTurn": true,
  "firstPlayerUserId": 0
}
2025-04-05 17:04:47.061840 - Game[no-game] - Conn[5c314e5a-eafe-4dd2-90ac-4635bd62c92a]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "ON3344",
  "userId": 26,
  "isSoloPlay": false,
  "connectionId": "5c314e5a-eafe-4dd2-90ac-4635bd62c92a",
  "gameExists": true,
  "connectionsExist": true
}
2025-04-05 17:04:47.062840 - Game[no-game] - Conn[5c314e5a-eafe-4dd2-90ac-4635bd62c92a]: JoinGame - Adding to userConnections
	{
  "connectionId": "5c314e5a-eafe-4dd2-90ac-4635bd62c92a",
  "userId": 26
}
2025-04-05 17:04:47.064839 - Game[no-game] - Conn[5c314e5a-eafe-4dd2-90ac-4635bd62c92a]: JoinGame - Game exists in memory - DETAILED
	{
  "gameId": "ON3344",
  "firstPlayerUserId": 0,
  "secondPlayerUserId": 0,
  "status": "AwaitingPlayers",
  "currentUserId": 26,
  "isReturningPlayer": false,
  "connectionExists": true,
  "connection1": "5c314e5a-eafe-4dd2-90ac-4635bd62c92a",
  "connection2": "null"
}
2025-04-05 17:04:47.067344 - Game[no-game] - Conn[5c314e5a-eafe-4dd2-90ac-4635bd62c92a]: JoinGame - Setting FirstPlayerUserId
	{
  "gameId": "ON3344",
  "firstPlayerUserId": 26
}
2025-04-05 17:04:47.068993 - Game[no-game] - Conn[5c314e5a-eafe-4dd2-90ac-4635bd62c92a]: JoinGame - Returning player detected - DETAILED
	{
  "gameId": "ON3344",
  "userId": 26,
  "isFirstPlayer": true,
  "isSecondPlayer": false
}
2025-04-05 17:04:47.092194 - Game[no-game] - Conn[5c314e5a-eafe-4dd2-90ac-4635bd62c92a]: ReconstructBoardState - No game found
	{
  "gameId": "ON3344"
}
2025-04-05 17:04:47.093194 - Game[no-game] - Conn[5c314e5a-eafe-4dd2-90ac-4635bd62c92a]: JoinGame - Before sending PlayerJoined
	{
  "gameId": "ON3344",
  "firstPlayerIsPlebs": true,
  "isPlebsTurn": true,
  "userId": 26,
  "firstPlayerUserId": 26,
  "isUserFirstPlayer": true
}
2025-04-05 17:04:47.095194 - Game[no-game] - Conn[5c314e5a-eafe-4dd2-90ac-4635bd62c92a]: JoinGame - Role calculation
	{
  "gameId": "ON3344",
  "userId": 26,
  "isFirstPlayer": true,
  "firstPlayerIsPlebs": true,
  "calculatedPlayerRole": "Plebs"
}
2025-04-05 17:04:53.076460 - Game[no-game] - Conn[85b6661c-d486-4201-8902-210cde88f5bc]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "ON3344",
  "userId": 1,
  "isSoloPlay": false,
  "connectionId": "85b6661c-d486-4201-8902-210cde88f5bc",
  "gameExists": true,
  "connectionsExist": true
}
2025-04-05 17:04:53.078603 - Game[no-game] - Conn[85b6661c-d486-4201-8902-210cde88f5bc]: JoinGame - Adding to userConnections
	{
  "connectionId": "85b6661c-d486-4201-8902-210cde88f5bc",
  "userId": 1
}
2025-04-05 17:04:53.080605 - Game[no-game] - Conn[85b6661c-d486-4201-8902-210cde88f5bc]: JoinGame - Game exists in memory - DETAILED
	{
  "gameId": "ON3344",
  "firstPlayerUserId": 26,
  "secondPlayerUserId": 0,
  "status": "AwaitingPlayers",
  "currentUserId": 1,
  "isReturningPlayer": false,
  "connectionExists": true,
  "connection1": "5c314e5a-eafe-4dd2-90ac-4635bd62c92a",
  "connection2": "null"
}
2025-04-05 17:04:53.082605 - Game[no-game] - Conn[85b6661c-d486-4201-8902-210cde88f5bc]: JoinGame - After rejoin check
	{
  "gameId": "ON3344",
  "userId": 1,
  "connectionsExist": true,
  "previousBlockExecuted": false
}
2025-04-05 17:04:53.084607 - Game[no-game] - Conn[85b6661c-d486-4201-8902-210cde88f5bc]: JoinGame - Second player joining
	{
  "gameId": "ON3344",
  "updateResult": true,
  "userId": 1
}
2025-04-05 17:04:53.089189 - Game[no-game] - Conn[85b6661c-d486-4201-8902-210cde88f5bc]: JoinGame - Second player joined successfully
	{
  "gameId": "ON3344",
  "userId": 1
}
2025-04-05 17:05:09.331459 - Game[no-game] - Conn[5c314e5a-eafe-4dd2-90ac-4635bd62c92a]: OnDisconnected - Found games
	{
  "connectionId": "5c314e5a-eafe-4dd2-90ac-4635bd62c92a",
  "gameCount": 1
}
2025-04-05 17:05:09.333458 - Game[no-game] - Conn[5c314e5a-eafe-4dd2-90ac-4635bd62c92a]: OnDisconnected - Updated game status
	{
  "gameId": "ON3344",
  "status": "Abandoned"
}
2025-04-05 17:05:09.335458 - Game[no-game] - Conn[5c314e5a-eafe-4dd2-90ac-4635bd62c92a]: OnDisconnected - Updated connections
	{
  "gameId": "ON3344"
}
2025-04-05 17:05:09.339085 - Game[no-game] - Conn[5c314e5a-eafe-4dd2-90ac-4635bd62c92a]: OnDisconnected - Notified other player
	{
  "gameId": "ON3344",
  "otherConnectionId": "85b6661c-d486-4201-8902-210cde88f5bc"
}
2025-04-05 17:05:09.340087 - Game[no-game] - Conn[5c314e5a-eafe-4dd2-90ac-4635bd62c92a]: OnDisconnected - Removed user connection
	{
  "connectionId": "5c314e5a-eafe-4dd2-90ac-4635bd62c92a",
  "userId": 26
}
2025-04-05 17:05:11.615230 - Game[no-game] - Conn[85b6661c-d486-4201-8902-210cde88f5bc]: OnDisconnected - Found games
	{
  "connectionId": "85b6661c-d486-4201-8902-210cde88f5bc",
  "gameCount": 1
}
2025-04-05 17:05:11.617230 - Game[no-game] - Conn[85b6661c-d486-4201-8902-210cde88f5bc]: OnDisconnected - Updated game status
	{
  "gameId": "ON3344",
  "status": "Abandoned"
}
2025-04-05 17:05:11.620230 - Game[no-game] - Conn[85b6661c-d486-4201-8902-210cde88f5bc]: OnDisconnected - Updated connections
	{
  "gameId": "ON3344"
}
2025-04-05 17:05:11.621230 - Game[no-game] - Conn[85b6661c-d486-4201-8902-210cde88f5bc]: OnDisconnected - Removed user connection
	{
  "connectionId": "85b6661c-d486-4201-8902-210cde88f5bc",
  "userId": 1
}
2025-04-05 17:07:05.093729 - Game[no-game] - Conn[43417c0c-80d1-4993-a9c8-441849eac016]: OnDisconnected - Found games
	{
  "connectionId": "43417c0c-80d1-4993-a9c8-441849eac016",
  "gameCount": 0
}
2025-04-05 17:07:19.314810 - Game[no-game] - Conn[5e672313-edd8-4f8c-a7fe-ebb310cd50dd]: UpdateFirstPlayerSelection - Starting
	{
  "gameId": "M0ZBAI",
  "isPlebs": true
}
2025-04-05 17:07:19.317467 - Game[no-game] - Conn[5e672313-edd8-4f8c-a7fe-ebb310cd50dd]: UpdateFirstPlayerSelection - Created new game
	{
  "gameId": "M0ZBAI",
  "firstPlayerUserId": 0,
  "connectionId": "5e672313-edd8-4f8c-a7fe-ebb310cd50dd"
}
2025-04-05 17:07:19.318969 - Game[no-game] - Conn[5e672313-edd8-4f8c-a7fe-ebb310cd50dd]: UpdateFirstPlayerSelection - State updated
	{
  "gameId": "M0ZBAI",
  "firstPlayerIsPlebs": true,
  "isPlebsTurn": true,
  "firstPlayerUserId": 0
}
2025-04-05 17:07:19.329079 - Game[no-game] - Conn[5e672313-edd8-4f8c-a7fe-ebb310cd50dd]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "M0ZBAI",
  "userId": 26,
  "isSoloPlay": false,
  "connectionId": "5e672313-edd8-4f8c-a7fe-ebb310cd50dd",
  "gameExists": true,
  "connectionsExist": true
}
2025-04-05 17:07:19.330081 - Game[no-game] - Conn[5e672313-edd8-4f8c-a7fe-ebb310cd50dd]: JoinGame - Adding to userConnections
	{
  "connectionId": "5e672313-edd8-4f8c-a7fe-ebb310cd50dd",
  "userId": 26
}
2025-04-05 17:07:19.332083 - Game[no-game] - Conn[5e672313-edd8-4f8c-a7fe-ebb310cd50dd]: JoinGame - Game exists in memory - DETAILED
	{
  "gameId": "M0ZBAI",
  "firstPlayerUserId": 0,
  "secondPlayerUserId": 0,
  "status": "AwaitingPlayers",
  "currentUserId": 26,
  "isReturningPlayer": false,
  "connectionExists": true,
  "connection1": "5e672313-edd8-4f8c-a7fe-ebb310cd50dd",
  "connection2": "null"
}
2025-04-05 17:07:19.333083 - Game[no-game] - Conn[5e672313-edd8-4f8c-a7fe-ebb310cd50dd]: JoinGame - Setting FirstPlayerUserId
	{
  "gameId": "M0ZBAI",
  "firstPlayerUserId": 26
}
2025-04-05 17:07:19.334083 - Game[no-game] - Conn[5e672313-edd8-4f8c-a7fe-ebb310cd50dd]: JoinGame - Returning player detected - DETAILED
	{
  "gameId": "M0ZBAI",
  "userId": 26,
  "isFirstPlayer": true,
  "isSecondPlayer": false
}
2025-04-05 17:07:19.353797 - Game[no-game] - Conn[5e672313-edd8-4f8c-a7fe-ebb310cd50dd]: ReconstructBoardState - No game found
	{
  "gameId": "M0ZBAI"
}
2025-04-05 17:07:19.355797 - Game[no-game] - Conn[5e672313-edd8-4f8c-a7fe-ebb310cd50dd]: JoinGame - Before sending PlayerJoined
	{
  "gameId": "M0ZBAI",
  "firstPlayerIsPlebs": true,
  "isPlebsTurn": true,
  "userId": 26,
  "firstPlayerUserId": 26,
  "isUserFirstPlayer": true
}
2025-04-05 17:07:19.356797 - Game[no-game] - Conn[5e672313-edd8-4f8c-a7fe-ebb310cd50dd]: JoinGame - Role calculation
	{
  "gameId": "M0ZBAI",
  "userId": 26,
  "isFirstPlayer": true,
  "firstPlayerIsPlebs": true,
  "calculatedPlayerRole": "Plebs"
}
2025-04-05 17:07:24.186854 - Game[no-game] - Conn[0aee2c06-f49c-41aa-afea-4e04bf997368]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "M0ZBAI",
  "userId": 1,
  "isSoloPlay": false,
  "connectionId": "0aee2c06-f49c-41aa-afea-4e04bf997368",
  "gameExists": true,
  "connectionsExist": true
}
2025-04-05 17:07:24.188453 - Game[no-game] - Conn[0aee2c06-f49c-41aa-afea-4e04bf997368]: JoinGame - Adding to userConnections
	{
  "connectionId": "0aee2c06-f49c-41aa-afea-4e04bf997368",
  "userId": 1
}
2025-04-05 17:07:24.190455 - Game[no-game] - Conn[0aee2c06-f49c-41aa-afea-4e04bf997368]: JoinGame - Game exists in memory - DETAILED
	{
  "gameId": "M0ZBAI",
  "firstPlayerUserId": 26,
  "secondPlayerUserId": 0,
  "status": "AwaitingPlayers",
  "currentUserId": 1,
  "isReturningPlayer": false,
  "connectionExists": true,
  "connection1": "5e672313-edd8-4f8c-a7fe-ebb310cd50dd",
  "connection2": "null"
}
2025-04-05 17:07:24.192959 - Game[no-game] - Conn[0aee2c06-f49c-41aa-afea-4e04bf997368]: JoinGame - After rejoin check
	{
  "gameId": "M0ZBAI",
  "userId": 1,
  "connectionsExist": true,
  "previousBlockExecuted": false
}
2025-04-05 17:07:24.193961 - Game[no-game] - Conn[0aee2c06-f49c-41aa-afea-4e04bf997368]: JoinGame - Second player joining
	{
  "gameId": "M0ZBAI",
  "updateResult": true,
  "userId": 1
}
2025-04-05 17:07:24.198962 - Game[no-game] - Conn[0aee2c06-f49c-41aa-afea-4e04bf997368]: JoinGame - Second player joined successfully
	{
  "gameId": "M0ZBAI",
  "userId": 1
}
2025-04-05 17:13:31.892163 - Game[no-game] - Conn[0aee2c06-f49c-41aa-afea-4e04bf997368]: OnDisconnected - Found games
	{
  "connectionId": "0aee2c06-f49c-41aa-afea-4e04bf997368",
  "gameCount": 0
}
2025-04-05 17:13:47.905660 - Game[no-game] - Conn[5e672313-edd8-4f8c-a7fe-ebb310cd50dd]: OnDisconnected - Found games
	{
  "connectionId": "5e672313-edd8-4f8c-a7fe-ebb310cd50dd",
  "gameCount": 0
}
2025-04-05 17:13:52.585227 - Game[no-game] - Conn[4f7ae47b-7b3e-4655-9bff-9820a5598eb1]: UpdateFirstPlayerSelection - Starting
	{
  "gameId": "6VDUIH",
  "isPlebs": true
}
2025-04-05 17:13:52.587227 - Game[no-game] - Conn[no-connection]: StartNewGame has been called...6VDUIH False 0
2025-04-05 17:13:52.636169 - Game[no-game] - Conn[no-connection]: StartNewGame error
	{
  "error": "The INSERT statement conflicted with the FOREIGN KEY constraint \"FK_ChessGames_FirstPlayerUser\". The conflict occurred in database \"ElthosChess\", table \"dbo.ChessUsers\", column 'UserId'.\r\nThe statement has been terminated."
}
2025-04-05 17:13:52.638750 - Game[no-game] - Conn[4f7ae47b-7b3e-4655-9bff-9820a5598eb1]: UpdateFirstPlayerSelection - Created new game
	{
  "gameId": "6VDUIH",
  "firstPlayerUserId": 0,
  "connectionId": "4f7ae47b-7b3e-4655-9bff-9820a5598eb1",
  "dbSuccess": false
}
2025-04-05 17:13:52.640753 - Game[no-game] - Conn[4f7ae47b-7b3e-4655-9bff-9820a5598eb1]: UpdateFirstPlayerSelection - State updated
	{
  "gameId": "6VDUIH",
  "firstPlayerIsPlebs": true,
  "isPlebsTurn": true,
  "firstPlayerUserId": 0
}
2025-04-05 17:13:52.651845 - Game[no-game] - Conn[4f7ae47b-7b3e-4655-9bff-9820a5598eb1]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "6VDUIH",
  "userId": 26,
  "isSoloPlay": false,
  "connectionId": "4f7ae47b-7b3e-4655-9bff-9820a5598eb1",
  "gameExists": true,
  "connectionsExist": true
}
2025-04-05 17:13:52.652845 - Game[no-game] - Conn[4f7ae47b-7b3e-4655-9bff-9820a5598eb1]: JoinGame - Adding to userConnections
	{
  "connectionId": "4f7ae47b-7b3e-4655-9bff-9820a5598eb1",
  "userId": 26
}
2025-04-05 17:13:52.654845 - Game[no-game] - Conn[4f7ae47b-7b3e-4655-9bff-9820a5598eb1]: JoinGame - Game exists in memory - DETAILED
	{
  "gameId": "6VDUIH",
  "firstPlayerUserId": 0,
  "secondPlayerUserId": 0,
  "status": "AwaitingPlayers",
  "currentUserId": 26,
  "isReturningPlayer": false,
  "connectionExists": true,
  "connection1": "4f7ae47b-7b3e-4655-9bff-9820a5598eb1",
  "connection2": "null"
}
2025-04-05 17:13:52.656845 - Game[no-game] - Conn[4f7ae47b-7b3e-4655-9bff-9820a5598eb1]: JoinGame - Setting FirstPlayerUserId
	{
  "gameId": "6VDUIH",
  "firstPlayerUserId": 26
}
2025-04-05 17:13:52.658940 - Game[no-game] - Conn[4f7ae47b-7b3e-4655-9bff-9820a5598eb1]: JoinGame - Returning player detected - DETAILED
	{
  "gameId": "6VDUIH",
  "userId": 26,
  "isFirstPlayer": true,
  "isSecondPlayer": false
}
2025-04-05 17:13:52.677035 - Game[no-game] - Conn[4f7ae47b-7b3e-4655-9bff-9820a5598eb1]: ReconstructBoardState - No game found
	{
  "gameId": "6VDUIH"
}
2025-04-05 17:13:52.679654 - Game[no-game] - Conn[4f7ae47b-7b3e-4655-9bff-9820a5598eb1]: JoinGame - Before sending PlayerJoined
	{
  "gameId": "6VDUIH",
  "firstPlayerIsPlebs": true,
  "isPlebsTurn": true,
  "userId": 26,
  "firstPlayerUserId": 26,
  "isUserFirstPlayer": true
}
2025-04-05 17:13:52.680654 - Game[no-game] - Conn[4f7ae47b-7b3e-4655-9bff-9820a5598eb1]: JoinGame - Role calculation
	{
  "gameId": "6VDUIH",
  "userId": 26,
  "isFirstPlayer": true,
  "firstPlayerIsPlebs": true,
  "calculatedPlayerRole": "Plebs"
}
2025-04-05 17:13:59.557722 - Game[no-game] - Conn[3f4b34c1-abf3-43a0-8849-fdfc4d739f7f]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "6VDUIH",
  "userId": 1,
  "isSoloPlay": false,
  "connectionId": "3f4b34c1-abf3-43a0-8849-fdfc4d739f7f",
  "gameExists": true,
  "connectionsExist": true
}
2025-04-05 17:13:59.558726 - Game[no-game] - Conn[3f4b34c1-abf3-43a0-8849-fdfc4d739f7f]: JoinGame - Adding to userConnections
	{
  "connectionId": "3f4b34c1-abf3-43a0-8849-fdfc4d739f7f",
  "userId": 1
}
2025-04-05 17:13:59.560728 - Game[no-game] - Conn[3f4b34c1-abf3-43a0-8849-fdfc4d739f7f]: JoinGame - Game exists in memory - DETAILED
	{
  "gameId": "6VDUIH",
  "firstPlayerUserId": 26,
  "secondPlayerUserId": 0,
  "status": "AwaitingPlayers",
  "currentUserId": 1,
  "isReturningPlayer": false,
  "connectionExists": true,
  "connection1": "4f7ae47b-7b3e-4655-9bff-9820a5598eb1",
  "connection2": "null"
}
2025-04-05 17:13:59.562728 - Game[no-game] - Conn[3f4b34c1-abf3-43a0-8849-fdfc4d739f7f]: JoinGame - After rejoin check
	{
  "gameId": "6VDUIH",
  "userId": 1,
  "connectionsExist": true,
  "previousBlockExecuted": false
}
2025-04-05 17:13:59.564728 - Game[no-game] - Conn[3f4b34c1-abf3-43a0-8849-fdfc4d739f7f]: JoinGame - Second player joining
	{
  "gameId": "6VDUIH",
  "updateResult": true,
  "userId": 1
}
2025-04-05 17:13:59.567233 - Game[no-game] - Conn[no-connection]: DB_AddSecondPlayer starting
	{
  "gameId": "6VDUIH",
  "secondPlayerUserId": 1
}
2025-04-05 17:13:59.582232 - Game[no-game] - Conn[no-connection]: AddSecondPlayer database update
	{
  "gameId": "6VDUIH",
  "secondPlayerUserId": 1,
  "rowsAffected": 0
}
2025-04-05 17:13:59.583234 - Game[no-game] - Conn[no-connection]: AddSecondPlayer warning
	{
  "gameId": "6VDUIH",
  "secondPlayerUserId": 1,
  "message": "No rows were updated"
}
2025-04-05 17:13:59.588234 - Game[no-game] - Conn[3f4b34c1-abf3-43a0-8849-fdfc4d739f7f]: JoinGame - Second player joined successfully
	{
  "gameId": "6VDUIH",
  "userId": 1
}
2025-04-05 17:23:49.974268 - Game[no-game] - Conn[3f4b34c1-abf3-43a0-8849-fdfc4d739f7f]: OnDisconnected - Found games
	{
  "connectionId": "3f4b34c1-abf3-43a0-8849-fdfc4d739f7f",
  "gameCount": 0
}
2025-04-05 17:23:57.181363 - Game[no-game] - Conn[4f7ae47b-7b3e-4655-9bff-9820a5598eb1]: OnDisconnected - Found games
	{
  "connectionId": "4f7ae47b-7b3e-4655-9bff-9820a5598eb1",
  "gameCount": 0
}
2025-04-05 17:24:02.134285 - Game[no-game] - Conn[cf406c7b-7f72-4d15-bdd2-9ea0055c3dce]: UpdateFirstPlayerSelection - Starting
	{
  "gameId": "5I3WHB",
  "isPlebs": true
}
2025-04-05 17:24:02.136284 - Game[no-game] - Conn[no-connection]: StartNewGame has been called...5I3WHB False 0
2025-04-05 17:24:02.189030 - Game[no-game] - Conn[no-connection]: StartNewGame error
	{
  "error": "The INSERT statement conflicted with the FOREIGN KEY constraint \"FK_ChessGames_FirstPlayerUser\". The conflict occurred in database \"ElthosChess\", table \"dbo.ChessUsers\", column 'UserId'.\r\nThe statement has been terminated."
}
2025-04-05 17:24:02.191531 - Game[no-game] - Conn[cf406c7b-7f72-4d15-bdd2-9ea0055c3dce]: UpdateFirstPlayerSelection - Created new game
	{
  "gameId": "5I3WHB",
  "firstPlayerUserId": 0,
  "connectionId": "cf406c7b-7f72-4d15-bdd2-9ea0055c3dce",
  "dbSuccess": false
}
2025-04-05 17:24:02.193534 - Game[no-game] - Conn[cf406c7b-7f72-4d15-bdd2-9ea0055c3dce]: UpdateFirstPlayerSelection - State updated
	{
  "gameId": "5I3WHB",
  "firstPlayerIsPlebs": true,
  "isPlebsTurn": true,
  "firstPlayerUserId": 0
}
2025-04-05 17:24:02.205154 - Game[no-game] - Conn[cf406c7b-7f72-4d15-bdd2-9ea0055c3dce]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "5I3WHB",
  "userId": 26,
  "isSoloPlay": false,
  "connectionId": "cf406c7b-7f72-4d15-bdd2-9ea0055c3dce",
  "gameExists": true,
  "connectionsExist": true
}
2025-04-05 17:24:02.207154 - Game[no-game] - Conn[cf406c7b-7f72-4d15-bdd2-9ea0055c3dce]: JoinGame - Adding to userConnections
	{
  "connectionId": "cf406c7b-7f72-4d15-bdd2-9ea0055c3dce",
  "userId": 26
}
2025-04-05 17:24:02.208761 - Game[no-game] - Conn[cf406c7b-7f72-4d15-bdd2-9ea0055c3dce]: JoinGame - Game exists in memory - DETAILED
	{
  "gameId": "5I3WHB",
  "firstPlayerUserId": 0,
  "secondPlayerUserId": 0,
  "status": "AwaitingPlayers",
  "currentUserId": 26,
  "isReturningPlayer": false,
  "connectionExists": true,
  "connection1": "cf406c7b-7f72-4d15-bdd2-9ea0055c3dce",
  "connection2": "null"
}
2025-04-05 17:24:02.210764 - Game[no-game] - Conn[cf406c7b-7f72-4d15-bdd2-9ea0055c3dce]: JoinGame - Setting FirstPlayerUserId
	{
  "gameId": "5I3WHB",
  "firstPlayerUserId": 26
}
2025-04-05 17:24:02.212764 - Game[no-game] - Conn[cf406c7b-7f72-4d15-bdd2-9ea0055c3dce]: JoinGame - Returning player detected - DETAILED
	{
  "gameId": "5I3WHB",
  "userId": 26,
  "isFirstPlayer": true,
  "isSecondPlayer": false
}
2025-04-05 17:24:02.236870 - Game[no-game] - Conn[cf406c7b-7f72-4d15-bdd2-9ea0055c3dce]: ReconstructBoardState - No game found
	{
  "gameId": "5I3WHB"
}
2025-04-05 17:24:02.238870 - Game[no-game] - Conn[cf406c7b-7f72-4d15-bdd2-9ea0055c3dce]: JoinGame - Before sending PlayerJoined
	{
  "gameId": "5I3WHB",
  "firstPlayerIsPlebs": true,
  "isPlebsTurn": true,
  "userId": 26,
  "firstPlayerUserId": 26,
  "isUserFirstPlayer": true
}
2025-04-05 17:24:02.240872 - Game[no-game] - Conn[cf406c7b-7f72-4d15-bdd2-9ea0055c3dce]: JoinGame - Role calculation
	{
  "gameId": "5I3WHB",
  "userId": 26,
  "isFirstPlayer": true,
  "firstPlayerIsPlebs": true,
  "calculatedPlayerRole": "Plebs"
}
2025-04-05 17:24:06.201012 - Game[no-game] - Conn[b4e4df7b-6ca7-4ff2-b2f8-a2f36c0f8394]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "5I3WHB",
  "userId": 1,
  "isSoloPlay": false,
  "connectionId": "b4e4df7b-6ca7-4ff2-b2f8-a2f36c0f8394",
  "gameExists": true,
  "connectionsExist": true
}
2025-04-05 17:24:06.203012 - Game[no-game] - Conn[b4e4df7b-6ca7-4ff2-b2f8-a2f36c0f8394]: JoinGame - Adding to userConnections
	{
  "connectionId": "b4e4df7b-6ca7-4ff2-b2f8-a2f36c0f8394",
  "userId": 1
}
2025-04-05 17:24:06.204012 - Game[no-game] - Conn[b4e4df7b-6ca7-4ff2-b2f8-a2f36c0f8394]: JoinGame - Game exists in memory - DETAILED
	{
  "gameId": "5I3WHB",
  "firstPlayerUserId": 26,
  "secondPlayerUserId": 0,
  "status": "AwaitingPlayers",
  "currentUserId": 1,
  "isReturningPlayer": false,
  "connectionExists": true,
  "connection1": "cf406c7b-7f72-4d15-bdd2-9ea0055c3dce",
  "connection2": "null"
}
2025-04-05 17:24:06.206011 - Game[no-game] - Conn[b4e4df7b-6ca7-4ff2-b2f8-a2f36c0f8394]: JoinGame - After rejoin check
	{
  "gameId": "5I3WHB",
  "userId": 1,
  "connectionsExist": true,
  "previousBlockExecuted": false
}
2025-04-05 17:24:06.208596 - Game[no-game] - Conn[b4e4df7b-6ca7-4ff2-b2f8-a2f36c0f8394]: JoinGame - Second player joining
	{
  "gameId": "5I3WHB",
  "updateResult": true,
  "userId": 1
}
2025-04-05 17:24:06.209599 - Game[no-game] - Conn[no-connection]: DB_AddSecondPlayer starting
	{
  "gameId": "5I3WHB",
  "secondPlayerUserId": 1
}
2025-04-05 17:24:06.234150 - Game[no-game] - Conn[no-connection]: AddSecondPlayer database update
	{
  "gameId": "5I3WHB",
  "secondPlayerUserId": 1,
  "rowsAffected": 0
}
2025-04-05 17:24:06.236150 - Game[no-game] - Conn[no-connection]: AddSecondPlayer warning
	{
  "gameId": "5I3WHB",
  "secondPlayerUserId": 1,
  "message": "No rows were updated"
}
2025-04-05 17:24:06.240151 - Game[no-game] - Conn[b4e4df7b-6ca7-4ff2-b2f8-a2f36c0f8394]: JoinGame - Second player joined successfully
	{
  "gameId": "5I3WHB",
  "userId": 1
}
2025-04-05 17:45:45.887878 - Game[no-game] - Conn[b4e4df7b-6ca7-4ff2-b2f8-a2f36c0f8394]: OnDisconnected - Found games
	{
  "connectionId": "b4e4df7b-6ca7-4ff2-b2f8-a2f36c0f8394",
  "gameCount": 0
}
2025-04-05 17:45:50.119400 - Game[no-game] - Conn[cf406c7b-7f72-4d15-bdd2-9ea0055c3dce]: OnDisconnected - Found games
	{
  "connectionId": "cf406c7b-7f72-4d15-bdd2-9ea0055c3dce",
  "gameCount": 0
}
2025-04-05 17:46:12.278001 - Game[no-game] - Conn[1733f140-e057-47f7-a8a1-ca4ba6670df4]: UpdateFirstPlayerSelection - Starting
	{
  "gameId": "RKVXM7",
  "isPlebs": true
}
2025-04-05 17:46:12.280005 - Game[no-game] - Conn[no-connection]: StartNewGame has been called...RKVXM7 False 0
2025-04-05 17:46:12.323013 - Game[no-game] - Conn[no-connection]: StartNewGame error
	{
  "error": "Could not find stored procedure 'sp_ChessUser_GetOrCreate'."
}
2025-04-05 17:46:12.325013 - Game[no-game] - Conn[1733f140-e057-47f7-a8a1-ca4ba6670df4]: UpdateFirstPlayerSelection - Created new game
	{
  "gameId": "RKVXM7",
  "firstPlayerUserId": 0,
  "connectionId": "1733f140-e057-47f7-a8a1-ca4ba6670df4",
  "dbSuccess": false
}
2025-04-05 17:46:12.326013 - Game[no-game] - Conn[1733f140-e057-47f7-a8a1-ca4ba6670df4]: UpdateFirstPlayerSelection - State updated
	{
  "gameId": "RKVXM7",
  "firstPlayerIsPlebs": true,
  "isPlebsTurn": true,
  "firstPlayerUserId": 0
}
2025-04-05 17:46:12.336214 - Game[no-game] - Conn[1733f140-e057-47f7-a8a1-ca4ba6670df4]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "RKVXM7",
  "userId": 26,
  "isSoloPlay": false,
  "connectionId": "1733f140-e057-47f7-a8a1-ca4ba6670df4",
  "gameExists": true,
  "connectionsExist": true
}
2025-04-05 17:46:12.337214 - Game[no-game] - Conn[1733f140-e057-47f7-a8a1-ca4ba6670df4]: JoinGame - Adding to userConnections
	{
  "connectionId": "1733f140-e057-47f7-a8a1-ca4ba6670df4",
  "userId": 26
}
2025-04-05 17:46:12.339326 - Game[no-game] - Conn[1733f140-e057-47f7-a8a1-ca4ba6670df4]: JoinGame - Game exists in memory - DETAILED
	{
  "gameId": "RKVXM7",
  "firstPlayerUserId": 0,
  "secondPlayerUserId": 0,
  "status": "AwaitingPlayers",
  "currentUserId": 26,
  "isReturningPlayer": false,
  "connectionExists": true,
  "connection1": "1733f140-e057-47f7-a8a1-ca4ba6670df4",
  "connection2": "null"
}
2025-04-05 17:46:12.340328 - Game[no-game] - Conn[1733f140-e057-47f7-a8a1-ca4ba6670df4]: JoinGame - Setting FirstPlayerUserId
	{
  "gameId": "RKVXM7",
  "firstPlayerUserId": 26
}
2025-04-05 17:46:12.341330 - Game[no-game] - Conn[1733f140-e057-47f7-a8a1-ca4ba6670df4]: JoinGame - Returning player detected - DETAILED
	{
  "gameId": "RKVXM7",
  "userId": 26,
  "isFirstPlayer": true,
  "isSecondPlayer": false
}
2025-04-05 17:46:12.384215 - Game[no-game] - Conn[1733f140-e057-47f7-a8a1-ca4ba6670df4]: ReconstructBoardState - No game found
	{
  "gameId": "RKVXM7"
}
2025-04-05 17:46:12.385215 - Game[no-game] - Conn[1733f140-e057-47f7-a8a1-ca4ba6670df4]: JoinGame - Before sending PlayerJoined
	{
  "gameId": "RKVXM7",
  "firstPlayerIsPlebs": true,
  "isPlebsTurn": true,
  "userId": 26,
  "firstPlayerUserId": 26,
  "isUserFirstPlayer": true
}
2025-04-05 17:46:12.386215 - Game[no-game] - Conn[1733f140-e057-47f7-a8a1-ca4ba6670df4]: JoinGame - Role calculation
	{
  "gameId": "RKVXM7",
  "userId": 26,
  "isFirstPlayer": true,
  "firstPlayerIsPlebs": true,
  "calculatedPlayerRole": "Plebs"
}
2025-04-05 17:46:17.091346 - Game[no-game] - Conn[742bb74a-bf99-4652-919d-e66f893ee6bb]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "RKVXM7",
  "userId": 1,
  "isSoloPlay": false,
  "connectionId": "742bb74a-bf99-4652-919d-e66f893ee6bb",
  "gameExists": true,
  "connectionsExist": true
}
2025-04-05 17:46:17.092345 - Game[no-game] - Conn[742bb74a-bf99-4652-919d-e66f893ee6bb]: JoinGame - Adding to userConnections
	{
  "connectionId": "742bb74a-bf99-4652-919d-e66f893ee6bb",
  "userId": 1
}
2025-04-05 17:46:17.093345 - Game[no-game] - Conn[742bb74a-bf99-4652-919d-e66f893ee6bb]: JoinGame - Game exists in memory - DETAILED
	{
  "gameId": "RKVXM7",
  "firstPlayerUserId": 26,
  "secondPlayerUserId": 0,
  "status": "AwaitingPlayers",
  "currentUserId": 1,
  "isReturningPlayer": false,
  "connectionExists": true,
  "connection1": "1733f140-e057-47f7-a8a1-ca4ba6670df4",
  "connection2": "null"
}
2025-04-05 17:46:17.095345 - Game[no-game] - Conn[742bb74a-bf99-4652-919d-e66f893ee6bb]: JoinGame - After rejoin check
	{
  "gameId": "RKVXM7",
  "userId": 1,
  "connectionsExist": true,
  "previousBlockExecuted": false
}
2025-04-05 17:46:17.096345 - Game[no-game] - Conn[742bb74a-bf99-4652-919d-e66f893ee6bb]: JoinGame - Second player joining
	{
  "gameId": "RKVXM7",
  "updateResult": true,
  "userId": 1
}
2025-04-05 17:46:17.097959 - Game[no-game] - Conn[no-connection]: DB_AddSecondPlayer starting
	{
  "gameId": "RKVXM7",
  "secondPlayerUserId": 1
}
2025-04-05 17:46:17.144807 - Game[no-game] - Conn[no-connection]: AddSecondPlayer error
	{
  "gameId": "RKVXM7",
  "secondPlayerUserId": 1,
  "error": "Could not find stored procedure 'sp_ChessUser_GetOrCreate'.",
  "stackTrace": "   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)\r\n   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)\r\n   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)\r\n   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()\r\n   at System.Data.SqlClient.SqlDataReader.get_MetaData()\r\n   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)\r\n   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)\r\n   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)\r\n   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)\r\n   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)\r\n   at System.Data.SqlClient.SqlCommand.ExecuteReader()\r\n   at PvAChess.ChessGameLogger.DB_AddSecondPlayer(String gameId, Int32 secondPlayerUserId) in C:\\inetpub\\wwwroot\\PvAChess\\App_Code\\ChessGameLogger.vb:line 125"
}
2025-04-05 17:46:17.156312 - Game[no-game] - Conn[742bb74a-bf99-4652-919d-e66f893ee6bb]: JoinGame error
	{
  "error": "Could not find stored procedure 'sp_ChessUser_GetOrCreate'.",
  "stackTrace": "   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)\r\n   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)\r\n   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)\r\n   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()\r\n   at System.Data.SqlClient.SqlDataReader.get_MetaData()\r\n   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)\r\n   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)\r\n   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)\r\n   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)\r\n   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)\r\n   at System.Data.SqlClient.SqlCommand.ExecuteReader()\r\n   at PvAChess.ChessGameLogger.DB_AddSecondPlayer(String gameId, Int32 secondPlayerUserId) in C:\\inetpub\\wwwroot\\PvAChess\\App_Code\\ChessGameLogger.vb:line 183\r\n   at PvAChess.PvAChess.Hubs.GameHub.JoinGame(String gameId, Int32 userId, Boolean isSoloPlay) in C:\\inetpub\\wwwroot\\PvAChess\\Hubs\\GameHub.vb:line 278"
}
2025-04-05 17:46:17.160814 - Game[no-game] - Conn[742bb74a-bf99-4652-919d-e66f893ee6bb]: CheckGameExists
	{
  "gameId": "RKVXM7"
}
2025-04-05 17:52:06.721458 - Game[no-game] - Conn[1733f140-e057-47f7-a8a1-ca4ba6670df4]: OnDisconnected - Found games
	{
  "connectionId": "1733f140-e057-47f7-a8a1-ca4ba6670df4",
  "gameCount": 0
}
2025-04-05 17:52:08.793721 - Game[no-game] - Conn[742bb74a-bf99-4652-919d-e66f893ee6bb]: OnDisconnected - Found games
	{
  "connectionId": "742bb74a-bf99-4652-919d-e66f893ee6bb",
  "gameCount": 0
}
2025-04-05 17:54:12.963558 - Game[no-game] - Conn[db2ae541-b4b3-4f3e-91a7-ad7937a0bf11]: UpdateFirstPlayerSelection - Starting
	{
  "gameId": "YO8Y1S",
  "isPlebs": true
}
2025-04-05 17:54:12.965558 - Game[no-game] - Conn[no-connection]: StartNewGame has been called...YO8Y1S False 0
2025-04-05 17:54:12.966558 - Game[no-game] - Conn[no-connection]: Skipping user existence check
	{
  "userId": 0
}
2025-04-05 17:54:13.008080 - Game[no-game] - Conn[no-connection]: StartNewGame error
	{
  "error": "The INSERT statement conflicted with the FOREIGN KEY constraint \"FK_ChessGames_FirstPlayerUser\". The conflict occurred in database \"ElthosChess\", table \"dbo.ChessUsers\", column 'UserId'.\r\nThe statement has been terminated."
}
2025-04-05 17:54:13.009585 - Game[no-game] - Conn[db2ae541-b4b3-4f3e-91a7-ad7937a0bf11]: UpdateFirstPlayerSelection - Created new game
	{
  "gameId": "YO8Y1S",
  "firstPlayerUserId": 0,
  "connectionId": "db2ae541-b4b3-4f3e-91a7-ad7937a0bf11",
  "dbSuccess": false
}
2025-04-05 17:54:13.010585 - Game[no-game] - Conn[db2ae541-b4b3-4f3e-91a7-ad7937a0bf11]: UpdateFirstPlayerSelection - State updated
	{
  "gameId": "YO8Y1S",
  "firstPlayerIsPlebs": true,
  "isPlebsTurn": true,
  "firstPlayerUserId": 0
}
2025-04-05 17:54:13.020692 - Game[no-game] - Conn[db2ae541-b4b3-4f3e-91a7-ad7937a0bf11]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "YO8Y1S",
  "userId": 26,
  "isSoloPlay": false,
  "connectionId": "db2ae541-b4b3-4f3e-91a7-ad7937a0bf11",
  "gameExists": true,
  "connectionsExist": true
}
2025-04-05 17:54:13.022692 - Game[no-game] - Conn[db2ae541-b4b3-4f3e-91a7-ad7937a0bf11]: JoinGame - Adding to userConnections
	{
  "connectionId": "db2ae541-b4b3-4f3e-91a7-ad7937a0bf11",
  "userId": 26
}
2025-04-05 17:54:13.023692 - Game[no-game] - Conn[db2ae541-b4b3-4f3e-91a7-ad7937a0bf11]: JoinGame - Game exists in memory - DETAILED
	{
  "gameId": "YO8Y1S",
  "firstPlayerUserId": 0,
  "secondPlayerUserId": 0,
  "status": "AwaitingPlayers",
  "currentUserId": 26,
  "isReturningPlayer": false,
  "connectionExists": true,
  "connection1": "db2ae541-b4b3-4f3e-91a7-ad7937a0bf11",
  "connection2": "null"
}
2025-04-05 17:54:13.024692 - Game[no-game] - Conn[db2ae541-b4b3-4f3e-91a7-ad7937a0bf11]: JoinGame - Setting FirstPlayerUserId
	{
  "gameId": "YO8Y1S",
  "firstPlayerUserId": 26
}
2025-04-05 17:54:13.026692 - Game[no-game] - Conn[db2ae541-b4b3-4f3e-91a7-ad7937a0bf11]: JoinGame - Returning player detected - DETAILED
	{
  "gameId": "YO8Y1S",
  "userId": 26,
  "isFirstPlayer": true,
  "isSecondPlayer": false
}
2025-04-05 17:54:13.046378 - Game[no-game] - Conn[db2ae541-b4b3-4f3e-91a7-ad7937a0bf11]: ReconstructBoardState - No game found
	{
  "gameId": "YO8Y1S"
}
2025-04-05 17:54:13.048461 - Game[no-game] - Conn[db2ae541-b4b3-4f3e-91a7-ad7937a0bf11]: JoinGame - Before sending PlayerJoined
	{
  "gameId": "YO8Y1S",
  "firstPlayerIsPlebs": true,
  "isPlebsTurn": true,
  "userId": 26,
  "firstPlayerUserId": 26,
  "isUserFirstPlayer": true
}
2025-04-05 17:54:13.049463 - Game[no-game] - Conn[db2ae541-b4b3-4f3e-91a7-ad7937a0bf11]: JoinGame - Role calculation
	{
  "gameId": "YO8Y1S",
  "userId": 26,
  "isFirstPlayer": true,
  "firstPlayerIsPlebs": true,
  "calculatedPlayerRole": "Plebs"
}
2025-04-05 17:54:17.738290 - Game[no-game] - Conn[f8767c5d-ecb9-4520-812f-3a004b9b479f]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "YO8Y1S",
  "userId": 1,
  "isSoloPlay": false,
  "connectionId": "f8767c5d-ecb9-4520-812f-3a004b9b479f",
  "gameExists": true,
  "connectionsExist": true
}
2025-04-05 17:54:17.740290 - Game[no-game] - Conn[f8767c5d-ecb9-4520-812f-3a004b9b479f]: JoinGame - Adding to userConnections
	{
  "connectionId": "f8767c5d-ecb9-4520-812f-3a004b9b479f",
  "userId": 1
}
2025-04-05 17:54:17.741291 - Game[no-game] - Conn[f8767c5d-ecb9-4520-812f-3a004b9b479f]: JoinGame - Game exists in memory - DETAILED
	{
  "gameId": "YO8Y1S",
  "firstPlayerUserId": 26,
  "secondPlayerUserId": 0,
  "status": "AwaitingPlayers",
  "currentUserId": 1,
  "isReturningPlayer": false,
  "connectionExists": true,
  "connection1": "db2ae541-b4b3-4f3e-91a7-ad7937a0bf11",
  "connection2": "null"
}
2025-04-05 17:54:17.742290 - Game[no-game] - Conn[f8767c5d-ecb9-4520-812f-3a004b9b479f]: JoinGame - After rejoin check
	{
  "gameId": "YO8Y1S",
  "userId": 1,
  "connectionsExist": true,
  "previousBlockExecuted": false
}
2025-04-05 17:54:17.744290 - Game[no-game] - Conn[f8767c5d-ecb9-4520-812f-3a004b9b479f]: JoinGame - Second player joining
	{
  "gameId": "YO8Y1S",
  "updateResult": true,
  "userId": 1
}
2025-04-05 17:54:17.745792 - Game[no-game] - Conn[no-connection]: DB_AddSecondPlayer starting
	{
  "gameId": "YO8Y1S",
  "secondPlayerUserId": 1
}
2025-04-05 17:54:17.745792 - Game[no-game] - Conn[no-connection]: Skipping second player user existence check
	{
  "userId": 1
}
2025-04-05 17:54:17.763298 - Game[no-game] - Conn[no-connection]: AddSecondPlayer database update
	{
  "gameId": "YO8Y1S",
  "secondPlayerUserId": 1,
  "rowsAffected": 0
}
2025-04-05 17:54:17.807805 - Game[no-game] - Conn[no-connection]: AddSecondPlayer error
	{
  "gameId": "YO8Y1S",
  "secondPlayerUserId": 1,
  "error": "The INSERT statement conflicted with the FOREIGN KEY constraint \"FK_ChessGames_FirstPlayerUser\". The conflict occurred in database \"ElthosChess\", table \"dbo.ChessUsers\", column 'UserId'.\r\nThe statement has been terminated.",
  "stackTrace": "   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)\r\n   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)\r\n   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)\r\n   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)\r\n   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)\r\n   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)\r\n   at System.Data.SqlClient.SqlCommand.InternalExecuteNonQuery(TaskCompletionSource`1 completion, String methodName, Boolean sendToPipe, Int32 timeout, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)\r\n   at System.Data.SqlClient.SqlCommand.ExecuteNonQuery()\r\n   at PvAChess.ChessGameLogger.DB_AddSecondPlayer(String gameId, Int32 secondPlayerUserId) in C:\\inetpub\\wwwroot\\PvAChess\\App_Code\\ChessGameLogger.vb:line 138"
}
2025-04-05 17:54:17.818309 - Game[no-game] - Conn[f8767c5d-ecb9-4520-812f-3a004b9b479f]: JoinGame - Error updating database with second player
	{
  "gameId": "YO8Y1S",
  "userId": 1,
  "error": "The INSERT statement conflicted with the FOREIGN KEY constraint \"FK_ChessGames_FirstPlayerUser\". The conflict occurred in database \"ElthosChess\", table \"dbo.ChessUsers\", column 'UserId'.\r\nThe statement has been terminated."
}
2025-04-05 17:54:17.822309 - Game[no-game] - Conn[f8767c5d-ecb9-4520-812f-3a004b9b479f]: JoinGame - Second player joined successfully
	{
  "gameId": "YO8Y1S",
  "userId": 1
}
2025-04-05 19:09:38.833417 - Game[no-game] - Conn[db2ae541-b4b3-4f3e-91a7-ad7937a0bf11]: OnDisconnected - Found games
	{
  "connectionId": "db2ae541-b4b3-4f3e-91a7-ad7937a0bf11",
  "gameCount": 0
}
2025-04-05 19:09:42.420490 - Game[no-game] - Conn[9ca4c46a-2e7e-4231-8b48-90a843860e09]: UpdateFirstPlayerSelection - Starting
	{
  "gameId": "WDFDSO",
  "isPlebs": true
}
2025-04-05 19:09:42.423491 - Game[no-game] - Conn[9ca4c46a-2e7e-4231-8b48-90a843860e09]: UpdateFirstPlayerSelection - Using userId parameter
	{
  "gameId": "WDFDSO",
  "userId": 26
}
2025-04-05 19:09:42.425490 - Game[no-game] - Conn[no-connection]: StartNewGame has been called...WDFDSO False 26
2025-04-05 19:09:42.471402 - Game[no-game] - Conn[no-connection]: StartNewGame error
	{
  "error": "Could not find stored procedure 'sp_ChessGame_StartWithUserCreation'."
}
2025-04-05 19:09:42.472904 - Game[no-game] - Conn[9ca4c46a-2e7e-4231-8b48-90a843860e09]: UpdateFirstPlayerSelection - Created new game
	{
  "gameId": "WDFDSO",
  "firstPlayerUserId": 26,
  "connectionId": "9ca4c46a-2e7e-4231-8b48-90a843860e09",
  "dbSuccess": false
}
2025-04-05 19:09:42.474906 - Game[no-game] - Conn[9ca4c46a-2e7e-4231-8b48-90a843860e09]: UpdateFirstPlayerSelection - State updated
	{
  "gameId": "WDFDSO",
  "firstPlayerIsPlebs": true,
  "isPlebsTurn": true,
  "firstPlayerUserId": 26
}
2025-04-05 19:09:42.485985 - Game[no-game] - Conn[9ca4c46a-2e7e-4231-8b48-90a843860e09]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "WDFDSO",
  "userId": 26,
  "isSoloPlay": false,
  "connectionId": "9ca4c46a-2e7e-4231-8b48-90a843860e09",
  "gameExists": true,
  "connectionsExist": true
}
2025-04-05 19:09:42.488061 - Game[no-game] - Conn[9ca4c46a-2e7e-4231-8b48-90a843860e09]: JoinGame - Adding to userConnections
	{
  "connectionId": "9ca4c46a-2e7e-4231-8b48-90a843860e09",
  "userId": 26
}
2025-04-05 19:09:42.489063 - Game[no-game] - Conn[9ca4c46a-2e7e-4231-8b48-90a843860e09]: JoinGame - Game exists in memory - DETAILED
	{
  "gameId": "WDFDSO",
  "firstPlayerUserId": 26,
  "secondPlayerUserId": 0,
  "status": "AwaitingPlayers",
  "currentUserId": 26,
  "isReturningPlayer": true,
  "connectionExists": true,
  "connection1": "9ca4c46a-2e7e-4231-8b48-90a843860e09",
  "connection2": "null"
}
2025-04-05 19:09:42.491065 - Game[no-game] - Conn[9ca4c46a-2e7e-4231-8b48-90a843860e09]: JoinGame - Returning player detected - DETAILED
	{
  "gameId": "WDFDSO",
  "userId": 26,
  "isFirstPlayer": true,
  "isSecondPlayer": false
}
2025-04-05 19:09:42.507760 - Game[no-game] - Conn[9ca4c46a-2e7e-4231-8b48-90a843860e09]: ReconstructBoardState - No game found
	{
  "gameId": "WDFDSO"
}
2025-04-05 19:09:42.509764 - Game[no-game] - Conn[9ca4c46a-2e7e-4231-8b48-90a843860e09]: JoinGame - Before sending PlayerJoined
	{
  "gameId": "WDFDSO",
  "firstPlayerIsPlebs": true,
  "isPlebsTurn": true,
  "userId": 26,
  "firstPlayerUserId": 26,
  "isUserFirstPlayer": true
}
2025-04-05 19:09:42.511764 - Game[no-game] - Conn[9ca4c46a-2e7e-4231-8b48-90a843860e09]: JoinGame - Role calculation
	{
  "gameId": "WDFDSO",
  "userId": 26,
  "isFirstPlayer": true,
  "firstPlayerIsPlebs": true,
  "calculatedPlayerRole": "Plebs"
}
2025-04-05 19:09:47.740982 - Game[no-game] - Conn[95bf7717-e1b5-4b75-bbcc-dfd59d74f4f2]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "WDFDSO",
  "userId": 1,
  "isSoloPlay": false,
  "connectionId": "95bf7717-e1b5-4b75-bbcc-dfd59d74f4f2",
  "gameExists": true,
  "connectionsExist": true
}
2025-04-05 19:09:47.742983 - Game[no-game] - Conn[95bf7717-e1b5-4b75-bbcc-dfd59d74f4f2]: JoinGame - Adding to userConnections
	{
  "connectionId": "95bf7717-e1b5-4b75-bbcc-dfd59d74f4f2",
  "userId": 1
}
2025-04-05 19:09:47.743983 - Game[no-game] - Conn[95bf7717-e1b5-4b75-bbcc-dfd59d74f4f2]: JoinGame - Game exists in memory - DETAILED
	{
  "gameId": "WDFDSO",
  "firstPlayerUserId": 26,
  "secondPlayerUserId": 0,
  "status": "AwaitingPlayers",
  "currentUserId": 1,
  "isReturningPlayer": false,
  "connectionExists": true,
  "connection1": "9ca4c46a-2e7e-4231-8b48-90a843860e09",
  "connection2": "null"
}
2025-04-05 19:09:47.745983 - Game[no-game] - Conn[95bf7717-e1b5-4b75-bbcc-dfd59d74f4f2]: JoinGame - After rejoin check
	{
  "gameId": "WDFDSO",
  "userId": 1,
  "connectionsExist": true,
  "previousBlockExecuted": false
}
2025-04-05 19:09:47.748561 - Game[no-game] - Conn[95bf7717-e1b5-4b75-bbcc-dfd59d74f4f2]: JoinGame - Second player joining
	{
  "gameId": "WDFDSO",
  "updateResult": true,
  "userId": 1
}
2025-04-05 19:09:47.750564 - Game[no-game] - Conn[no-connection]: DB_AddSecondPlayer starting
	{
  "gameId": "WDFDSO",
  "secondPlayerUserId": 1
}
2025-04-05 19:09:47.796575 - Game[no-game] - Conn[no-connection]: AddSecondPlayer error
	{
  "gameId": "WDFDSO",
  "secondPlayerUserId": 1,
  "error": "Could not find stored procedure 'sp_ChessGame_AddSecondPlayerWithUserCreation'.",
  "stackTrace": "   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)\r\n   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)\r\n   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)\r\n   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)\r\n   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)\r\n   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)\r\n   at System.Data.SqlClient.SqlCommand.InternalExecuteNonQuery(TaskCompletionSource`1 completion, String methodName, Boolean sendToPipe, Int32 timeout, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)\r\n   at System.Data.SqlClient.SqlCommand.ExecuteNonQuery()\r\n   at PvAChess.ChessGameLogger.DB_AddSecondPlayer(String gameId, Int32 secondPlayerUserId) in C:\\inetpub\\wwwroot\\PvAChess\\App_Code\\ChessGameLogger.vb:line 114"
}
2025-04-05 19:09:47.811080 - Game[no-game] - Conn[95bf7717-e1b5-4b75-bbcc-dfd59d74f4f2]: JoinGame - Error updating database with second player
	{
  "gameId": "WDFDSO",
  "userId": 1,
  "error": "Could not find stored procedure 'sp_ChessGame_AddSecondPlayerWithUserCreation'."
}
2025-04-05 19:09:47.815080 - Game[no-game] - Conn[95bf7717-e1b5-4b75-bbcc-dfd59d74f4f2]: JoinGame - Second player joined successfully
	{
  "gameId": "WDFDSO",
  "userId": 1
}
2025-04-05 19:10:11.016523 - Game[no-game] - Conn[f8767c5d-ecb9-4520-812f-3a004b9b479f]: OnDisconnected - Found games
	{
  "connectionId": "f8767c5d-ecb9-4520-812f-3a004b9b479f",
  "gameCount": 0
}
2025-04-29 23:11:54.805920 - Game[no-game] - Conn[f4862c9a-baaf-4010-aa00-cdfbcaf692aa]: OnDisconnected - Found games
	{
  "connectionId": "f4862c9a-baaf-4010-aa00-cdfbcaf692aa",
  "gameCount": 0
}
2025-05-26 17:24:39.413808 - Game[no-game] - Conn[ae6d48f4-0608-4e83-abc5-ee052ddbdf12]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "5WYHAR",
  "userId": 26,
  "isSoloPlay": false,
  "connectionId": "ae6d48f4-0608-4e83-abc5-ee052ddbdf12",
  "gameExists": false,
  "connectionsExist": false
}
2025-05-26 17:24:39.413808 - Game[no-game] - Conn[ae6d48f4-0608-4e83-abc5-ee052ddbdf12]: JoinGame - Adding to userConnections
	{
  "connectionId": "ae6d48f4-0608-4e83-abc5-ee052ddbdf12",
  "userId": 26
}
2025-05-26 17:24:39.413808 - Game[no-game] - Conn[ae6d48f4-0608-4e83-abc5-ee052ddbdf12]: JoinGame - Game not in memory, checking database
	{
  "gameId": "5WYHAR",
  "userId": 26
}
2025-05-26 17:24:39.429815 - Game[no-game] - Conn[ae6d48f4-0608-4e83-abc5-ee052ddbdf12]: ReconstructBoardState - No game found
	{
  "gameId": "5WYHAR"
}
2025-05-26 17:24:39.429815 - Game[no-game] - Conn[ae6d48f4-0608-4e83-abc5-ee052ddbdf12]: JoinGame - Found game in database, restoring
	{
  "gameId": "5WYHAR",
  "firstPlayerUserId": 0,
  "firstPlayerIsPlebs": true
}
2025-05-26 17:24:39.449946 - Game[no-game] - Conn[ae6d48f4-0608-4e83-abc5-ee052ddbdf12]: JoinGame - Connections check
	{
  "gameId": "5WYHAR",
  "connectionsExists": true,
  "userId": 26
}
2025-05-26 17:25:37.845221 - Game[no-game] - Conn[ae6d48f4-0608-4e83-abc5-ee052ddbdf12]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "5WYHAR",
  "userId": 26,
  "isSoloPlay": false,
  "connectionId": "ae6d48f4-0608-4e83-abc5-ee052ddbdf12",
  "gameExists": true,
  "connectionsExist": true
}
2025-05-26 17:25:37.847221 - Game[no-game] - Conn[ae6d48f4-0608-4e83-abc5-ee052ddbdf12]: JoinGame - Game exists in memory - DETAILED
	{
  "gameId": "5WYHAR",
  "firstPlayerUserId": 0,
  "secondPlayerUserId": 0,
  "status": "AwaitingPlayers",
  "currentUserId": 26,
  "isReturningPlayer": false,
  "connectionExists": true,
  "connection1": "null",
  "connection2": "ae6d48f4-0608-4e83-abc5-ee052ddbdf12"
}
2025-05-26 17:25:37.849881 - Game[no-game] - Conn[ae6d48f4-0608-4e83-abc5-ee052ddbdf12]: JoinGame - Setting FirstPlayerUserId
	{
  "gameId": "5WYHAR",
  "firstPlayerUserId": 26
}
2025-05-26 17:25:37.851880 - Game[no-game] - Conn[ae6d48f4-0608-4e83-abc5-ee052ddbdf12]: JoinGame - Returning player detected - DETAILED
	{
  "gameId": "5WYHAR",
  "userId": 26,
  "isFirstPlayer": true,
  "isSecondPlayer": false
}
2025-05-26 17:25:37.867783 - Game[no-game] - Conn[ae6d48f4-0608-4e83-abc5-ee052ddbdf12]: ReconstructBoardState - No game found
	{
  "gameId": "5WYHAR"
}
2025-05-26 17:25:37.869857 - Game[no-game] - Conn[ae6d48f4-0608-4e83-abc5-ee052ddbdf12]: JoinGame - Before sending PlayerJoined
	{
  "gameId": "5WYHAR",
  "firstPlayerIsPlebs": true,
  "isPlebsTurn": true,
  "userId": 26,
  "firstPlayerUserId": 26,
  "isUserFirstPlayer": true
}
2025-05-26 17:25:37.871900 - Game[no-game] - Conn[ae6d48f4-0608-4e83-abc5-ee052ddbdf12]: JoinGame - Role calculation
	{
  "gameId": "5WYHAR",
  "userId": 26,
  "isFirstPlayer": true,
  "firstPlayerIsPlebs": true,
  "calculatedPlayerRole": "Plebs"
}
2025-05-26 17:25:56.627007 - Game[no-game] - Conn[01e95070-c11d-4bbf-bb1d-c5dac3a58402]: JoinGame - Starting with FULL DETAILS
	{
  "gameId": "5WYHAR",
  "userId": 1,
  "isSoloPlay": false,
  "connectionId": "01e95070-c11d-4bbf-bb1d-c5dac3a58402",
  "gameExists": true,
  "connectionsExist": true
}
2025-05-26 17:25:56.643137 - Game[no-game] - Conn[01e95070-c11d-4bbf-bb1d-c5dac3a58402]: JoinGame - Adding to userConnections
	{
  "connectionId": "01e95070-c11d-4bbf-bb1d-c5dac3a58402",
  "userId": 1
}
2025-05-26 17:25:56.645785 - Game[no-game] - Conn[01e95070-c11d-4bbf-bb1d-c5dac3a58402]: JoinGame - Game exists in memory - DETAILED
	{
  "gameId": "5WYHAR",
  "firstPlayerUserId": 26,
  "secondPlayerUserId": 0,
  "status": "AwaitingPlayers",
  "currentUserId": 1,
  "isReturningPlayer": false,
  "connectionExists": true,
  "connection1": "ae6d48f4-0608-4e83-abc5-ee052ddbdf12",
  "connection2": "ae6d48f4-0608-4e83-abc5-ee052ddbdf12"
}
2025-05-26 17:25:56.647785 - Game[no-game] - Conn[01e95070-c11d-4bbf-bb1d-c5dac3a58402]: JoinGame - After rejoin check
	{
  "gameId": "5WYHAR",
  "userId": 1,
  "connectionsExist": true,
  "previousBlockExecuted": false
}
2025-05-26 17:25:56.649785 - Game[no-game] - Conn[01e95070-c11d-4bbf-bb1d-c5dac3a58402]: JoinGame - Game already has two players
	{
  "gameId": "5WYHAR",
  "connection1": "ae6d48f4-0608-4e83-abc5-ee052ddbdf12",
  "connection2": "ae6d48f4-0608-4e83-abc5-ee052ddbdf12",
  "firstPlayerUserId": 26,
  "secondPlayerUserId": 0,
  "currentUserId": 1
}
2025-05-26 17:25:56.653785 - Game[no-game] - Conn[01e95070-c11d-4bbf-bb1d-c5dac3a58402]: CheckGameExists
	{
  "gameId": "5WYHAR"
}
2025-05-26 17:26:32.770790 - Game[no-game] - Conn[01e95070-c11d-4bbf-bb1d-c5dac3a58402]: OnDisconnected - Found games
	{
  "connectionId": "01e95070-c11d-4bbf-bb1d-c5dac3a58402",
  "gameCount": 0
}
