USE [Elth<PERSON><PERSON>hes<PERSON>]
GO
/****** Object:  StoredProcedure [dbo].[sp_ChessGame_GetOverallStats]    Script Date: 4/5/2025 1:17:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- ALTER PROCEDURE for Overall Stats

CREATE PROCEDURE [dbo].[sp_ChessGame_GetOverallStats]
AS
BEGIN
    SET NOCOUNT ON;

    WITH PlayerGames AS (
        SELECT 
            p.UserId,
            p.DisplayName,
            -- Total Completed Games
            COUNT(DISTINCT CASE 
                WHEN g.EndTime IS NOT NULL 
                AND g.EndGameUserID IS NOT NULL
                AND (g.FirstPlayerUserId = p.UserId OR g.SecondPlayerUserId = p.UserId)
                THEN g.GameId 
            END) as TotalGames,
            
            -- Games Won
            COUNT(DISTINCT CASE 
                WHEN g.EndGameUserID IS NOT NULL AND (
                    -- Win by opponent resignation (EndG<PERSON><PERSON><PERSON><PERSON> is the resigning player)
                    (g.EndGameAction LIKE '%Resigned%' AND g.EndGameUserID <> p.UserId 
                     AND (g.FirstPlayerUserId = p.UserId OR g.SecondPlayerUserId = p.UserId))
                    OR
                    -- Direct win (EndGameUserID is the winning player)
                    (g.EndGameAction NOT LIKE '%Resigned%' AND g.EndGameUserID = p.UserId)
                )
                THEN g.GameId
            END) as GamesWon,
            
            -- Multiplayer Games
            COUNT(DISTINCT CASE 
                WHEN g.IsSoloPlay = 0 
                AND g.EndTime IS NOT NULL 
                AND g.EndGameUserID IS NOT NULL
                AND (g.FirstPlayerUserId = p.UserId OR g.SecondPlayerUserId = p.UserId)
                THEN g.GameId 
            END) as MultiplayerGames,
            
            -- Solo Games
            COUNT(DISTINCT CASE 
                WHEN g.IsSoloPlay = 1 
                AND g.EndTime IS NOT NULL 
                AND g.EndGameUserID IS NOT NULL
                AND g.FirstPlayerUserId = p.UserId 
                THEN g.GameId 
            END) as SoloGames
        FROM ChessUsers p
        LEFT JOIN ChessGames g ON p.UserId = g.FirstPlayerUserId OR p.UserId = g.SecondPlayerUserId
        WHERE p.DisplayName NOT LIKE '%Test%'
        GROUP BY p.UserId, p.DisplayName
    )
    SELECT 
        DisplayName,
        TotalGames,
        GamesWon,
        CASE 
            WHEN TotalGames > 0 THEN CAST(CAST(GamesWon AS FLOAT) / CAST(TotalGames AS FLOAT) * 100 AS DECIMAL(5,2))
            ELSE 0 
        END as WinPercentage,
        MultiplayerGames,
        SoloGames
    FROM PlayerGames
    WHERE TotalGames > 0
    ORDER BY WinPercentage DESC, TotalGames DESC;
END
GO
