ALTER PROCEDURE [dbo].[sp_ChessGame_GetOverallStats]
AS
   BEGIN
       SET NOCOUNT ON;
   
      WITH PlayerGames AS (
          SELECT 
              p.UserId,
              p.DisplayName,
              COUNT(DISTINCT CASE WHEN g.FirstPlayerUserId = p.UserId OR g.SecondPlayerUserId = p.UserId THEN g.GameId END) as TotalGames,
              COUNT(DISTINCT CASE WHEN g.Winner = p.DisplayName THEN g.GameId END) as GamesWon,
              COUNT(DISTINCT CASE WHEN g.IsSoloPlay = 0 AND (g.FirstPlayerUserId = p.UserId OR g.SecondPlayerUserId = p.UserId) THEN g.GameId END) as MultiplayerGames,
              COUNT(DISTINCT CASE WHEN g.IsSoloPlay = 1 AND g.FirstPlayerUserId = p.UserId THEN g.GameId END) as SoloGames
          FROM ChessUsers p
          LEFT JOIN ChessGames g ON p.UserId = g.FirstPlayerUserId OR p.UserId = g.SecondPlayerUserId
          GROUP BY p.UserId, p.DisplayName
      )
      SELECT 
          DisplayName,
          TotalGames,
          GamesWon,
          CASE 
              WHEN TotalGames > 0 THEN CAST(CAST(GamesWon AS FLOAT) / CAST(TotalGames AS FLOAT) * 100 AS DECIMAL(5,2))
              ELSE 0 
          END as WinPercentage,
          MultiplayerGames,
          SoloGames
      FROM PlayerGames
      WHERE TotalGames > 0
      ORDER BY WinPercentage DESC, TotalGames DESC;
   END

ALTER PROCEDURE [dbo].[sp_ChessGame_GetAggressiveStats]
AS
BEGIN
    SET NOCOUNT ON;

   -- 2. Most Aggressive Players (Based on Captures)
   WITH PlayerCaptures AS (
       SELECT 
           p.UserId,
           p.DisplayName,
           COUNT(CASE WHEN m.PieceCaptured <> '' THEN 1 END) as TotalCaptures,
           COUNT(DISTINCT m.GameId) as GamesPlayed
       FROM ChessUsers p
       JOIN ChessGames g ON p.UserId = g.FirstPlayerUserId OR p.UserId = g.SecondPlayerUserId
       JOIN ChessMoves m ON g.GameId = m.GameId AND p.UserId = m.UserId
       GROUP BY p.UserId, p.DisplayName
   )
   SELECT 
       DisplayName,
       TotalCaptures,
       GamesPlayed,
       CAST(CAST(TotalCaptures AS FLOAT) / CAST(GamesPlayed AS FLOAT) AS DECIMAL(5,2)) as CapturesPerGame
   FROM PlayerCaptures
   WHERE GamesPlayed > 0
   ORDER BY CapturesPerGame DESC;
END

-- 3. Role Performance (Plebs vs Aristoi)

 
ALTER PROCEDURE [dbo].[sp_ChessGame_RolePerformance]
AS
BEGIN
   SET NOCOUNT ON;

   WITH RoleStats AS (
       SELECT 
           p.UserId,
           p.DisplayName,
           m.PlayerRole,
           COUNT(DISTINCT m.GameId) as GamesInRole,
           COUNT(CASE WHEN m.PieceCaptured <> '' THEN 1 END) as CapturesInRole
       FROM ChessUsers p
       JOIN ChessMoves m ON p.UserId = m.UserId
       GROUP BY p.UserId, p.DisplayName, m.PlayerRole
   )
   SELECT 
       DisplayName,
       PlayerRole,
       GamesInRole,
       CapturesInRole,
       CAST(CAST(CapturesInRole AS FLOAT) / CAST(GamesInRole AS FLOAT) AS DECIMAL(5,2)) as CapturesPerGame
   FROM RoleStats
   ORDER BY PlayerRole, CapturesPerGame DESC;
END

-- ALTER PROCEDURE for Recent Activity
ALTER PROCEDURE [dbo].[sp_ChessGame_GetRecentStats]
AS
BEGIN
    SET NOCOUNT ON;
   SELECT 
       p.DisplayName,
       COUNT(DISTINCT g.GameId) as RecentGames,
       COUNT(DISTINCT CASE WHEN g.Winner = p.DisplayName THEN g.GameId END) as RecentWins
   FROM ChessUsers p
   JOIN ChessGames g ON (p.UserId = g.FirstPlayerUserId OR p.UserId = g.SecondPlayerUserId)
   WHERE g.StartTime >= DATEADD(day, -30, GETDATE())
   GROUP BY p.DisplayName
   HAVING COUNT(DISTINCT g.GameId) > 0
   ORDER BY RecentGames DESC, RecentWins DESC;
END 


-- 5. Fastest Winners (Completed Games Only)

-- ALTER PROCEDURE for Recent Activity
ALTER PROCEDURE [dbo].[sp_ChessGame_FastestWinners]
AS
BEGIN 
   SELECT TOP 10
       p.DisplayName as Winner,
       g.GameId,
       DATEDIFF(minute, g.StartTime, g.EndTime) as GameDurationMinutes,
       CASE WHEN g.IsSoloPlay = 1 THEN 'Solo' ELSE 'Multiplayer' END as GameType
   FROM ChessGames g
   JOIN ChessUsers p ON p.DisplayName = g.Winner
   WHERE g.EndTime IS NOT NULL 
       AND g.Winner NOT IN ('Abandoned', 'Draw')
   ORDER BY GameDurationMinutes ASC;
END