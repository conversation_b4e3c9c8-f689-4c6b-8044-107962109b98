Option Strict On
Option Explicit On

Imports Microsoft.AspNet.SignalR
Imports Microsoft.AspNet.SignalR.Hubs
Imports System.Threading.Tasks
Imports System.Collections.Concurrent

Namespace PvAChess.Common.Hubs
    Public Interface ISignalRCoreHub
        Function JoinGroup(groupName As String) As Task
    End Interface

    Public MustInherit Class SignalRCoreHub(Of T As Class)
        Inherits Hub(Of T)
        Implements ISignalRCoreHub

        ' Shared across all hubs
        Public Shared ReadOnly Connections As New ConcurrentDictionary(Of String, PvAChess.Common.Models.ConnectionInfo)
        Protected Shared ReadOnly userConnections As New ConcurrentDictionary(Of String, Integer)
        
        Public Overridable Function JoinGroup(groupName As String) As Task Implements ISignalRCoreHub.JoinGroup
            LoggingHandler.LogToFile("Group join attempt", New With {
                .ConnectionId = Context.ConnectionId,
                .Group = groupName
            })
            Return Groups.Add(Context.ConnectionId, groupName)
        End Function

        Public Overrides Function OnConnected() As Task
            Connections.TryAdd(Context.ConnectionId, New PvAChess.Common.Models.ConnectionInfo With {
                .UserId = GetUserId(),
                .ConnectedAt = DateTime.UtcNow
            })
            Return MyBase.OnConnected()
        End Function

        Public Overrides Function OnDisconnected(stopCalled As Boolean) As Task
            Dim temp As PvAChess.Common.Models.ConnectionInfo = Nothing
            Connections.TryRemove(Context.ConnectionId, temp)
            Return MyBase.OnDisconnected(stopCalled)
        End Function

        Public Overrides Function OnReconnected() As Task
            Return MyBase.OnReconnected()
        End Function

        Protected Function GetUserId() As Integer
            Dim userId As Integer = -1
            If Context.User IsNot Nothing AndAlso Context.User.Identity IsNot Nothing Then
                Integer.TryParse(Context.User.Identity.Name, userId)
            End If
            Return userId
        End Function
    End Class
End Namespace 