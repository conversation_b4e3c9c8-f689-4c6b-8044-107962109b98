<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<configSections>
		<section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
	</configSections>

	<system.net>
		<mailSettings>
			<smtp deliveryMethod="Network" from="<EMAIL>">
				<network host="127.0.0.1" port="25" />
			</smtp>
		</mailSettings>
	</system.net>

	<system.web>
		<customErrors mode="Off"/>
		<compilation debug="true" strict="false" explicit="true" targetFramework="4.7.2"/>
		<httpRuntime targetFramework="4.7.2"/>
		<authentication mode="Forms">
			<forms loginUrl="Login.aspx"
				   defaultUrl="Default.aspx"
				   timeout="30"
				   requireSSL="false"
				   slidingExpiration="true"
				   path="/"
				   protection="All">
			</forms>
		</authentication>
		<authorization>
			<deny users="?" />
			<allow users="*" />
		</authorization>
	</system.web>

	<appSettings>
		<add key="owin:AutomaticAppStartup" value="true"/>
		<add key="owin:AppStartup" value="PvAChess.Startup"/>
		<add key="ValidationSettings:UnobtrusiveValidationMode" value="None" />
		<!-- USE THIS TO DETERMINE WHICH EMAILER TO USE - WHEN DEBUGGING USE LOCAL -->

		<add key="WhichEmailSystem" value="Local" />

		<add key="SystemEmailAddress" value="<EMAIL>" />
		<add key="EnableGameLogging" value="true"/>
	</appSettings>

	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<!-- Keep SignalR and related dependencies -->
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Owin" culture="neutral" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Owin.Security" culture="neutral" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.AspNet.SignalR.Core" culture="neutral" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed" />
				<bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.AspNet.Cors" culture="neutral" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="0.0.0.0-5.0.0.0" newVersion="5.0.0.0" />
			</dependentAssembly>
			<!-- Add new System.Buffers binding -->
			<dependentAssembly>
				<assemblyIdentity name="System.Buffers" culture="neutral" publicKeyToken="cc7b13ffcd2ddd51" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>

	<connectionStrings>
		<add name="ChessGameConnection"
			 connectionString="Data Source=173.214.174.114;Initial Catalog=ElthosChess;Persist Security Info=True;User ID=ElthosChessAdmin;Password='O,mO#ZqoPU@|QuYgbA0W1.R`aV`g8G=R'"
			 providerName="System.Data.SqlClient" />
	</connectionStrings>

	<system.webServer>
		<security>
			<requestFiltering>
				<requestLimits maxAllowedContentLength="**********" />
			</requestFiltering>
		</security>
		<modules runAllManagedModulesForAllRequests="true">
			<remove name="WebDAVModule" />
		</modules>
		<handlers>
			<remove name="WebDAV" />
			<remove name="ExtensionlessUrlHandler-Integrated-4.0" />
			<add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
			<add name="LoggingHandler" verb="*" path="LoggingHandler.ashx"
				 type="PvAChess.LoggingHandler" />
		</handlers>
		<validation validateIntegratedModeConfiguration="false" />
		<rewrite>
			<rules>
				<!-- Rule to allow Let's Encrypt validation -->
				<rule name="LetsEncrypt" stopProcessing="true">
					<match url="^\.well-known/acme-challenge/.*" />
					<action type="None" />
				</rule>
			</rules>
		</rewrite>
	</system.webServer>

	<location path="Logs">
		<system.web>
			<authorization>
				<allow users="*"/>
			</authorization>
		</system.web>
	</location>

	<location path="Styles">
		<system.web>
			<authorization>
				<allow users="*"/>
			</authorization>
		</system.web>
	</location>

	<location path="images">
		<system.web>
			<customErrors mode="Off"/>
			<authorization>
				<allow users="*"/>
			</authorization>
		</system.web>
	</location>

	<location path="Register.aspx">
		<system.web>
			<authorization>
				<allow users="?"/>
			</authorization>
		</system.web>
	</location>

	<location path="well-known">
		<system.web>
			<authorization>
				<allow users="*"/>
			</authorization>
		</system.web>
	</location>

</configuration>
