Option Explicit On
Option Strict On

Partial Public Class Login

    Protected WithEvents form1 As System.Web.UI.HtmlControls.HtmlForm

    Protected WithEvents txtUsername As System.Web.UI.WebControls.TextBox

    Protected WithEvents txtPassword As System.Web.UI.WebControls.TextBox

    Protected WithEvents btnLogin As System.Web.UI.WebControls.Button
 
    Protected WithEvents lblError As Global.System.Web.UI.WebControls.Label

    Protected WithEvents UpdatePanel1 As Global.System.Web.UI.UpdatePanel

    Protected WithEvents txtResetEmail As Global.System.Web.UI.WebControls.TextBox

    Protected WithEvents btnRequestReset As Global.System.Web.UI.WebControls.Button

    Protected WithEvents lblResetMessage As Global.System.Web.UI.WebControls.Label

    ' Add new controls for password reset
    Protected WithEvents UpdatePanel2 As Global.System.Web.UI.UpdatePanel

    Protected WithEvents txtNewPassword As Global.System.Web.UI.WebControls.TextBox

    Protected WithEvents txtConfirmPassword As Global.System.Web.UI.WebControls.TextBox

    Protected WithEvents btnSetNewPassword As Global.System.Web.UI.WebControls.Button

    Protected WithEvents lblResetPasswordMessage As Global.System.Web.UI.WebControls.Label

    Protected WithEvents hdnResetToken As Global.System.Web.UI.WebControls.HiddenField

End Class