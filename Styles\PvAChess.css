/* Consolidated CSS File */

#currentGameId {
    background-color: rgb(255, 255, 255);
    border: 1px solid rgb(255, 217, 0);
    border-radius: 4px;
    color: black;
    display: inline-block;
    font-family: 'Courier New', Courier, monospace;
    font-size: 18px;
    font-weight: bold;
    padding: 5px 10px;
}

#gameIdDisplay {
    margin-bottom: 10px;
}

    #gameIdDisplay strong {
        font-size: 16px;
        margin-right: 10px;
    }

#playerRole {
    color: #2196F3;
    font-size: 24px;
    font-weight: bold;
    margin: 10px 0;
    padding: 10px 0;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

#turn-message {
    color: #333;
    font-family: 'Palatino Linotype';
    font-size: 1.1em;
    white-space: normal;
}


.UserDisplay {
    background-color: rgb(247 247 247 / 80%);
    border-radius: 4px;
    color: #003971;
    padding: 8px 12px;
    text-decoration: none;
}

.black {
    background-color: #b58863;
}

.black-piece {
    color: #000000;
    text-shadow: -1px -1px 0 #FFF, 1px -1px 0 #FFF, -1px 1px 0 #FFF, 1px 1px 0 #FFF, 4px 4px 4px rgba(0, 0, 0, 0.8);
}

    .black-piece.promoted-pawn::after {
        border: 2px solid #770000;
        box-shadow: inset 0 0 16px #df0000da;
    }

    .black-piece.radicalized-pawn {
        text-shadow: -1px -1px 0 #FFF, 1px -1px 0 #d10e0e, -1px 1px 0 #991515, 1px 1px 0 #501c8a, 4px 4px 4px rgb(69 36 47 / 5%), 3px 3px 3px maroon;
    }

.board {
    border: 2px solid #333;
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    margin: 40px 0;
    position: relative;
    width: fit-content;
}

    .board::after {
        background: rgb(255, 255, 255);
        bottom: -20px;
        height: 20px;
        left: 0;
        right: 0;
        z-index: -1;
    }

    .board::before {
        background-color: black;
        border: 2px solid #333;
        border-bottom: none;
        top: -20px;
    }

    .board::before,
    .board::after {
        border: 2px solid #333;
        box-sizing: border-box;
        content: '';
        height: 20px;
        left: -2px;
        pointer-events: none;
        position: absolute;
        right: -2px;
    }

.captured-piece {
    align-items: center;
    display: flex;
    font-size: 36px;
    height: 30px;
    justify-content: center;
    width: 30px;
}

.chat-header {
    background: rgba(0, 0, 0, 0.7);
    border-bottom: 1px solid #444;
    padding: 10px;
}

    .chat-header h3 {
        border: none;
        color: #fff;
        font-family: 'Cinzel', serif;
        font-size: 16px;
        letter-spacing: 0.05em;
        margin: 0;
        padding: 12px;
    }

.chat-input-area {
    background: rgba(0, 0, 0, 0.7);
    border-top: 1px solid #444;
    display: flex;
    gap: 8px;
    padding: 10px;
}

    .chat-input-area button {
        background: #2b5278;
        border: none;
        border-radius: 4px;
        color: #fff;
        cursor: pointer;
        padding: 8px 16px;
    }

        .chat-input-area button:hover {
            background: #366391;
        }

    .chat-input-area input {
        background: #222;
        border: 1px solid #444;
        border-radius: 4px;
        color: #fff;
        flex: 1;
        padding: 8px;
    }

.chat-interface {
    background: rgba(0, 0, 0, 0.85);
    border-left: 1px solid #444;
    display: flex !important;
    flex-direction: column;
    height: 80vh;
    min-height: 400px;
    position: fixed;
    right: -300px;
    top: 50%;
    transform: translateY(-50%);
    transition: all 0.3s ease-in-out;
    visibility: visible !important;
    width: 280px;
    z-index: 2000;
}

    .chat-interface * {
        border: 1px solid red;
    }

    .chat-interface.hidden {
        display: flex !important;
        opacity: 0;
        right: -300px;
    }

    .chat-interface.visible {
        opacity: 1;
        pointer-events: auto;
        right: 0;
    }

.chat-message {
    border-radius: 4px;
    max-width: 85%;
    padding: 8px;
    word-wrap: break-word;
}

    .chat-message.received {
        align-self: flex-start;
        background: #3a3a3a;
        color: #fff;
    }

    .chat-message.sent {
        align-self: flex-end;
        background: #2b5278;
        color: #fff;
    }

.chat-messages {
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 8px;
    min-height: 200px;
    overflow-y: auto;
    padding: 10px;
}

.container {
    background-color: white;
    display: flex;
    margin: 220px auto 20px auto;
    max-width: 1286px;
    padding: 20px;
    position: relative;
    top: 180px;
    transition: none !important;
    z-index: 1;
}

.controls {
    margin-top: 20px;
}

.focus-mode .board {
    margin: 10px 0;
    position: relative;
    z-index: 2;
}

.focus-mode .container {
    background-color: transparent;
    margin-bottom: 100px;
    margin-right: 300px;
    max-width: calc(100% - 300px);
    position: relative;
    transition: all 0.3s ease-in-out;
}

.focus-mode .game-section {
    background: white;
    border-radius: 8px;
    left: 0;
    margin: 0 auto;
    padding: 30px;
    position: fixed;
    right: 0;
    top: 60px;
    transition: none !important;
    width: fit-content;
}

    .focus-mode .game-section > *:not(.game-title):not(.turn-indicator):not(.turn-status):not(.board):not(.prison-container) {
        display: none;
    }

.focus-mode .game-title {
    background: white;
    margin: -10px 0 0 -30px;
    margin-bottom: 0;
    padding: 5px;
    position: absolute;
    text-align: center;
    top: -30px;
    width: 98%;
}

.focus-mode .info-section {
    display: none;
    opacity: 0;
    position: absolute;
}

.focus-mode .prison-container {
    background: darkgrey;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    gap: 20px;
    left: 20px;
    padding: 10px;
    position: fixed;
    top: 50px;
    transition: none !important;
    width: auto;
    z-index: 1;
}

.focus-mode .turn-indicator,
.focus-mode .turn-status {
    background: white;
    margin: 5px 0;
    padding: 5px;
    text-align: center;
}

.focus-mode .turn-indicator, .focus-mode .turn-status {
    background: white;
    margin: 5px 0;
    margin-bottom: 20px;
    padding: 5px;
    text-align: center;
}

.focus-mode-btn {
    background-color: #4a90e2;
    border: none;
    border-radius: 4px;
    color: white;
    cursor: pointer;
    display: block;
    font-size: 14px;
    margin-left: 10px;
    padding: 8px 16px;
    transition: background-color 0.3s;
}

    .focus-mode-btn:hover {
        background-color: #357abd;
    }

.game-info,
.game-section,
.prison-container {
    transition: all 0.5s ease-in-out;
}

.game-section {
    flex: 0 0 auto;
    padding: 20px;
}

.game-title {
    background: white;
    border: 1px solid gray;
    border-radius: 12px;
    box-shadow: 3px 3px 4px gray;
    color: #2c1810;
    font-family: 'Cinzel', serif;
    font-size: 2em;
    letter-spacing: 0.05em;
    margin-bottom: 30px;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.header-controls {
    align-items: center;
    display: flex;
    gap: 15px;
    position: fixed;
    right: 10px;
    top: 10px;
    z-index: 1000;
}

.hidden {
    display: none !important;
}

.info-section {
    background-color: #f5f5f5;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    flex: 1;
    padding: 20px;
}

    .info-section h3 {
        color: #333;
        margin-bottom: 15px;
        margin-top: 0;
    }

    .info-section p {
        line-height: 1.6;
        margin-bottom: 15px;
    }

.Instructions {
    border: 1px solid gray;
    border-radius: 12px;
    padding: 12px;
    position: relative;
    height: 480px;
    overflow-y: auto;
    background-color: lightgray;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.join-game-section {
    float: right;
}

    .join-game-section button {
        background-color: #4CAF50;
        border: none;
        border-radius: 4px;
        color: white;
        cursor: pointer;
        font-size: 14px;
        padding: 8px 16px;
        transition: background-color 0.3s;
        width: fit-content;
    }

.lnkbtn {
    background-color: rgb(22 90 120 / 80%);
    border-radius: 4px;
    color: #ffffff;
    padding: 8px 12px;
    text-decoration: none;
    transition: all 0.3s ease;
}

    .lnkbtn:active {
        background-color: rgba(0, 0, 0, 1);
        color: #87cefa;
    }

    .lnkbtn:hover {
        background-color: rgba(0, 0, 0, 0.9);
        color: #00bfff;
        text-decoration: none;
    }

a.lnkbtn.aspNetDisabled {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
    pointer-events: none !important;
    text-decoration: none !important;
}

.page-wrapper {
    align-items: center;
    display: flex;
    height: 100vh;
    justify-content: center;
}

.prison {
    background-color: gainsboro;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: inset 0 0 16px;
    display: grid;
    grid-template-columns: repeat(8, 30px);
    gap: 8px;
    align-content: start;
    min-height: 120px;
    padding: 10px;
    overflow: auto;
}


.prison-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 20px;
    transition: all 0.5s ease-in-out;
    width: 100%;
}

    .prison-container > div {
        background-color: black;
        border-radius: 5px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 10px;
    }

.prison-label {
    font-weight: bold;
    margin-bottom: 5px;
    background-color: black;
    color: white;
}

.promoted-pawn {
    font-weight: bold;
    position: relative;
    text-shadow: none;
}

    .promoted-pawn::after {
        border-radius: 50%;
        content: '';
        height: 40px;
        margin: calc((100% - 40px) / 2);
        pointer-events: none;
        position: absolute;
        transform: none;
        width: 40px;
        z-index: -1;
    }

.resign-btn {
    background-color: #dc3545 !important;
    border: none;
    border-radius: 4px;
    color: white !important;
    cursor: pointer;
    font-size: 14px;
    padding: 8px 16px;
    transition: background-color 0.3s;
}

    .resign-btn:hover {
        background-color: #c82333 !important;
    }

.rules-list {
    padding-left: 20px;
}

    .rules-list > li {
        margin: 20px;
        margin-bottom: 20px;
    }

        .rules-list > li > ul {
            margin-top: 20px;
        }

            .rules-list > li > ul > li > ul {
                margin-top: 12px;
            }

                .rules-list > li > ul > li > ul > li > ul {
                    margin-top: 12px;
                }

            .rules-list > li > ul > li:first-child {
                margin-top: 12px;
            }

    .rules-list li {
        margin-bottom: 10px;
    }

        .rules-list li li {
            margin-bottom: 10px;
        }

.selected {
    position: relative;
}

    .selected.promoted-pawn::after {
        left: 0;
        top: 0;
        transform: none;
    }

    .selected::after {
        background-color: rgba(127, 179, 213, 0.4);
        bottom: 0;
        box-shadow: inset 0 0 15px yellowgreen, inset 0 0 15px yellowgreen, inset 0 0 15px yellowgreen, inset 0 0 15px yellowgreen;
        content: '';
        left: 0;
        pointer-events: none;
        position: absolute;
        right: 0;
        top: 0;
        z-index: -1;
    }

.square {
    align-items: center;
    border: 1px solid #585858;
    color: #000000;
    cursor: pointer;
    display: flex;
    font-size: 48px;
    justify-content: center;
    position: relative;
    z-index: 1;
    height: 60px;
    width: 60px;
}

    .square span,
    .square div {
        position: relative;
        z-index: 2;
    }

.strategy-guide {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 0 20px rgba(0,0,0,0.3);
    display: none;
    left: 50%;
    max-height: 90vh;
    max-width: 800px;
    opacity: 0;
    overflow-y: auto;
    padding: 20px;
    position: fixed;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    z-index: 1001;
}

    .strategy-guide .close-btn {
        background: none;
        border: none;
        color: #666;
        cursor: pointer;
        font-size: 24px;
        padding: 5px 10px;
        position: absolute;
        right: 0;
        top: 0;
    }

        .strategy-guide .close-btn:hover {
            color: #000;
        }

    .strategy-guide h2 {
        color: #2c1810;
        font-family: 'Cinzel', serif;
        margin-bottom: 20px;
    }

    .strategy-guide h3 {
        color: #45a049;
        font-family: 'Crimson Text', serif;
        margin-top: 20px;
    }

    .strategy-guide li {
        line-height: 1.4;
        margin-bottom: 10px;
    }

    .strategy-guide ul {
        padding-left: 20px;
    }

.strategy-guide-btn {
    background-color: #4CAF50;
    border: none;
    border-radius: 50%;
    box-shadow: 2px 2px 5px rgba(0,0,0,0.2);
    color: white;
    cursor: pointer;
    font-size: 20px;
    height: 30px;
    transition: all 0.3s ease;
    width: 30px;
}

    .strategy-guide-btn:hover {
        background-color: #45a049;
        transform: scale(1.1);
    }

.strategy-guide-content {
    position: relative;
}

.team-selection {
    align-items: center;
    margin-bottom: 20px;
}

    .team-selection .radio-options {
        align-items: center;
        border: 1px solid gray;
        border-radius: 12px;
        box-shadow: 2px 2px 3px gray;
        display: flex;
        gap: 15px;
        padding: 12px;
    }

    .team-selection button {
        background-color: #4CAF50;
        border: 1px solid white;
        border-radius: 4px;
        box-shadow: 2px 2px 3px darkgrey;
        color: white;
        cursor: pointer;
        flex-shrink: 0;
        font-size: 18px;
        padding: 12px 20px;
        transition: background-color 0.3s;
        width: fit-content;
    }

        .team-selection button:hover {
            background-color: #45a049;
        }

.turn-indicator {
    font-size: 1.5em;
    font-weight: bold;
    margin: 10px;
}

.turn-light {
    background-color: #ccc;
    border-radius: 50%;
    height: 12px;
    transition: background-color 0.3s ease;
    width: 12px;
}

    .turn-light.active {
        background-color: #4CAF50;
        box-shadow: 0 0 5px #4CAF50;
    }

    .turn-light.inactive {
        background-color: #f44336;
        box-shadow: 0 0 5px #f44336;
    }

    .turn-light.waiting {
        background-color: #ccc;
        border-radius: 50%;
        height: 12px;
        transition: background-color 0.3s ease;
        width: 12px;
    }

.turn-status {
    align-items: center;
    display: flex;
    gap: 10px;
    margin: 10px 0;
}

.victory-message {
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    box-shadow: 0 0 20px rgba(0,0,0,0.3);
    font-size: 24px;
    left: 50%;
    padding: 20px 40px;
    position: fixed;
    text-align: center;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
}

    .victory-message button {
        background-color: #3498db;
        border: none;
        border-radius: 4px;
        color: white;
        cursor: pointer;
        font-size: 16px;
        padding: 10px 20px;
        transition: background-color 0.3s;
    }

        .victory-message button:hover {
            background-color: #2980b9;
        }

    .victory-message h2 {
        color: #2c3e50;
        margin-bottom: 15px;
    }

    .victory-message p {
        color: #34495e;
        font-size: 16px;
        margin-bottom: 20px;
    }

.victory-mode-container {
    align-items: center;
    border: 1px solid gray;
    border-radius: 12px;
    box-shadow: 2px 2px 3px gray;
    display: flex;
    gap: 15px;
    padding: 12px;
}

    .victory-mode-container .victory-mode-selection {
        margin: 6px;
        display: inline;
        font-weight: bold;
        padding: 6px;
    }

    .victory-mode-container .DropDownList {
        padding: 4px;
        font-size: 16px;
        border: 1px solid lightblue;
        border-radius: 12px;
        box-shadow: 2px 2px 3px gray;
    }

.white {
    background-color: #f0d9b5;
}

.white-piece {
    color: #FFFFFF;
    text-shadow: -1px -1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000, 1px 1px 0 #000, 4px 4px 4px rgba(0, 0, 0, 0.8);
}

.white-piece-radicalized-pawn-text {
    color: #380404;
    text-shadow: none;
    transition: all 0.5s ease-in-out;
}

    .white-piece-radicalized-pawn-text:hover {
        color: black;
        text-shadow: 0 0 0 #94ff00, 0 0 0 #d60000e6, 0 0 0 #a500ad5e, 0 0 0 #501c8a29, 0 0 4px rgb(69 36 47 / 5%), 0 0 3px maroon;
    }

.white-piece.promoted-pawn::after {
    border: 2px solid #72bd00;
    box-shadow: inset 0 0 16px #abff00;
    text-shadow: none;
}

.white-piece.radicalized-pawn {
    text-shadow: -1px -1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000, 1px 1px 0 #000, 4px 4px 4px rgb(19 255 0), 3px 3px 3px green;
}

body {
    background-color: rgba(0, 0, 0, 0.95);
    background-image: url('../images/Cover1.jpeg');
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    font-family: Arial, sans-serif;
    margin: 0;
    min-height: 100vh;
    padding: 0;
    position: relative;
}

p.game-description {
    background-color: #f0f0f0;
    border: 1px solid #000000;
    border-radius: 12px;
    color: #2c1810;
    font-family: 'Crimson Text','Cormorant Garamond','Cinzel', 'Franklin Gothic Medium', 'Arial Narrow', Arial, sans-serif;
    font-size: large;
    font-weight: 600;
    line-height: 1.6;
    margin-bottom: 20px;
    padding: 10px;
    text-align: justify;
}


@media only screen and (max-width: 740px) {

    .prison-container {
        /* place holder */
    }
}

@media screen and (max-width: 1024px) {

    .prison-container {
    }

    .focus-mode .chat-interface {
        position: relative !important;
        top: auto !important;
        right: auto !important;
        transform: none !important;
        width: 100% !important;
        height: 200px !important;
        margin: 30px 0 0 0 !important;
    }
}

@media (max-width: 740px) {

    .prison-container {
    }
}

@media screen and (max-width: 740px) and (orientation: portrait) {

    .prison-container {
    }
}

@media screen and (max-width: 360px) {
    .focus-mode .prison {
        grid-template-columns: repeat(8, 25px) !important;
        gap: 2px !important;
        max-width: 200px !important;
    }
}

@media screen and (max-width: 1024px) {

    .container {
        background-color: whitesmoke;
        margin: 0 auto !important;
        padding: 10px !important;
        position: relative !important;
        margin-top: 0px !important;
        top: 25px;
    }

    .focus-mode .game-title {
        background: white;
        margin: -30px 0 0 -10px;
        margin-bottom: 0;
        padding: 5px;
        position: absolute;
        text-align: center;
        top: -30px;
    }

    .focus-mode .game-section {
        position: relative !important;
        width: 100% !important;
        padding: 10px !important;
    }

    .focus-mode .board {
        position: static !important;
        margin: 0 auto !important;
        transform: scale(0.9) !important;
        transform-origin: top center !important;
        width: fit-content;
    }

    .focus-mode .prison-container {
        position: static !important;
        width: 75% !important;
        margin: 30px auto 0 !important;
        padding: 10px !important;
    }

    .focus-mode .prison {
        display: grid !important;
        grid-template-columns: repeat(8, 30px) !important;
        gap: 4px !important;
        min-height: 60px !important;
        max-width: 100% !important;
        margin: 0 auto !important;
        padding: 5px !important;
    }

    .focus-mode .chat-interface {
        background: rgba(0, 0, 0, 0.85);
        border-left: 1px solid #444;
        display: flex !important;
        flex-direction: column;
        height: 80vh;
        min-height: 400px;
        position: fixed;
        right: -300px;
        top: 50%;
        transform: translateY(-50%);
        transition: all 0.3s ease-in-out;
        visibility: visible !important;
        width: 280px;
        z-index: 2000;
    }

    /* Visible state */
    .chat-interface.visible {
        right: 0;
    }

    .square {
        font-size: 42px;
        height: 48px;
        width: 48px;
    }

    .filter-button {
        right: 0%;
        width: 200px;
        margin-bottom: 30px !important;
    }

    .game-selection-panel {
        background-color: rgba(0, 0, 0, 0.7);
        border-radius: 5px;
        color: #fff;
        margin-top: 6px;
        padding: 6px;
    }


    .Instructions {
        height: 700px !important;
    }
}

@media screen and (max-width: 360px) {
    .focus-mode .board {
        transform: scale(0.8) !important;
    }

    .focus-mode .prison {
        grid-template-columns: repeat(8, 25px) !important;
        gap: 2px !important;
    }
}
