﻿<%@ Page Language="vb" AutoEventWireup="false" CodeBehind="ChessGameReplay.aspx.vb" Inherits="PvAChess.ChessGameReplay" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <title>Chess Game Replay - Plebs vs Aristoi</title>
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@600&display=swap" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@400;600&display=swap" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=Crimson+Text:wght@400;600&display=swap" rel="stylesheet" />

    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <link rel="icon" type="image/png" href="/images/Favicon_PA.png" />

    <link href="Styles/PvAChess.css" rel="stylesheet" type="text/css" />
    <link href="Styles/ChessAnimations.css" rel="stylesheet" type="text/css" />
    <link href="Styles/ChessReplay.css" rel="stylesheet" type="text/css" />

    <script src="Scripts/ChessCommon.js"></script>
    <script src="Scripts/jquery-3.7.1.min.js"></script>
    <script src="Scripts/jquery.signalR-2.4.3.min.js"></script>
    <script src="signalr/hubs"></script>

</head>
<body>
    <form id="form1" runat="server">

        <audio id="moveSound" src="sounds/Move.wav" preload="auto"></audio>
        <audio id="captureSound" src="sounds/tada.wav" preload="auto"></audio>
        <audio id="swapSound" src="sounds/Swap.wav" preload="auto"></audio>

        <asp:ScriptManager ID="ScriptManager1" runat="server"></asp:ScriptManager>
        <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">

            <Triggers>
                <asp:AsyncPostBackTrigger ControlID="chkAutoPlay" EventName="CheckedChanged" />
                <asp:AsyncPostBackTrigger ControlID="ddlPlaySpeed" EventName="SelectedIndexChanged" />
                <asp:AsyncPostBackTrigger ControlID="rblGameType" EventName="SelectedIndexChanged" />
            </Triggers>
            <ContentTemplate>

                <div class="header-controls">
                    <asp:Label ID="lblUserDisplay" runat="server" CssClass="UserDisplay" Text=""></asp:Label>
                    <asp:LinkButton ID="btnPlayGame" runat="server" CssClass="lnkbtn" Text="Play Game"></asp:LinkButton>
                    <asp:LinkButton ID="btnLeaderBoard" runat="server" CssClass="lnkbtn" Text="Leader Board"></asp:LinkButton>
                    <asp:LinkButton ID="btnLogout" runat="server" CssClass="lnkbtn" Text="Logout"></asp:LinkButton>
                </div>

                <asp:Label ID="lblError" runat="server" CssClass="error"></asp:Label>
                <div class="main-container">
                    <div class="left-column">
                        <div class="game-selection-panel">
                            <h2>Select Game to Replay</h2>
                            <div class="filter-controls">
                                <div class="game-type-filter">
                                    <asp:Label ID="lblGameType" runat="server" Text="Game Type:" AssociatedControlID="rblGameType" />
                                    <asp:RadioButtonList ID="rblGameType" runat="server" RepeatDirection="Vertical" CssClass="radio-group">
                                        <asp:ListItem Text="Multiplayer Games" Value="0" Selected="True" />
                                        <asp:ListItem Text="Solo Games" Value="1" />
                                    </asp:RadioButtonList>
                                </div>
                                <div class="player-filter">
                                    <asp:Label ID="lblPlayer" runat="server" Text="Player:" AssociatedControlID="ddlPlayer" />
                                    <br>
                                    <asp:DropDownList ID="ddlPlayer" runat="server" />
                                </div>
                                <div class="date-range">
                                    <asp:Label ID="lblDateRange" runat="server" Text="Date Range:" AssociatedControlID="txtStartDate" />
                                    <asp:TextBox ID="txtStartDate" runat="server" TextMode="Date" />
                                    <asp:Label ID="lblTo" runat="server" Text="to" />
                                    <asp:TextBox ID="txtEndDate" runat="server" TextMode="Date" />
                                </div>
                                <asp:Button ID="btnFilter" runat="server" Text="Filter Games" CssClass="filter-button" />
                            </div>
                            <div style="max-height: 540px; overflow: auto;">
                                <asp:GridView ID="gvGames" runat="server" AutoGenerateColumns="False"
                                    CssClass="games-grid" OnSelectedIndexChanged="gvGames_SelectedIndexChanged">
                                    <Columns>
                                        <asp:BoundField DataField="gameId" HeaderText="GameID" />
                                        <asp:BoundField DataField="StartTime" HeaderText="Start Time" />
                                        <asp:BoundField DataField="Plebs" HeaderText="Plebs" />
                                        <asp:BoundField DataField="Aristoi" HeaderText="Aristoi" />
                                        <asp:BoundField DataField="EndGameAction" HeaderText="Results" />
                                        <asp:TemplateField>
                                            <ItemTemplate>
                                                <asp:LinkButton ID="lnkReplay" runat="server" Text="Replay"
                                                    CommandName="Select" CssClass="replay-link" />
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                    </Columns>
                                </asp:GridView>
                            </div>
                        </div>
                    </div>

                    <div class="center-column">
                        <div class="game-replay" id="replaySection" runat="server" visible="false">
                            <div id="board" runat="server" class="board"></div>

                            <div class="replay-controls">
                                <div class="navigation-controls">
                                    <asp:Button ID="btnFirst" runat="server" Text="⏮" CssClass="nav-button"
                                        ToolTip="First Move" OnClientClick="handleFirstMove(); return false;" />
                                    <asp:Button ID="btnPrevious" runat="server" Text="⏪" CssClass="nav-button"
                                        ToolTip="Previous Move" OnClientClick="handlePreviousMove(); return false;" />
                                    <asp:Button ID="btnNext" runat="server" Text="⏩" CssClass="nav-button"
                                        ToolTip="Next Move" OnClientClick="handleNextMove(); return false;" />
                                    <asp:Button ID="btnLast" runat="server" Text="⏭" CssClass="nav-button"
                                        ToolTip="Last Move" OnClientClick="handleLastMove(); return false;" />
                                </div>

                                <div class="auto-play-controls">
                                    <asp:CheckBox ID="chkAutoPlay" runat="server" Text="Auto Play"
                                        AutoPostBack="true"
                                        OnCheckedChanged="chkAutoPlay_CheckedChanged"
                                        CausesValidation="false" />
                                    <asp:DropDownList ID="ddlPlaySpeed" runat="server" AutoPostBack="true">
                                        <asp:ListItem Value="2000" Text="0.5x Speed"></asp:ListItem>
                                        <asp:ListItem Value="1000" Text="1x Speed" Selected="True"></asp:ListItem>
                                        <asp:ListItem Value="500" Text="2x Speed"></asp:ListItem>
                                        <asp:ListItem Value="250" Text="4x Speed"></asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="right-column">
                        <div class="game-info">
                            <asp:Label ID="lblGameInfo" runat="server" CssClass="game-info-text"></asp:Label>
                            <asp:Label ID="lblCurrentMove" runat="server" CssClass="current-move-text"></asp:Label>
                        </div>
                    </div>
                </div>

                <asp:HiddenField ID="hdnBoardState" runat="server" />
                <asp:HiddenField ID="hdnInitialGameData" runat="server" />
                <asp:HiddenField ID="hdnInitialMoveIndex" runat="server" />
                <asp:HiddenField ID="hdnUserId" runat="server" />

                <div id="errorDisplay" class="error-message" style="display: none;"></div>

                <asp:Timer ID="_autoPlayTimer" runat="server" Interval="1000" Enabled="false" />
            </ContentTemplate>
        </asp:UpdatePanel>

        <div id="connection-message" style="display: none; position: fixed; top: 10px; left: 10%; transform: translateX(-50%); background-color: rgba(0, 0, 0, 0.8); color: white; padding: 10px 20px; border-radius: 5px; z-index: 9999;"></div>


        <script type="text/javascript">

            // Global variables
            let pawnStates = new Map(); // Key: "row,col", Value: PAWN_STATES


            function executeReplayMove(fromRow, fromCol, toRow, toCol, isCapture) {

                console.log('ChessGameReplay.aspx - INSIDE handleNextMove()');
                console.log('ChessGameReplay.aspx - handleNextMove() - fromRow, fromCol, toRow, toCol, isCapture:', fromRow, fromCol, toRow, toCol, isCapture);
                console.log('ChessGameReplay.aspx - board:', board);

                const sourceSquare = document.querySelector(`.square[data-row="${fromRow}"][data-col="${fromCol}"]`);
                const targetPiece = sourceSquare ? sourceSquare.querySelector('.piece') : null;
                const destSquare = document.querySelector(`.square[data-row="${toRow}"][data-col="${toCol}"]`);

                if (destSquare) {
                    // Remove any existing animation classes
                    // destSquare.classList.remove('move-highlight', 'capture-highlight');

                    // Force a reflow to ensure animation plays again
                    void destSquare.offsetWidth;

                    // Check if this is a pawn swap
                    const sourceIsPawn = sourceSquare?.querySelector('.piece')?.classList.contains('pawn');
                    const destIsPawn = destSquare?.querySelector('.piece')?.classList.contains('pawn');

                    console.log('playMoveEffects call - ' + targetPiece + ' ' + destSquare)

                    // Add appropriate highlight class 
                    playMoveEffects(targetPiece, destSquare);

                }
            }

            function updateBoardState(fromRow, fromCol, toRow, toCol) {
                // Update the board state
                window.boardState[toRow][toCol] = window.boardState[fromRow][fromCol];
                window.boardState[fromRow][fromCol] = null;

                // Re-render the board
                renderBoard();
            }

            function updateMoveDisplay(moveText) {
                const moveDisplay = document.getElementById('lblCurrentMove');
                if (moveDisplay) {
                    moveDisplay.innerHTML = moveText;
                }
            }

            function handleNextMove() {
                console.log('handleNextMove called');
                console.log('gameData:', gameData);
                console.log('gameData.Moves:', gameData.Moves);
                console.log('currentMoveIndex:', currentMoveIndex);

                if (!gameData || !gameData.Moves || currentMoveIndex >= gameData.Moves.length - 1) {
                    return;
                }

                currentMoveIndex++;
                const move = gameData.Moves[currentMoveIndex];
                console.log('Next move:', move);

                // Update the board state
                const fromSquare = document.querySelector(`.square[data-row="${move.FromRow}"][data-col="${move.FromCol}"]`);
                const toSquare = document.querySelector(`.square[data-row="${move.ToRow}"][data-col="${move.ToCol}"]`);

                displayCurrentPosition();

                document.getElementById('<%= hdnInitialMoveIndex.ClientID %>').value = currentMoveIndex;
            }

            function handlePreviousMove() {
                if (window.currentMoveIndex > -1) {
                    window.currentMoveIndex--;
                    displayCurrentPosition();
                }
            }

            function handleFirstMove() {
                window.currentMoveIndex = -1;
                displayCurrentPosition();
            }

            function handleLastMove() {
                if (window.gameData && window.gameData.Moves) {
                    window.currentMoveIndex = window.gameData.Moves.length - 1;
                    displayCurrentPosition();
                }
            }

            function displayCurrentPosition() {
                console.log('ChessGameReplay.aspx - INSIDE displayCurrentPosition()');

                if (!window.gameData) return;

                // Initialize the board to starting position
                let board = initializeStartingPosition();

                // Clear existing pawn states
                pawnStates.clear();

                // Apply moves up to current index
                if (window.currentMoveIndex >= 0) {
                    for (let i = 0; i <= window.currentMoveIndex; i++) {
                        const move = window.gameData.Moves[i];
                        console.log('ChessGameReplay.aspx - INSIDE displayCurrentPosition() - window.currentMoveIndex: ' + window.currentMoveIndex);

                        // Check if this is a pawn move
                        const sourceIsPawn = board[move.FromRow][move.FromCol]?.endsWith('pawn');
                        console.log('ChessGameReplay.aspx - INSIDE displayCurrentPosition() - sourceIsPawn: ' + sourceIsPawn);

                        if (sourceIsPawn) {
                            // Get current state first
                            const currentState = pawnStates.get(`${move.FromRow},${move.FromCol}`);
                            console.log('ChessGameReplay.aspx - INSIDE displayCurrentPosition() - currentState: ' + currentState);

                            // If already super, stay super
                            if (currentState === PAWN_STATES.SUPER) {
                                pawnStates.set(`${move.ToRow},${move.ToCol}`, PAWN_STATES.SUPER);
                                console.log('displayCurrentPosition PAWN_STATES.SUPER');

                            } else {
                                // Check conditions for becoming super
                                const isHomeRow = (move.ToRow === 0 || move.ToRow === 7);
                                const capturedPiece = move.PieceCaptured;

                                if (isHomeRow || (capturedPiece && (capturedPiece.toLowerCase().includes('king') ||
                                    capturedPiece.toLowerCase().includes('queen')))) {
                                    pawnStates.set(`${move.ToRow},${move.ToCol}`, PAWN_STATES.SUPER);
                                    console.log('displayCurrentPosition PAWN_STATES.SUPER');
                                } else {
                                    // Become/stay radical
                                    pawnStates.set(`${move.ToRow},${move.ToCol}`, PAWN_STATES.RADICAL);
                                    console.log('displayCurrentPosition PAWN_STATES.RADICAL');
                                }
                            }

                            // Clean up old position
                            pawnStates.delete(`${move.FromRow},${move.FromCol}`);
                            console.log('displayCurrentPosition pawnStates.delete');
                        }

                        // Update board state for this move
                        board[move.ToRow][move.ToCol] = board[move.FromRow][move.FromCol];
                        board[move.FromRow][move.FromCol] = null;
                    }
                    console.log('displayCurrentPosition End Current Index Loop');

                }

                // Set the global board variable
                window.board = board;

                console.log('ChessGameReplay.aspx - INSIDE displayCurrentPosition() - before renderBoard()');

                // Now render the board with all states
                renderBoard();

                console.log('ChessGameReplay.aspx - INSIDE displayCurrentPosition() - after renderBoard()');

                // Animate the current move
                if (window.currentMoveIndex >= 0 && window.currentMoveIndex < window.gameData.Moves.length) {
                    const currentMove = window.gameData.Moves[window.currentMoveIndex];

                    console.log('ChessGameReplay.aspx - INSIDE displayCurrentPosition() - currentMove:', currentMove);

                    executeReplayMove(currentMove.FromRow, currentMove.FromCol,
                        currentMove.ToRow, currentMove.ToCol,
                        currentMove.PieceCaptured !== null);

                }
            }




            function updateMoveInformation(move) {
                const moveInfoElement = document.getElementById('lblCurrentMove');
                if (moveInfoElement) {
                    moveInfoElement.textContent = `Move ${move.MoveNumber}: ${move.PlayerName} (${move.PlayerRole}) moved ${move.PieceMoved} from ${move.FromRow},${move.FromCol} to ${move.ToRow},${move.ToCol}`;
                }
            }

            function initializeBoard() {
                if (window.gameData) {
                    window.boardState = initializeStartingPosition();
                    renderBoard();
                }
            }

            // Initialize game data when page loads
            document.addEventListener('DOMContentLoaded', function () {
                console.log('DOM Content Loaded');
                initializeGameData();
            });

            function initializeGameData() {
                const gameDataElement = document.getElementById('<%= hdnInitialGameData.ClientID %>');
                const moveIndexElement = document.getElementById('<%= hdnInitialMoveIndex.ClientID %>');

                console.log('Move Index Element:', moveIndexElement);
                console.log('Move Index Value:', moveIndexElement ? moveIndexElement.value : 'element not found');

                if (gameDataElement && gameDataElement.value) {
                    try {
                        window.gameData = JSON.parse(gameDataElement.value);
                        window.currentMoveIndex = parseInt(moveIndexElement.value || '0');
                        console.log('Game Data initialized:', window.gameData);
                        console.log('Game Data Moves:', window.gameData.Moves);
                        console.log('Number of moves:', window.gameData.Moves ? window.gameData.Moves.length : 0);
                        console.log('Current Move Index:', window.currentMoveIndex);
                        displayCurrentPosition();
                    } catch (error) {
                        console.error('Error initializing game data:', error);
                    }
                } else {
                    console.log('No game data available');
                }
            }

            // Add UpdatePanel support
            if (typeof (Sys) !== 'undefined' && Sys.WebForms) {
                Sys.WebForms.PageRequestManager.getInstance().add_endRequest(function () {
                    console.log('UpdatePanel refresh completed');
                    const gameDataElement = document.getElementById('<%= hdnInitialGameData.ClientID %>');
                    const moveIndexElement = document.getElementById('<%= hdnInitialMoveIndex.ClientID %>');

                    if (gameDataElement && gameDataElement.value) {
                        try {
                            window.gameData = JSON.parse(gameDataElement.value);
                            window.currentMoveIndex = parseInt(moveIndexElement.value || '-1');
                            console.log('Game Data initialized:', window.gameData);
                            console.log('Game Data Moves:', window.gameData.Moves);
                            console.log('Number of moves:', window.gameData.Moves ? window.gameData.Moves.length : 0);
                            console.log('Current Move Index:', window.currentMoveIndex);
                            displayCurrentPosition();
                        } catch (error) {
                            console.error('Error initializing game data:', error);
                        }
                    }
                });
            }

            function showError(message) {
                const errorDisplay = document.getElementById('errorDisplay');
                if (errorDisplay) {
                    errorDisplay.textContent = message;
                    errorDisplay.style.display = 'block';
                    setTimeout(() => {
                        errorDisplay.style.display = 'none';
                    }, 5000); // Hide after 5 seconds
                }
            }

            // Stub function to match Default.aspx structure
            function updatePrisons() {
                // No prison updates needed in replay mode
            }

            function showReplay() {
                if (window.innerWidth <= 740) {
                    const replaySection = document.getElementById('replaySection');
                    replaySection.classList.add('active');
                }
            }

            // Add this to your existing replay click handler
            document.querySelectorAll('.replay-link').forEach(link => {
                link.addEventListener('click', showReplay);
            });
        </script>
    </form>
</body>
</html>
